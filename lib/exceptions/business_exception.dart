import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/utils/Translations.dart';

class MobileSendNewPasswordException extends WebServiceCallException {
  MobileSendNewPasswordException([String message = ""])
      : super(message, allTranslations.text('error_sending_new_password'));
}



/* Exemple d'utilisation
// Dans le repository:
if (statusResponse.valide != "OK") {
  throw MobileSendNewPasswordException(statusResponse.erreur!);
}

// Dans le bloc:
try {
  await repository.sendNewPassword(...);
} on MobileSendNewPasswordException catch (e) {
  // L'erreur aura le format: "Erreur d'envoi du nouveau mot de passe: [message d'erreur]"
  emit(state.copyWith(error: e.toString()));
} on WebServiceCallException catch (e) {
  // Gestion des autres erreurs de service web
  emit(state.copyWith(error: e.toString()));
}
*/
