import 'dart:convert';
import 'package:theradom/models/consumable.dart';
import 'package:theradom/models/drug.dart';

GetDrugsResponse getDrugsResponseFromJson(String str) =>
    GetDrugsResponse.fromJson(json.decode(str));
String getDrugsResponseToJson(GetDrugsResponse data) =>
    json.encode(data.toJson());

class GetDrugsResponse {
  Map<String, Drug> traitements;
  Map<String, Consumable> produits;
  String valide;
  String? erreur;

  GetDrugsResponse(
      {required this.traitements,
      required this.produits,
      required this.valide,
      this.erreur});

  factory GetDrugsResponse.fromJson(Map<String, dynamic> json) =>
      GetDrugsResponse(
          traitements: Map.from(json["traitements"])
              .map((k, v) => MapEntry<String, Drug>(k, Drug.fromJson(v))),
          produits: Map.from(json["produits"]).map((k, v) =>
              MapEntry<String, Consumable>(k, Consumable.fromJson(v))),
          valide: json["valide"] ?? "NOK",
          erreur: json["erreur"] ?? "");

  Map<String, dynamic> toJson() => {
        "traitements": Map.from(traitements)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "produits": Map.from(produits)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "valide": valide,
        "erreur": erreur
      };
}
