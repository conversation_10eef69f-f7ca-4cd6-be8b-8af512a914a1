import 'package:flutter/material.dart';
import 'package:theradom/models/session_sampling_data_element.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

class SessionSamplingDataPage {
  List<SessionSamplingDataElement> sessionSamplingDataElementList;
  int? numPage;
  DateTime? modifDate;
  DateTime? date;
  TimeOfDay? modifTime;
  TimeOfDay? time;
  String? mode;

  SessionSamplingDataPage(
      {this.sessionSamplingDataElementList = const [],
      required this.numPage,
      this.modifTime,
      this.time,
      this.modifDate,
      this.date,
      this.mode});

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = Map<String, dynamic>();
    sessionSamplingDataElementList.forEach((sessionSamplingDataElement) {
      map[sessionSamplingDataElement.id] = sessionSamplingDataElement.toJson();
    });
    DateTime? dateTime = ((date != null) && (time != null))
        ? DateTime(date!.year, date!.month, date!.day, time!.hour, time!.minute)
        : null;
    int? timestamp = dateTime != null
        ? (dateTime.millisecondsSinceEpoch ~/ 1000).toInt()
        : null; // en secondes
    map["Hrs"] = {"value": timestamp != null ? "$timestamp" : ""};
    map["date_modif"] = {
      "value": modifDate != null ? modifDate!.toYYYYMMDD() : ""
    };
    map["heure_modif"] = {
      "value": modifTime != null ? modifTime!.toLongLanguageString() : ""
    };
    return map;
  }
}
