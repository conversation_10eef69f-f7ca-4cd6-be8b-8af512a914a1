import 'package:flutter/material.dart';

class FieldCalculation extends ChangeNotifier {
  static FieldCalculation? _instance;
  
  factory FieldCalculation() {
    _instance ??= FieldCalculation._internal();
    return _instance!;
  }

  FieldCalculation._internal() {
    // Initialize with default values
    _poidsSec = 0.0;
    _poidsDebut = 0.0;
    _apportsRealises = 0.0;
    _poidsFin = 0.0;
    _poidsApres = 0.0;
  }

  // Clear the singleton instance (useful for testing or resetting)
  static void reset() {
    _instance?.dispose();
    _instance = null;
  }

  double _poidsSec = 0.0;
  double _poidsDebut = 0.0;
  double _apportsRealises = 0.0;
  double _poidsFin = 0.0;
  double _poidsApres = 0.0;

  double get getPoidsSec => _poidsSec;

  double get getPoidsDebut => _poidsDebut;

  double get getApportsRealises => _apportsRealises;

  double get getPoidsFin => _poidsFin;

  double get getPoidsApres => _poidsApres;

  set setPoidsSec(double value) {
    _poidsSec = value;
    notifyListeners();
  }

  set setPoidsDebut(double value) {
    _poidsDebut = value;
    notifyListeners();
  }

  set setApportsRealises(double value) {
    _apportsRealises = value;
    notifyListeners();
  }

  set setPoidsFin(double value) {
    _poidsFin = value;
    notifyListeners();
  }

  set setPoidsApres(double value) {
    _poidsApres = value;
    notifyListeners();
  }
}
