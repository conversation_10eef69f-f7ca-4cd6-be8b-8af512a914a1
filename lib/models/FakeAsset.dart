import 'dart:convert';

FakeAsset fakeAssetFromJson(String str) => FakeAsset.fromJson(json.decode(str));
String fakeAssetToJson(FakeAsset data) => json.encode(data.toJson());

class FakeAsset {
  String identifier;
  String name;
  int width;
  int height;

  FakeAsset(
      {required this.identifier,
      required this.name,
      required this.width,
      required this.height});

  factory FakeAsset.fromJson(Map<String, dynamic> json) => FakeAsset(
      identifier: json["identifier"],
      name: json["name"],
      width: json["width"],
      height: json["height"]);

  Map<String, dynamic> toJson() => {
        "identifier": identifier,
        "name": name,
        "width": width,
        "height": height
      };
}
