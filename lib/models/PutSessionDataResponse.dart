import 'dart:convert';

PutSessionDataResponse putSessionDataResponseFromJson(String str) =>
    PutSessionDataResponse.fromJson(json.decode(str));
String putSessionDataResponseToJson(PutSessionDataResponse data) =>
    json.encode(data.toJson());

class PutSessionDataResponse {
  String valide;
  String? erreur;
  String numSeance;

  PutSessionDataResponse(
      {required this.valide, this.erreur, required this.numSeance});

  factory PutSessionDataResponse.fromJson(Map<String, dynamic> json) =>
      PutSessionDataResponse(
          valide: json["valide"] as String,
          erreur: json["erreur"] as String?,
          numSeance: json["num_seance"]??"");

  Map<String, dynamic> toJson() =>
      {"valide": valide, "erreur": erreur, "num_seance": numSeance};
}
