import 'package:theradom/models/session_sampling_structure.dart';
import 'package:theradom/models/session_unique_structure.dart';

class SessionStructure {
  SessionUniqueStructure unique;
  SessionSamplingStructure box;
  SessionSamplingStructure table;
  bool normalite;
  dynamic alerteImmediate;

  SessionStructure(
      {required this.unique,
      required this.box,
      required this.table,
      required this.normalite,
      this.alerteImmediate});

  factory SessionStructure.fromJson(Map<String, dynamic> json) =>
      SessionStructure(
          unique: SessionUniqueStructure.fromJson(json["unique"]),
          box: SessionSamplingStructure.fromJson(json["box"]),
          table: SessionSamplingStructure.fromJson(json["table"]),
          normalite: json["normalite"],
          alerteImmediate: json["alerteImmediate"]);

  Map<String, dynamic> toJson() => {
        "unique": unique.toJson(),
        "box": box.toJson(),
        "table": table.toJson(),
        "normalite": normalite.toString(),
        "alerteImmediate": alerteImmediate.toString()
      };
}
