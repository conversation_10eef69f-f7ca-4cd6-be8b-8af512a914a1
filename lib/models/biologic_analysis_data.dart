import 'package:theradom/models/biologic_data_content.dart';
import 'package:theradom/extensions/string_extension.dart';

class BiologicAnalysisData {
  Map<String, BiologicDataContent>? donnees;
  String typeBilan;
  DateTime cDate;
  String? pdf;
  String idPdf;
  String dernUser;

  BiologicAnalysisData({
    required this.donnees,
    required this.typeBilan,
    required this.cDate,
    this.pdf,
    required this.idPdf,
    required this.dernUser,
  });

  factory BiologicAnalysisData.fromJson(Map<String, dynamic> json) {
    DateTime date = json["cDATE"].toString().yyyymmdd2DateTime();
    return BiologicAnalysisData(
        donnees: json["donnees"] == null
            ? {}
            : Map.from(json["donnees"]).map((k, v) =>
                MapEntry<String, BiologicDataContent>(
                    k, BiologicDataContent.fromJson(k, v, date))),
        typeBilan: json["TYPE_BILAN"],
        cDate: date,
        pdf: json["pdf"],
        idPdf: json["idPDF"] ?? "",
        dernUser: json["dernUser"] ?? "");
  }

  Map<String, dynamic> toJson() => {
        "donnees": Map.from(donnees!)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "TYPE_BILAN": typeBilan,
        "cDATE": cDate,
        "pdf": pdf == null ? null : pdf,
        "idPDF": idPdf,
        "dernUser": dernUser
      };
}
