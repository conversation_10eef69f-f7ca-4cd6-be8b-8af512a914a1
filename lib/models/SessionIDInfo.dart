import 'dart:convert';

SessionIDInfo sessionIDInfoFromJson(String str) =>
    SessionIDInfo.fromJson(json.decode(str));
String sessionIDInfoToJson(SessionIDInfo data) => json.encode(data.toJson());

class SessionIDInfo {
  String numpat;
  String numSeance;
  String suivi;
  bool past;
  bool locked;

  SessionIDInfo(
      {required this.numpat,
      required this.numSeance,
      required this.suivi,
      required this.past,
      required this.locked});

  factory SessionIDInfo.fromJson(Map<String, dynamic> json) => SessionIDInfo(
      numpat: json["NUMPAT"],
      numSeance: json["num_seance"],
      suivi: json["suivi"],
      past: json["past"],
      locked: json["locked"]);

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "num_seance": numSeance,
        "suivi": suivi,
        "past": past,
        "locked": locked
      };
}
