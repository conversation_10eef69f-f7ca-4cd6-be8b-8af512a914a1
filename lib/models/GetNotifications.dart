import 'dart:convert';
import 'package:theradom/models/num_seance.dart';

GetNotifications getNotificationsFromJson(String str) =>
    GetNotifications.fromJson(json.decode(str));
String getNotificationsToJson(GetNotifications data) =>
    json.encode(data.toJson());

class GetNotifications {
  String numpat;
  NumSeance numSeance;

  GetNotifications({
    required this.numpat,
    required this.numSeance,
  });

  factory GetNotifications.fromJson(Map<String, dynamic> json) =>
      GetNotifications(
        numpat: json["NUMPAT"] == null ? null : json["NUMPAT"],
        numSeance: NumSeance.fromJson(json["num_seance"]),
      );

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "num_seance": numSeance.toJson(),
      };
}
