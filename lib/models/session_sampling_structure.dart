class SessionSamplingStructure {
  Map<String, dynamic> fieldID; //normalement de type FieldDescription

  SessionSamplingStructure({required this.fieldID});

  factory SessionSamplingStructure.fromJson(Map<String, dynamic> json) =>
      SessionSamplingStructure(
          fieldID:
              Map.from(json).map((k, v) => MapEntry<String, dynamic>(k, v)));

  Map<String, dynamic> toJson() => {
        "fieldID":
            Map.from(fieldID).map((k, v) => MapEntry<String, dynamic>(k, v))
      };
}
