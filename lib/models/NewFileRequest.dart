import 'dart:convert';

NewFileRequest newFileRequestFromJson(String str) =>
    NewFileRequest.fromJson(json.decode(str));
String newFileRequestToJson(NewFileRequest data) => json.encode(data.toJson());

class NewFileRequest {
  String numpat;
  String table;
  String numSeanceMail;
  String pj;

  NewFileRequest(
      {required this.numpat,
      required this.table,
      required this.numSeanceMail,
      required this.pj});

  factory NewFileRequest.fromJson(Map<String, dynamic> json) => NewFileRequest(
      numpat: json["NUMPAT"],
      table: json["table"],
      numSeanceMail: json["num_seance_mail"],
      pj: json["pj"]);

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "table": table,
        "num_seance_mail": numSeanceMail,
        "pj": pj
      };
}
