import 'dart:convert';
import 'package:theradom/models/pj.dart';

GetFilesResponse getFilesResponseFromJson(String str) =>
    GetFilesResponse.fromJson(json.decode(str));
String getFilesResponseToJson(GetFilesResponse data) =>
    json.encode(data.toJson());

class GetFilesResponse {
  String valide;
  String? erreur;
  Map<String, Pj> pj;
  String? content;

  GetFilesResponse(
      {required this.valide, this.erreur, required this.pj, this.content});

  factory GetFilesResponse.fromJson(Map<String, dynamic> json) =>
      GetFilesResponse(
          valide: json["valide"],
          erreur: json["erreur"],
          pj: json["PJ"] != null
              ? Map.from(json["PJ"])
                  .map((k, v) => MapEntry<String, Pj>(k, Pj.fromJson(v)))
              : {},
          content: json["content"]);

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "PJ": Map.from(pj)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "content": content
      };
}
