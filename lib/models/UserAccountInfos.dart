import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

UserAccountInfos userAccountInfosFromJson(String str) =>
    UserAccountInfos.fromJson(json.decode(str));

class UserAccountInfos {
  String nom;
  String nomN;
  String prenom;
  String INS;
  String dateN;
  String genre;
  Image? photo;
  String login;
  String passwordHash;
  String email;

  UserAccountInfos(
      {this.nom = "",
      this.nomN = "",
      this.prenom = "",
      this.INS = "",
      this.dateN = "",
      this.genre = "",
      this.photo,
      this.login = "",
      this.passwordHash = "",
      this.email = ""});

  factory UserAccountInfos.fromJson(Map<String, dynamic> json) =>
      UserAccountInfos(
          nom: json['nom'],
          nomN: json['nomN'],
          prenom: json['prenom'],
          INS: json['INS'] ?? "",
          dateN: json['dateN'],
          genre: json['genre'],
          login: json['login'] ?? "",
          photo: json['image'] != ""
              ? Image.memory(base64.decode(json['image'].replaceAll("\n", "")))
              : null,
          passwordHash: "",
          email: json["email"] ?? "");
}
