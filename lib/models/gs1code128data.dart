import 'dart:core';
import 'package:theradom/models/AII.dart';

class GS1Code128Data {
  final Map<String, String> _data = Map<String, String>();
  static List<AII> aiList = [
    // tous les AI jusqu'à l'id 30, puis ceux obligatoires
    // https://fr.activebarcode.com/codes/ean128_ucc128_ai.html
    AII(
        id: "00",
        minLength: 18,
        maxLength: 18), // Serial Shipping Container Code, SSCC
    AII(
        id: "01",
        minLength: 14,
        maxLength: 14), // Global Trade Item Number, GTIN
    AII(
        id: "02",
        minLength: 14,
        maxLength: 14), // GTIN des articles commerciaux contenus
    AII(id: "10", minLength: 1, maxLength: 20), // Numéro de lot/lot
    AII(id: "11", minLength: 6, maxLength: 6), // Production Date
    AII(id: "12", minLength: 6, maxLength: 6), // Date d’échéance
    AII(id: "13", minLength: 6, maxLength: 6), // Date d’emballage
    AII(id: "15", minLength: 6, maxLength: 6), // Date de péremption
    AII(id: "17", minLength: 6, maxLength: 6), // Date d’expiration
    AII(id: "20", minLength: 2, maxLength: 2), // Variante de produit
    AII(id: "21", minLength: 1, maxLength: 20), // Numéro de série
    AII(id: "22", minLength: 1, maxLength: 29), // Champs de données secondaires
    AII(id: "23n", minLength: 1, maxLength: 19), // Numéro de lot n
    AII(
        id: "240",
        minLength: 1,
        maxLength: 30), // Identification supplémentaire du produit
    AII(id: "241", minLength: 1, maxLength: 30), // Numéro de pièce client
    AII(
        id: "242",
        minLength: 1,
        maxLength: 6), // Numéro de variation sur commande
    AII(id: "250", minLength: 1, maxLength: 30), // Numéro de série secondaire
    AII(id: "251", minLength: 1, maxLength: 30), // Référence à l’entité source
    AII(
        id: "253",
        minLength: 13,
        maxLength: 17), // Identificateur global de type de document
    AII(id: "254", minLength: 1, maxLength: 20), // Composant d’extension GLN
    AII(id: "255", minLength: 13, maxLength: 25), // Global Coupon Number, GCN
    AII(id: "30", minLength: 1, maxLength: 8), // Nombre d’articles
    AII(id: "37", minLength: 1, maxLength: 8), // Nombre d’unités contenues
    AII(
        id: "390d",
        minLength: 1,
        maxLength: 15), // Montant à payer en monnaie locale
    AII(
        id: "391d",
        minLength: 3,
        maxLength: 18), // Montant à payer avec le code de devise ISO
    AII(
        id: "392d",
        minLength: 1,
        maxLength: 15), // Montant payable par article unique en monnaie locale
    AII(
        id: "393d",
        minLength: 3,
        maxLength:
            18), // Montant payable par article unique avec le code de devise ISO
    AII(
        id: "400",
        minLength: 1,
        maxLength: 30), // Numéro de commande d’achat du client
    AII(id: "401", minLength: 1, maxLength: 30), // Numéro d’envoi
    AII(id: "403", minLength: 1, maxLength: 30), // Code de routage
    AII(
        id: "420",
        minLength: 1,
        maxLength: 20), // Expédier au code postal, Autorité postale unique
    AII(
        id: "421",
        minLength: 3,
        maxLength: 15), // Expédier au code postal avec le code de pays ISO
    AII(
        id: "423",
        minLength: 3,
        maxLength: 15), // Pays ou pays de traitement initial
    AII(
        id: "7002",
        minLength: 1,
        maxLength:
            30), // UN/ECE Classification des carcasses de viande et des coupes
    AII(id: "7004", minLength: 1, maxLength: 4), // Puissance active
    AII(
        id: "703n",
        minLength: 3,
        maxLength:
            30), // Approbation du processeur, avec code de pays ISOn indique le nombre de séquences de plusieurs processeurs
    AII(
        id: "8002",
        minLength: 1,
        maxLength: 20), // Identificateur de téléphone mobile
    AII(
        id: "8003",
        minLength: 14,
        maxLength: 30), // Identificateur global d’actif remboursable
    AII(
        id: "8004",
        minLength: 1,
        maxLength: 30), // Identificateur d’actif individuel global
    AII(
        id: "8007",
        minLength: 1,
        maxLength: 30), // International Bank Account Number, IBAN
    AII(id: "8008", minLength: 8, maxLength: 12), // Date et heure de production
    AII(
        id: "8020",
        minLength: 1,
        maxLength: 25), // Numéro de référence du bordereau de paiement
    AII(id: "8110", minLength: 1, maxLength: 30), // Coupon code ID
    AII(id: "8200", minLength: 1, maxLength: 70), // URL d’emballage étendu
    AII(
        id: "90",
        minLength: 1,
        maxLength: 30) // Mutuellement convenu entre partenaires commerciaux
  ];

  GS1Code128Data(String s) {
    int fnc1CodeUnit = s[0].codeUnitAt(0);
    StringBuffer ai = StringBuffer();
    int index = 1;
    while (index < s.length) {
      ai.write(s[index++]);
      AII? info;
      int position =
          aiList.indexWhere((element) => element.id == ai.toString());
      if (position != -1) {
        info = aiList[position];
      }
      if (info != null) {
        StringBuffer value = StringBuffer();
        for (int i = 0; i < info.maxLength && index < s.length; i++) {
          String c = s[index++];
          if (c.codeUnitAt(0) == fnc1CodeUnit) {
            break;
          }
          value.write(c);
        }
        // DEBUG
        // if (value.length < info.minLength) {
        //   print("Short field for AI \"" +
        //       ai.toString() +
        //       "\": \"" +
        //       value.toString() +
        //       "\".");
        // }
        _data[ai.toString()] = value.toString();
        ai.clear();
      }
    }
    // DEBUG
    // if (ai.length > 0) {
    //   print("Unknown AI \"" + ai.toString() + "\".");
    // }
  }

  static DateTime? asDate(String? s) {
    if (s == null) {
      return null;
    }
    int d = int.parse(s.substring(4, 6));
    int m = int.parse(s.substring(2, 4));
    if (d == 0) {
      m++;
    }
    int y = int.parse("20" + s.substring(0, 2));
    return DateTime(y, m, d);
  }

  DateTime? getExpirationDate() {
    return asDate(_data["17"]);
  }

  String? getBatchNumber() {
    return _data["10"];
  }
}
