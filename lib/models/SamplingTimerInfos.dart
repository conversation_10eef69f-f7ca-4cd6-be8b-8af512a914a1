import 'dart:convert';

SamplingTimerInfos samplingTimerInfosFromJson(String str) =>
    SamplingTimerInfos.fromJson(json.decode(str));
String samplingTimerInfosToJson(SamplingTimerInfos data) =>
    json.encode(data.toJson());

class SamplingTimerInfos {
  DateTime alertDate;
  DateTime dateSeance;
  String? numSeance;

  SamplingTimerInfos(
      {required this.alertDate,
      required this.dateSeance,
      required this.numSeance});

  factory SamplingTimerInfos.fromJson(Map<String, dynamic> json) {
    return SamplingTimerInfos(
        alertDate: DateTime.fromMillisecondsSinceEpoch(json["alertDate"]),
        numSeance: json["numSeance"],
        dateSeance: DateTime.fromMillisecondsSinceEpoch(json["dateSeance"]));
  }

  Map<String, dynamic> toJson() => {
        "alertDate": alertDate.millisecondsSinceEpoch,
        "dateSeance": dateSeance.millisecondsSinceEpoch,
        "numSeance": numSeance
      };
}
