import 'dart:convert';

import 'package:theradom/utils/html_character_entities.dart';

SendMessageRequest sendMessageRequestFromJson(String str) =>
    SendMessageRequest.fromJson(json.decode(str));
String sendMessageRequestToJson(SendMessageRequest data) =>
    json.encode(data.toJson());

class SendMessageRequest {
  String numpat;
  String destinataire;
  String objet;
  String contenu;
  List<String> pjBase64List;
  List<String> pjNameList;

  SendMessageRequest(
      {required this.numpat,
      required this.destinataire,
      required this.objet,
      required this.contenu,
      required this.pjBase64List,
      required this.pjNameList});

  factory SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      SendMessageRequest(
          numpat: json["NUMPAT"],
          destinataire: json["destinataire"],
          objet: json["objet"],
          contenu: json["contenu"],
          pjBase64List: [],
          pjNameList: []);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {};

    json["NUMPAT"] = numpat;
    json["destinataire"] = destinataire;
    json["objet"] = HtmlCharacterEntities.encode(objet);
    json["contenu"] = HtmlCharacterEntities.encode(contenu);

    Map<String, dynamic> pj = {};
    int i = 0;
    pjBase64List.forEach((base64String) {
      Map<String, dynamic> pjContenu = {};
      pjContenu["name"] = pjNameList[i];
      pjContenu["content"] = pjBase64List[i];
      pj[i.toString()] = pjContenu;
      i++;
    });

    json["pj"] = pj;

    return json;
  }
}
