import 'dart:convert';

FileObject fileObjectFromJson(String str) =>
    FileObject.fromJson(json.decode(str));
String fileObjectToJson(FileObject data) => json.encode(data.toJson());

class FileObject {
  final String name;
  final String content;

  FileObject({required this.name, required this.content});

  factory FileObject.fromJson(Map<String, dynamic> json) => FileObject(
        name: json["name"],
        content: json["content"],
      );

  Map<String, dynamic> toJson() => {"name": name, "content": content};
}
