import 'package:flutter/material.dart';
import 'package:theradom/extensions/string_extension.dart';

class Consumable {
  String numTraitement;
  String nomTrait;
  String numMedic;
  bool boolNonAdministre;
  double nombre;
  String? numLot;
  bool administre;
  TimeOfDay heureAdministration;
  String motifNonAdmin;
  double nbPrescrit;

  Consumable({
    required this.numTraitement,
    required this.nomTrait,
    required this.numMedic,
    required this.boolNonAdministre,
    required this.nombre,
    required this.numLot,
    required this.administre,
    required this.heureAdministration,
    required this.motifNonAdmin,
    required this.nbPrescrit,
  });

  factory Consumable.fromJson(Map<String, dynamic> json) => Consumable(
      numTraitement: json["num_traitement"],
      nomTrait: json["nom_trait"],
      numMedic: json["num_medic"],
      boolNonAdministre: json["bool_non_administre"],
      nombre: double.parse(json["nombre"].toString().replaceAll(",", ".")),
      numLot: json["num_lot"],
      administre: json["administre"],
      heureAdministration:
          json["heure_administration"].toString().hhmm2TimeOfDay(),
      motifNonAdmin: json["motif_non_admin"],
      nbPrescrit:
          double.parse(json["nb_prescrit"].toString().replaceAll(",", ".")));

  Map<String, dynamic> toJson() => {
        "num_traitement": numTraitement,
        "nom_trait": nomTrait,
        "num_medic": numMedic,
        "bool_non_administre": boolNonAdministre,
        "num_lot": numLot,
        "nombre": nombre.toString().replaceAll(".", ","),
        "administre": administre,
        "heure_administration": heureAdministration,
        "motif_non_admin": motifNonAdmin,
        "nb_prescrit": nbPrescrit.toString().replaceAll(".", ",")
      };
}
