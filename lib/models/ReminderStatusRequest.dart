import 'dart:convert';

ReminderStatusRequest reminderStatusRequestFromJson(String str) =>
    ReminderStatusRequest.fromJson(json.decode(str));
String reminderStatusRequestToJson(ReminderStatusRequest data) =>
    json.encode(data.toJson());

class ReminderStatusRequest {
  String numpat;
  String cleFac;
  bool rappel;

  ReminderStatusRequest(
      {required this.numpat, required this.cleFac, required this.rappel});

  factory ReminderStatusRequest.fromJson(Map<String, dynamic> json) =>
      ReminderStatusRequest(
          numpat: json["NUMPAT"],
          cleFac: json["CLE_FAC"],
          rappel: json["rappel"]);

  Map<String, dynamic> toJson() =>
      {"NUMPAT": numpat, "CLE_FAC": cleFac, "rappel": rappel};
}
