import 'dart:convert';
import 'package:theradom/models/jours_dial.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';

BasicUserInfos basicUserInfosFromJson(String str) =>
    BasicUserInfos.fromJson(json.decode(str));
String basicUserInfosToJson(BasicUserInfos data) => json.encode(data.toJson());

class BasicUserInfos {
  String valide;
  String? erreur;
  String changementMdp;
  double taillePolice;
  bool accordCgu;
  String numpat;
  String ins;
  String mail;
  Map<String, String> numSeance;
  String serviceToken;
  String localisation;
  String JWT;
  Map<String, String> enumMotifNonAdmin;
  JoursDial joursDial;
  Map<String, String> seanceNonFinie;
  Map<String, String> dateSeanceNonFinie;
  Map<String, String> heureSeanceNonFinie;
  Map<String, String> heureDebutSeance;
  Map<String, String> monitorings; // id: title;
  String statut;
  int lastEvaluation;
  String login;
  String firstPage;
  int monitoringCount;
  final AlgoPwd algoPwd;

  BasicUserInfos(
      {required this.valide,
      this.erreur,
      required this.changementMdp,
      required this.taillePolice,
      required this.accordCgu,
      required this.numpat,
      required this.ins,
      required this.mail,
      required this.numSeance,
      required this.JWT,
      required this.serviceToken,
      required this.localisation,
      required this.enumMotifNonAdmin,
      required this.joursDial,
      required this.seanceNonFinie,
      required this.dateSeanceNonFinie,
      required this.heureSeanceNonFinie,
      required this.heureDebutSeance,
      required this.login,
      required this.monitorings,
      required this.statut,
      required this.lastEvaluation,
      required this.firstPage,
      required this.monitoringCount,
      required this.algoPwd});

  factory BasicUserInfos.fromJson(Map<String, dynamic> json) => BasicUserInfos(
      valide: json["valide"],
      erreur: json["erreur"] ?? "",
      changementMdp: json["changement_mdp"] ?? "",
      taillePolice: json["taille_police"] != null
          ? double.parse(json["taille_police"])
          : 12.0,
      accordCgu: json["accord_CGU"] ?? false,
      numpat: json["numpat"] ?? "",
      ins: json["ins"] ?? "",
      mail: json["mail"] ?? "",
      numSeance: json["num_seance"] != null
          ? Map.from(json["num_seance"])
              .map((k, v) => MapEntry<String, String>(k, v))
          : {},
      serviceToken: json["serviceToken"] ?? "",
      login: json["login"] ?? "",
      localisation: json["localisation"] ?? "",
      enumMotifNonAdmin: json["enum_motif_non_admin"] != null
          ? Map.from(json["enum_motif_non_admin"])
              .map((k, v) => MapEntry<String, String>(k, v))
          : {},
      joursDial: json["jours_dial"] != null
          ? JoursDial.fromJson(json["jours_dial"])
          : JoursDial(
              lundi: false,
              mardi: false,
              mercredi: false,
              jeudi: false,
              vendredi: false,
              samedi: false,
              dimanche: false),
      seanceNonFinie: json["seance_nonFinie"] != null
          ? Map.from(json["seance_nonFinie"])
              .map((k, v) => MapEntry<String, String>(k, v))
          : {},
      dateSeanceNonFinie: json["dateSeance_nonFinie"] != null
          ? Map.from(json["dateSeance_nonFinie"])
              .map((k, v) => MapEntry<String, String>(k, v))
          : {},
      heureSeanceNonFinie: json["heureSeance_nonFinie"] != null
          ? Map.from(json["heureSeance_nonFinie"])
              .map((k, v) => MapEntry<String, String>(k, v))
          : {},
      heureDebutSeance: json["heureDebut_seance"] != null
          ? Map.from(json["heureDebut_seance"])
              .map((k, v) => MapEntry<String, String>(k, v))
          : {},
      monitorings: json["statut"] != null ? MonitoringUtils.getMonitoringTypesFromStatut(json["statut"]) : {},
      statut: json["statut"] ?? "",
      lastEvaluation: (json["lastEvaluation"] != "" && json["lastEvaluation"] != null) ? int.parse(json["lastEvaluation"]) : 0,
      firstPage: json["firstPage"] ?? "",
      algoPwd: AlgoPwd.fromJson(json['algo_pwd']),
      monitoringCount: "1".allMatches(json["statut"] ?? "").length,
      JWT: json["JWT"] ?? "");

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "changement_mdp": changementMdp,
        "taille_police": taillePolice,
        "accord_CGU": accordCgu,
        "numpat": numpat,
        "num_seance":
            Map.from(numSeance).map((k, v) => MapEntry<String, dynamic>(k, v)),
        "serviceToken": serviceToken,
        "localisation": localisation,
        "enum_motif_non_admin": Map.from(enumMotifNonAdmin)
            .map((k, v) => MapEntry<String, dynamic>(k, v)),
        "jours_dial": joursDial.toJson(),
        "seance_nonFinie": Map.from(seanceNonFinie)
            .map((k, v) => MapEntry<String, dynamic>(k, v)),
        "dateSeance_nonFinie": Map.from(dateSeanceNonFinie)
            .map((k, v) => MapEntry<String, dynamic>(k, v)),
        "heureSeance_nonFinie": Map.from(heureSeanceNonFinie)
            .map((k, v) => MapEntry<String, dynamic>(k, v)),
        "heureDebut_seance": Map.from(heureDebutSeance)
            .map((k, v) => MapEntry<String, dynamic>(k, v)),
        "statut": statut,
        "lastEvaluation": lastEvaluation.toString(),
        "firstPage": firstPage,
        "JWT": JWT,
        'algoPwd': algoPwd.toJson(),
      };
}

class AlgoPwd {
  final String length;
  final String upperCase;
  final String lowerCase;
  final String numbers;
  final String specialChar;

  AlgoPwd({
    this.length = "8",
    this.upperCase = "1",
    this.lowerCase = "1",
    this.numbers = "0",
    this.specialChar = "1",
  });

  factory AlgoPwd.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return AlgoPwd(); // Valeurs par défaut
    }
    return AlgoPwd(
      length: json['length'] ?? "8",
      upperCase: json['upperCase'] ?? "1",
      lowerCase: json['lowerCase'] ?? "1",
      numbers: json['numbers'] ?? "0",
      specialChar: json['specialChar'] ?? "1",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'length': length,
      'upperCase': upperCase,
      'lowerCase': lowerCase,
      'numbers': numbers,
      'specialChar': specialChar,
    };
  }

  bool isEqual(AlgoPwd other) {
    return length == other.length &&
        upperCase == other.upperCase &&
        lowerCase == other.lowerCase &&
        numbers == other.numbers &&
        specialChar == other.specialChar;
  }
}
