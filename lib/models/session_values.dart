import 'package:theradom/models/session_sampling_data.dart';
import 'package:theradom/models/session_unique_data.dart';

class SessionValues {
  SessionUniqueData unique;
  SessionSamplingData box;
  SessionSamplingData table;

  SessionValues({required this.unique, required this.box, required this.table});

  factory SessionValues.fromJson(Map<String, dynamic> json) => SessionValues(
      unique: SessionUniqueData.fromJson(json["unique"]),
      box: SessionSamplingData.fromJson(json["box"]),
      table: SessionSamplingData.fromJson(json["table"]));

  Map<String, dynamic> toJson() =>
      {"unique": unique.toJson(), "box": box.toJson(), "table": table.toJson()};
}
