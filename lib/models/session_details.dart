import 'package:theradom/models/session_structure.dart';
import 'package:theradom/models/session_values.dart';

class SessionDetails {
  SessionValues values;
  SessionStructure structure;

  SessionDetails({
    required this.values,
    required this.structure,
  });

  factory SessionDetails.fromJson(Map<String, dynamic> json) => SessionDetails(
      values: SessionValues.fromJson(json["values"]),
      structure: SessionStructure.fromJson(json["structure"]));

  Map<String, dynamic> toJson() => {
        "values": values.toJson(),
        "structure": Map.from(structure.toJson())
            .map((k, v) => MapEntry<String, dynamic>(k, v))
      };
}
