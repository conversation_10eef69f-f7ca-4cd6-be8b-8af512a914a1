import 'dart:convert';
import 'package:theradom/models/PutDataDescription.dart';

String putSessionSamplingToJson(Map<String, PutSessionSampling> data) =>
    json.encode(
        Map.from(data).map((k, v) => MapEntry<String, dynamic>(k, v.toJson())));

class PutSessionSampling {
  final PutDataDescription putDataDescription;
  final Hrs hrs;
  final Hrs heureModif;
  final Hrs dateModif;

  PutSessionSampling(
      {required this.putDataDescription,
      required this.hrs,
      required this.heureModif,
      required this.dateModif});

  Map<String, dynamic> toJson() => {
        "${putDataDescription.idBD}":
            putDataDescriptionToJson(putDataDescription),
        "Hrs": hrs.toJson(),
        "heure_modif": heureModif.toJson(),
        "date_modif": dateModif.toJson()
      };
}

class Hrs {
  Hrs({required this.value});

  final String value;

  factory Hrs.fromJson(Map<String, dynamic> json) => Hrs(value: json["value"]);

  Map<String, dynamic> toJson() => {"value": value};
}
