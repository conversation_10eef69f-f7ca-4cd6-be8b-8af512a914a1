import 'package:flutter/material.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

class ConsumableDetails {
  String? nom;
  bool administre;
  String? motifNonAdmin;
  double? nombre;
  String? numLot;
  TimeOfDay? heure;
  String? consumableID;
  DateTime? date;

  ConsumableDetails(
      {this.nom,
      required this.administre,
      this.motifNonAdmin,
      this.nombre,
      this.numLot,
      this.heure,
      this.consumableID = "",
      this.date});

  factory ConsumableDetails.fromJson(Map<String, dynamic> json) =>
      ConsumableDetails(
          nom: json["nom"],
          administre: json["administre"],
          motifNonAdmin: json["motif_non_admin"],
          nombre: double.parse(json["nombre"].toString().replaceAll(",", ".")),
          heure: json["heure"]);

  Map<String, dynamic> toJson() => {
        "administre": administre,
        "motif_non_admin": motifNonAdmin,
        "num_lot": numLot,
        "nombre": nombre.toString().replaceAll(".", ","),
        "heure": heure!.toLanguageString()
      };
}
