import 'dart:convert';

BasicUserInfos2 basicUserInfos2FromJson(String str) =>
    BasicUserInfos2.fromJson(json.decode(str));
String basicUserInfos2ToJson(BasicUserInfos2 data) =>
    json.encode(data.toJson());

class BasicUserInfos2 {
  String valide;
  String? erreur;
  String numpat;
  String nompat;
  String login;
  String prenom;

  BasicUserInfos2(
      {required this.valide,
      this.erreur,
      required this.numpat,
      required this.nompat,
      required this.login,
      required this.prenom});

  factory BasicUserInfos2.fromJson(Map<String, dynamic> json) =>
      BasicUserInfos2(
          valide: json["valide"],
          erreur: json["erreur"],
          numpat: json["NUMPAT"],
          nompat: json["NOMPAT"],
          login: json["login"],
          prenom: json["PRENOM"]);

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "NUMPAT": numpat,
        "NOMPAT": nompat,
        "login": login,
        "PRENOM": prenom
      };
}
