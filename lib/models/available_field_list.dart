import 'package:theradom/models/enum_champ_special.dart';

class AvailableFieldList {
  String tag;
  ChampSpecial champSpecial;
  String id;
  String name;

  AvailableFieldList(
      {required this.tag,
      required this.champSpecial,
      required this.id,
      required this.name});

  factory AvailableFieldList.fromJson(Map<String, dynamic> json) =>
      AvailableFieldList(
          tag: json["tag"],
          champSpecial: json["champSpecial"] == null
              ? ChampSpecial.EMPTY
              : champSpecialValues.map[json["champSpecial"]] ??
                  ChampSpecial.EMPTY,
          id: json["id"] == null ? null : json["id"],
          name: json["name"] == null ? null : json["name"]);

  Map<String, dynamic> toJson() => {
        "tag": tag,
        "champSpecial": champSpecial == ChampSpecial.EMPTY
            ? null
            : champSpecialValues.reverse[champSpecial],
        "id": id,
        "name": name
      };
}

final champSpecialValues = EnumValues(map: {
  "1": ChampSpecial.ONE,
  "2": ChampSpecial.TWO,
  "": ChampSpecial.EMPTY,
  "time": ChampSpecial.TIME
});

class EnumValues<T> {
  Map<String, T> map;
  Map<T, String>? reverseMap;

  EnumValues({required this.map});

  Map<T, String> get reverse {
    if (reverseMap == null) {
      reverseMap = map.map((k, v) => new MapEntry(v, k));
    }
    return reverseMap!;
  }
}
