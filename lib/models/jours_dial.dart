class JoursDial {
  final bool lundi;
  final bool mardi;
  final bool mercredi;
  final bool jeudi;
  final bool vendredi;
  final bool samedi;
  final bool dimanche;

  const JoursDial(
      {this.lundi = false,
      this.mardi = false,
      this.mercredi = false,
      this.jeudi = false,
      this.vendredi = false,
      this.samedi = false,
      this.dimanche = false});

  factory JoursDial.fromJson(Map<String, dynamic> json) => JoursDial(
      lundi: json["1"],
      mardi: json["2"],
      mercredi: json["3"],
      jeudi: json["4"],
      vendredi: json["5"],
      samedi: json["6"],
      dimanche: json["7"]);

  Map<String, dynamic> toJson() => {
        "1": lundi,
        "2": mardi,
        "3": mercredi,
        "4": jeudi,
        "5": vendredi,
        "6": samedi,
        "7": dimanche
      };

  bool canCreateSessionOnWeekday(int weekday) {
    switch (weekday) {
      case 1:
        {
          // monday
          return lundi;
        }
      case 2:
        {
          // tuesday
          return mardi;
        }
      case 3:
        {
          // wednesday
          return mercredi;
        }
      case 4:
        {
          // thursday
          return jeudi;
        }
      case 5:
        {
          // friday
          return vendredi;
        }
      case 6:
        {
          // saturday
          return samedi;
        }
      case 7:
        {
          // sunday
          return dimanche;
        }
      default:
        {
          return false;
        }
    }
  }
}
