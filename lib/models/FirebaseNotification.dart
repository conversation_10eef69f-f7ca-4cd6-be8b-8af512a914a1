FirebaseNotification firebaseNotificationFromJson(
        int event, Map<String, dynamic> message) =>
    FirebaseNotification.fromJSON(event, message);

class FirebaseNotification {
  String title;
  String body;
  String? url;
  int? actionType; // 0 -> suivis, 1 -> calendrier, 2 -> messagerie, 3 -> bio
  int event; // 0 = onMessage, 1 = onResume, 2 = onLaunch

  FirebaseNotification(
      {required this.title,
      required this.body,
      this.url,
      this.actionType,
      required this.event});

  factory FirebaseNotification.fromJSON(int event, Map<String, dynamic> json) {
    return FirebaseNotification(
        title: json['notification']['title'],
        body: json['notification']['body'],
        url: json['data']['url'],
        actionType: json['data']['action_type'] != null
            ? int.tryParse(json['data']['action_type'])
            : null,
        event: event);
  }
}
