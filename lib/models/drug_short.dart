class DrugShort {
  String numTraitement;
  String numType;
  String posologie;
  String dci;

  DrugShort(
      {required this.numTraitement,
      required this.numType,
      required this.posologie,
      required this.dci});

  factory DrugShort.fromJson(Map<String, dynamic> json) => DrugShort(
      numTraitement: json['num_traitement'],
      numType: json['Num_Type'],
      posologie: json['POSOLOGIE'],
      dci: json["dci"] ?? "");

  Map<String, dynamic> toJson() => {
        "num_traitement": numTraitement,
        "Num_Type": numType,
        "POSOLOGIE": posologie,
        "DCI": dci
      };
}
