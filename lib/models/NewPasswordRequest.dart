import 'dart:convert';

NewPasswordRequest newPasswordRequestFromJson(String str) =>
    NewPasswordRequest.fromJson(json.decode(str));
String newPasswordRequestToJson(NewPasswordRequest data) =>
    json.encode(data.toJson());

class NewPasswordRequest {
  String numpat;
  String newPwd;

  NewPasswordRequest({required this.numpat, required this.newPwd});

  factory NewPasswordRequest.fromJson(Map<String, dynamic> json) =>
      NewPasswordRequest(numpat: json["NUMPAT"], newPwd: json["new_pwd"]);

  Map<String, dynamic> toJson() => {"NUMPAT": numpat, "new_pwd": newPwd};
}
