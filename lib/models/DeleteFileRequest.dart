import 'dart:convert';

DeleteFileRequest deleteFileRequestFromJson(String str) =>
    DeleteFileRequest.fromJson(json.decode(str));
String deleteFileRequestToJson(DeleteFileRequest data) =>
    json.encode(data.toJson());

class DeleteFileRequest {
  String numpat;
  String table;
  String numSeanceMail;
  String numPj;

  DeleteFileRequest(
      {required this.numpat,
      required this.table,
      required this.numSeanceMail,
      required this.numPj});

  factory DeleteFileRequest.fromJson(Map<String, dynamic> json) =>
      DeleteFileRequest(
          numpat: json["NUMPAT"],
          table: json["table"],
          numSeanceMail: json["num_seance_mail"],
          numPj: json["num_pj"]);

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "table": table,
        "num_seance_mail": numSeanceMail,
        "num_pj": numPj
      };
}
