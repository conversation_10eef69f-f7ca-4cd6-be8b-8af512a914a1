import 'dart:convert';
import 'package:theradom/models/consumable_details.dart';
import 'package:theradom/models/drug_details.dart';

String putDrugStatusToJson(PutDrugStatus data) => json.encode(data.toJson());

class PutDrugStatus {
  String numpat;
  String numSeance;
  String suivi;
  List<ConsumableDetails> produits;
  List<DrugDetails> traitements;

  PutDrugStatus({
    required this.numpat,
    required this.numSeance,
    required this.suivi,
    required this.produits,
    required this.traitements,
  });

  Map<String, dynamic> toJson() {
    // Consumable
    Map consumableMap = Map<String, dynamic>();
    produits.forEach((produit) {
      consumableMap[produit.consumableID] = produit.toJson();
    });

    // Drug
    Map drugMap = Map<String, dynamic>();
    traitements.forEach((traitement) {
      drugMap[traitement.drugID] = traitement.toJson();
    });

    String produitsKey = "x";
    if (consumableMap.keys.length != 0) {
      produitsKey = "produits";
    }

    String traitementsKey = "x";
    if (drugMap.keys.length != 0) {
      traitementsKey = "traitements";
    }

    return {
      "NUMPAT": numpat,
      "num_seance": numSeance,
      "suivi": suivi,
      "$produitsKey": consumableMap,
      "$traitementsKey": drugMap
    };
  }
}
