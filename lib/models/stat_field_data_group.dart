import 'package:theradom/models/stats_field_data.dart';

class StatsFieldDataGroup {
  String name;
  List<StatsFieldData> statsFieldDataList;

  StatsFieldDataGroup({required this.name, required this.statsFieldDataList});

  int selectedFieldCount() {
    int count = 0;
    statsFieldDataList.forEach((data) {
      if (data.selected) {
        count++;
      }
    });
    return count;
  }

  void selectAll() {
    statsFieldDataList.forEach((data) {
      data.selected = true;
    });
  }

  void deselectAll() {
    statsFieldDataList.forEach((data) {
      data.selected = false;
    });
  }
}
