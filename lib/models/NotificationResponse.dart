import 'dart:convert';
import 'package:theradom/models/finished_list.dart';
import 'package:theradom/models/unfinished_session.dart';

NotificationResponse notificationResponseFromJson(String str) =>
    NotificationResponse.fromJson(json.decode(str));
String notificationResponseToJson(NotificationResponse data) =>
    json.encode(data.toJson());

class NotificationResponse {
  String valide;
  String? erreur;
  int notifMail;
  int notifRdVnew;
  int notifRdVsoon;
  int notifRdvAujourdhui;
  String teleconsultation;
  Map<String, UnfinishedSession> seanceNonFinie;
  FinishedList endSeance;

  NotificationResponse(
      {required this.valide,
      this.erreur,
      required this.notifMail,
      required this.notifRdVnew,
      required this.notifRdVsoon,
      required this.notifRdvAujourdhui,
      required this.teleconsultation,
      required this.seanceNonFinie,
      required this.endSeance});

  factory NotificationResponse.fromJson(Map<String, dynamic> json) =>
      NotificationResponse(
          valide: json["valide"] == null ? null : json["valide"],
          erreur: json["erreur"] == null ? null : json["erreur"],
          notifMail: int.parse(json["notifMail"]),
          notifRdVnew: int.parse(json["notifRDVnew"]),
          notifRdVsoon: int.parse(json["notifRDVsoon"]),
          notifRdvAujourdhui: int.parse(json["notifRDVAujourdhui"]),
          teleconsultation: json["teleconsultation"],
          seanceNonFinie: Map.from(json["seance_nonFinie"]).map((k, v) =>
              MapEntry<String, UnfinishedSession>(
                  k, UnfinishedSession.fromJson(v))),
          endSeance: FinishedList.fromJson(json["end_seance"]));

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "notifMail": notifMail,
        "notifRDVnew": notifRdVnew,
        "notifRDVsoon": notifRdVsoon,
        "notifRDVAujourdhui": notifRdvAujourdhui,
        "teleconsultation": teleconsultation,
        "seance_nonFinie": Map.from(seanceNonFinie)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "end_seance": endSeance.toJson()
      };
}
