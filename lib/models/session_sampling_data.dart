import 'dart:collection';

class SessionSamplingData {
  Map<String, dynamic> valueName;

  SessionSamplingData({required this.valueName});

  factory SessionSamplingData.fromJson(Map<String, dynamic> json) =>
      SessionSamplingData(
          valueName: LinkedHashMap.fromEntries(Map.from(json)
              .map((k, v) => MapEntry<String, dynamic>(k, v))
              .entries
              .toList()
              ));

  Map<String, dynamic> toJson() => {"valueName": valueName};
}
