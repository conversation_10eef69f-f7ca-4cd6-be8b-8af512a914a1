import 'dart:convert';

Credentials credentialsFromJson(String str) =>
    Credentials.fromJson(json.decode(str));
String credentialsToJson(Credentials data) => json.encode(data.toJson());

class Credentials {
  String login;
  String password;
  String appToken;

  Credentials(
      {required this.login, required this.password, required this.appToken});

  factory Credentials.fromJson(Map<String, dynamic> json) => Credentials(
      login: json["login"],
      password: json["password"],
      appToken: json["appToken"]);

  Map<String, dynamic> toJson() =>
      {"login": login, "password": password, "appToken": appToken};
}
