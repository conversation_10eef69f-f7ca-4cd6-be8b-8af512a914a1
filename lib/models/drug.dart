import 'package:flutter/material.dart';
import 'package:theradom/extensions/string_extension.dart';

class Drug {
  String numTraitement;
  String nomTrait;
  String numMedic;
  double dose;
  String unite;
  String forme;
  String? numLot;
  bool boolNonAdministre;
  TimeOfDay heureAdministration;
  double nombre;
  bool multiDosesEnSeance;
  double anticoagulantCharge;
  double anticoagulantEntretien;
  TimeOfDay anticoagulantArret;
  String numType;
  String posologie;
  bool administre;
  String motifNonAdmin;
  double nbPrescrit;
  double dosePrescrite;
  String message; // message indicatif en cas de dose non cohérente

  Drug(
      {required this.numTraitement,
      required this.nomTrait,
      required this.numMedic,
      required this.dose,
      required this.unite,
      required this.numLot,
      required this.forme,
      required this.boolNonAdministre,
      required this.heureAdministration,
      required this.nombre,
      required this.multiDosesEnSeance,
      required this.anticoagulantCharge,
      required this.anticoagulantEntretien,
      required this.anticoagulantArret,
      required this.numType,
      required this.posologie,
      required this.administre,
      required this.motifNonAdmin,
      required this.nbPrescrit,
      required this.dosePrescrite,
      required this.message});

  factory Drug.fromJson(Map<String, dynamic> json) {
    // filtre pour les mauvaises valeur de DOSE
    String doseStr = json["DOSE"].toString();
    double dose;
    String message = "";
    try {
      dose = double.parse(doseStr.replaceAll(",", "."));
    } catch (e) {
      if ((doseStr.contains("S")) && (doseStr.contains("P"))) {
        message = "Dose: selon protocole";
      } else {
        message = "Dose: veuillez vous référer à votre prescription";
      }
      dose = 0;
    }

    return Drug(
        numTraitement: json["num_traitement"],
        nomTrait: json["nom_trait"],
        numMedic: json["num_medic"],
        //dose: double.parse((json["DOSE"] == "") || (json["DOSE"] == "null") ? "0" : json["DOSE"].toString().replaceAll(",", ".")),
        dose: dose,
        unite: json["unite"] ?? "",
        forme: json["FORME"] ?? "",
        numLot: json["num_lot"],
        boolNonAdministre: json["bool_non_administre"],
        heureAdministration:
            json["heure_administration"].toString().hhmm2TimeOfDay(),
        nombre: double.parse(json["nombre"] == ""
            ? "0"
            : json["nombre"].toString().replaceAll(",", ".")),
        multiDosesEnSeance: json["multi_doses_en_seance"],
        anticoagulantCharge: double.parse(json["anticoagulant_charge"] == ""
            ? "0"
            : json["anticoagulant_charge"].toString().replaceAll(",", ".")),
        anticoagulantEntretien: double.parse(json["anticoagulant_entretien"] ==
                ""
            ? "0"
            : json["anticoagulant_entretien"].toString().replaceAll(",", ".")),
        anticoagulantArret:
            json["anticoagulant_arret"].toString().hhmm2TimeOfDay(),
        numType: json["Num_Type"],
        posologie: json["POSOLOGIE"],
        administre: json["administre"],
        motifNonAdmin: json["motif_non_admin"],
        nbPrescrit: double.parse(json["nb_prescrit"] == ""
            ? "0"
            : json["nb_prescrit"].toString().replaceAll(",", ".")),
        dosePrescrite: double.parse(json["dose_prescrite"] == ""
            ? "0"
            : json["dose_prescrite"].toString().replaceAll(",", ".")),
        message: message);
  }

  Map<String, dynamic> toJson() => {
        "num_traitement": numTraitement,
        "nom_trait": nomTrait,
        "num_medic": numMedic,
        "DOSE": dose.toString().replaceAll(".", ","),
        "unite": unite,
        "FORME": forme,
        "num_lot": numLot,
        "bool_non_administre": boolNonAdministre,
        "heure_administration": heureAdministration,
        "nombre": nombre.toString().replaceAll(".", ","),
        "multi_doses_en_seance": multiDosesEnSeance,
        "anticoagulant_charge":
            anticoagulantCharge.toString().replaceAll(".", ","),
        "anticoagulant_entretien":
            anticoagulantEntretien.toString().replaceAll(".", ","),
        "anticoagulant_arret": anticoagulantArret,
        "Num_Type": numType,
        "POSOLOGIE": posologie,
        "administre": administre,
        "motif_non_admin": motifNonAdmin,
        "nb_prescrit": nbPrescrit.toString().replaceAll(".", ","),
        "dose_prescrite": dosePrescrite.toString().replaceAll(".", ","),
      };
}
