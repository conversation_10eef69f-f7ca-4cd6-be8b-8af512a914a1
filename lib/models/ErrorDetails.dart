import 'dart:convert';

ErrorDetails errorDetailsFromJson(String str) =>
    ErrorDetails.fromJson(json.decode(str));
String errorDetailsToJson(ErrorDetails data) => json.encode(data.toJson());

class ErrorDetails {
  ErrorDetails({
    required this.code,
    required this.message,
  });

  String code;
  String message;

  factory ErrorDetails.fromJson(Map<String, dynamic> json) =>
      ErrorDetails(code: json["code"], message: json["message"]);

  Map<String, dynamic> toJson() => {"code": code, "message": message};
}
