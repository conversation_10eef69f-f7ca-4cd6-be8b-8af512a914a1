class BiologicDataContent {
  String typeBilan;
  String type;
  String valeurAv;
  String valeurAp;
  String limiteSup;
  String limiteInf;
  String commentaire;
  String normale;
  DateTime date;

  BiologicDataContent(
      {required this.typeBilan,
      required this.type,
      required this.valeurAv,
      required this.valeurAp,
      required this.limiteSup,
      required this.limiteInf,
      required this.commentaire,
      required this.normale,
      required this.date});

  factory BiologicDataContent.fromJson(
          String key, Map<String, dynamic> json, DateTime date) =>
      BiologicDataContent(
          typeBilan: key,
          type: json["type"] ?? "",
          valeurAv: json["VALEUR_AV"] ?? "",
          valeurAp: json["VALEUR_AP"] ?? "",
          limiteSup: json["LIMITE_SUP"] ?? "",
          limiteInf: json["LIMITE_INF"] ?? "",
          commentaire: json["COMMENTAIRE"] ?? "",
          normale: json["NORMALE"] ?? "",
          date: date);

  Map<String, dynamic> toJson() => {
        "type": type,
        "VALEUR_AV": valeurAv,
        "VALEUR_AP": valeurAp,
        "LIMITE_SUP": limiteSup,
        "LIMITE_INF": limiteInf,
        "COMMENTAIRE": commentaire,
        "NORMALE": normale
      };
}
