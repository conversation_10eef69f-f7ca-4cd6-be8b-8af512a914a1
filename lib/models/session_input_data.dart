import 'dart:collection';
import 'package:flutter/services.dart';
import 'package:theradom/models/FieldDescription.dart';
import 'package:theradom/utils/regexp_utils.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class SessionInputData {
  final FieldDescription fieldDescription;
  final bool normalite;
  late RegExp regExp;
  late TextInputType keyboardType;
  late List<TextInputFormatter> textInputFormatterList = [];
  late String hint = "";

  SessionInputData({
    required this.fieldDescription,
    required this.normalite,
  });

  void init() {
    _setRegExp();
    _setTextInputType();
    _setTextInputFormatterList();
    _setSpecialFieldTextInputFormatterList();
    _setSpecialFieldHint();
  }

  bool check(dynamic val) {
    // controle la valeur saisie par l'utilisateur en fonction des valeurs d'alertes définies
    bool showAlarm;
    switch (fieldDescription.champSpecial) {
      case "1":
        {
          // tension format XXX
          showAlarm = _checkSimpleValue(val);
        }
        break;
      case "2":
        {
          // tension format XXX/XXX
          if (fieldDescription.typeAlerte != "0") {
            // faire le check
            showAlarm = _checkCSValue(val, fieldDescription.valeur["min"],
                fieldDescription.valeur["max"]);
          } else {
            // pas d'alarme
            showAlarm = false;
          }
        }
        break;
      default:
        {
          showAlarm = _checkSimpleValue(val);
        }
        break;
    }
    return showAlarm;
  }

  bool checkOnFormValidation(dynamic valueToCheck) {
    // check les valeurs à la validation du formulaire
    bool alert;
    switch (fieldDescription.typeAlerte) {
      case "0":
        {
          // pas d'alarme
          alert = false;
        }
        break;
      case "2":
        {
          // alarme si valeur vide
          alert = _isValueEmpty(valueToCheck);
        }
        break;
      case "3":
        {
          // alarme si valeur anormale ou vide
          alert = _isValueEmpty(valueToCheck);
        }
        break;
      default:
        {
          // pas d'alarme
          alert = false;
        }
    }
    return alert;
  }

  bool _checkSimpleValue(dynamic valueToCheck) {
    // retourne un booleen indiquant si la valeur du champ special reçue doit lever une alerte ou non
    bool alert;
    dynamic alertValues = fieldDescription.valeur;
    switch (fieldDescription.typeAlerte) {
      case "0":
        {
          // pas d'alarme
          alert = false;
        }
        break;
      case "1":
        {
          // alarme si valeur anormale
          alert = _checkOperand(valueToCheck, alertValues);
        }
        break;
      case "2":
        {
          // alarme si valeur vide
          alert = _isValueEmpty(valueToCheck);
        }
        break;
      case "3":
        {
          // alarme si valeur anormale ou vide
          if ((_isValueEmpty(valueToCheck)) ||
              (_checkOperand(valueToCheck, alertValues))) {
            alert = true;
          } else {
            alert = false;
          }
        }
        break;
      default:
        {
          // pas d'alarme
          alert = false;
        }
    }
    return alert;
  }

  bool _checkCSValue(String valeurAchecker, String? min, String? max) {
    // retourne un booléen indiquant si la valeur reçue doit lever une alerte ou non
    // si c'est un champ special de la forme xxx/xxx == A/B
    // min et max sont de la forme xxx/xxx == A/B
    List<String> tabValeurAchecker = valeurAchecker.split("/");
    String valeurA = tabValeurAchecker[0];
    String valeurB = tabValeurAchecker[1];
    double valAcheckerA = double.parse(valeurA);
    double valAcheckerB = double.parse(valeurB);

    String minA = "";
    String minB = "";
    if ((min != null)) {
      if (min.contains('/')) {
        List<String> tabMin = min.split("/");
        minA = tabMin[0];
        minB = tabMin[1];
      }
    }

    String maxA = "";
    String maxB = "";
    if (max != null) {
      if (max.contains('/')) {
        List<String> tabMax = max.split("/");
        maxA = tabMax[0];
        maxB = tabMax[1];
      }
    }

    bool boolresA = _checkMinnmax(valAcheckerA, minA, maxA);
    bool boolresB = _checkMinnmax(valAcheckerB, minB, maxB);
    if ((boolresA) || (boolresB)) {
      return true;
    } else {
      return false;
    }
  }

  bool _checkOperand(dynamic valueToCheck, dynamic alertValues) {
    // check l'opérateur qui va nous permettre de lever une alerte sur la valeur à checker
    bool alarme;
    switch (fieldDescription.typeOperateur) {
      case "minnmaxx":
        {
          // alertValues est ListAlarmValues et valueToCheck est un numérique
          double valeur =
              double.parse(valueToCheck.toString().replaceAll(",", "."));
          LinkedHashMap<String, dynamic> valeursAlertesMap = alertValues;
          String minString = valeursAlertesMap["min"].toString();
          String maxString = valeursAlertesMap["max"].toString();
          alarme = _checkMinnmax(valeur, minString, maxString);
        }
        break;
      case "=":
        {
          if (valueToCheck is int) {
            // check d'un int
            if (valueToCheck == int.parse(alertValues)) {
              alarme = true;
            } else {
              alarme = false;
            }
          } else {
            // alertValues est une liste de bool ou de String et valueToCheck est un bool ou un String
            alarme = false;
            LinkedHashMap<String, dynamic> alertValuesMap = alertValues;
            if (alertValuesMap["0"] is bool) {
              // check d'une cac
              alertValuesMap.forEach((key, valBool) {
                if (valueToCheck == valBool) {
                  alarme = true;
                }
              });
            } else {
              // check d'une énumération
              String valeur = valueToCheck.toString();
              alertValuesMap.forEach((key, valString) {
                if (valeur == valString.toString()) {
                  alarme = true;
                }
              });
            }
          }
        }
        break;
      default:
        {
          alarme = false;
        }
    }
    return alarme;
  }

  bool _checkMinnmax(double valeur, String minString, String maxString) {
    // check la valeur à tester avec des valeurs min et max, retourne un booléen indiquant s'il y a une alerte à lever
    bool alert;
    if (minString == "") {
      // borne min vaut -inf
      if (maxString == "") {
        // ]-inf;+inf[
        alert = false;
      } else {
        // ]-inf;max[
        double max = double.parse(maxString.replaceAll(",", "."));
        if (normalite) {
          if (valeur > max) {
            alert = true;
          } else {
            alert = false;
          }
        } else {
          if (valeur < max) {
            alert = true;
          } else {
            alert = false;
          }
        }
      }
    } else {
      if (maxString == "") {
        // ]min;+inf[
        double min = double.parse(minString.replaceAll(",", "."));
        if (normalite) {
          if (valeur < min) {
            alert = true;
          } else {
            alert = false;
          }
        } else {
          if (valeur > min) {
            alert = true;
          } else {
            alert = false;
          }
        }
      } else {
        // ]min;max[
        double min = double.parse(minString.replaceAll(",", "."));
        double max = double.parse(maxString.replaceAll(",", "."));
        if (normalite) {
          if ((valeur < min) || (valeur > max)) {
            alert = true;
          } else {
            alert = false;
          }
        } else {
          if ((valeur > min) && (valeur < max)) {
            alert = true;
          } else {
            alert = false;
          }
        }
      }
    }
    return alert;
  }

  bool _isValueEmpty(dynamic valeurAchecker) {
    // regarde si la variable est nulle ou vide et renvoie un booléen
    bool isEmpty = false;
    if ((valeurAchecker == null) ||
        (valeurAchecker == "") ||
        (valeurAchecker == "0") ||
        (valeurAchecker == "000/00") ||
        (valeurAchecker == "000/000") ||
        (valeurAchecker == "0/0") ||
        (valeurAchecker == "- -")) {
      isEmpty = true;
    }
    return isEmpty;
  }

  void _setRegExp() {
    // set le regex en fonction du format
    switch (fieldDescription.format) {
      case "\"float\"":
        {
          regExp = RegexpUtils.decimal;
        }
        break;
      case "\"alphanumerique\"":
        {
          regExp = RegexpUtils.alphanumeric;
        }
        break;
      case "\"entier\"":
        {
          regExp = RegexpUtils.integer;
        }
        break;
      default:
        {}
        break;
    }
  }

  void _setTextInputType() {
    // set le TextInputType selon le format

    String format = "special";

    if (fieldDescription.champSpecial == "") format = fieldDescription.format;

    switch (format) {
      // switch format
      case "\"float\"":
        {
          // float
          keyboardType =
              TextInputType.numberWithOptions(decimal: true, signed: true);
        }
        break;
      case "\"alphanumerique\"":
        {
          // alpha
          keyboardType = TextInputType.text;
        }
        break;
      case "\"entier\"":
        {
          keyboardType =
              TextInputType.numberWithOptions(signed: true, decimal: false);
        }
        break;
      case "special":
        {
          // pour un champ special (tension)
          keyboardType = TextInputType.number;
        }
        break;
      default:
        {
          // on ne sait pas, je propose de saisir un text
          keyboardType = TextInputType.text;
        }
        break;
    }
  }

  void _setTextInputFormatterList() {
    // set la liste des TextInputFormatter selon le format
    switch (fieldDescription.format) {
      // switch format
      case "\"float\"":
        {
          // float
          textInputFormatterList
              .add(FilteringTextInputFormatter.allow(RegexpUtils.decimal));
          textInputFormatterList.add(LengthLimitingTextInputFormatter(7));
        }
        break;
      case "\"alphanumerique\"":
        {
          // alpha
          /*
        textInputFormatterList.add(
          FilteringTextInputFormatter.allow(RegexpUtils.alphanumeric)
        );
         */
          // commenté car en fait "alphanumerique" signifie texte libre
        }
        break;
      case "\"entier\"":
        {
          // int
          textInputFormatterList
              .add(FilteringTextInputFormatter.allow(RegexpUtils.integer));
          textInputFormatterList.add(LengthLimitingTextInputFormatter(7));
        }
        break;
      default:
        {}
        break;
    }
  }

  void _setSpecialFieldTextInputFormatterList() {
    // set la liste des TextInputFormatter selon la valeur de champSpecial

    if (fieldDescription.champSpecial != "") textInputFormatterList.clear();

    switch (fieldDescription.champSpecial) {
      case "1":
        {
          // tension format XXX
          textInputFormatterList.add(FilteringTextInputFormatter.digitsOnly);
          textInputFormatterList.add(LengthLimitingTextInputFormatter(3));
        }
        break;
      case "2":
        {
          // tension format XXX/XXX
          textInputFormatterList.add(MaskTextInputFormatter(mask: '###/###'));
        }
        break;
      default:
        {}
        break;
    }
  }

  void _setSpecialFieldHint() {
    switch (fieldDescription.champSpecial) {
      case "1":
        {
          // tension format XXX
          hint = "XXX";
        }
        break;
      case "2":
        {
          // tension format XXX/XXX
          hint = "XXX/XXX";
        }
        break;
      default:
        {}
        break;
    }
  }

  dynamic getValueWithType(String value) {
    dynamic val;
    switch (fieldDescription.format) {
      case "\"booleen\"":
        {
          // bool
          val = value.contains("true") || value == "1"
              ? true
              : false; // dans le cas du suivi Greffe, "0" ou "1" est reçu...
        }
        break;
      default:
        {
          // on ne sait pas, je propose de saisir un texte
          val = value;
        }
        break;
    }
    return val;
  }
}
