import 'package:flutter/material.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

class DrugDetails {
  String? nom;
  bool administre;
  String? motifNonAdmin;
  TimeOfDay? heureArret;
  double? nombre;
  String? numLot;
  TimeOfDay? heure;
  double? dose;
  String drugID;
  DateTime? date;

  DrugDetails(
      {this.nom,
      required this.administre,
      this.motifNonAdmin,
      this.heureArret,
      this.nombre,
      this.numLot,
      this.heure,
      this.dose,
      this.drugID = "",
      this.date});

  Map<String, dynamic> toJson() => {
        "administre": administre,
        "motif_non_admin": motifNonAdmin,
        "num_lot": numLot,
        "heure_arret": heureArret!.toLanguageString(),
        "nombre": nombre.toString().replaceAll(".", ","),
        "heure": heure!.toLanguageString(),
        "dose": dose.toString().replaceAll(".", ",")
      };
}
