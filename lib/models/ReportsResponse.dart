import 'dart:convert';
import 'package:theradom/models/report_file.dart';

ReportsResponse reportsResponseFromJson(String str) =>
    ReportsResponse.fromJson(json.decode(str));
String reportsResponseToJson(ReportsResponse data) =>
    json.encode(data.toJson());

class ReportsResponse {
  String valide;
  String? erreur;
  Map<String, ReportFile> fichiers;

  ReportsResponse({required this.fichiers, required this.valide, this.erreur});

  factory ReportsResponse.fromJson(Map<String, dynamic> json) =>
      ReportsResponse(
          fichiers: Map.from(json["fichiers"]).map((k, v) =>
              MapEntry<String, ReportFile>(k, ReportFile.fromJson(v))),
          valide: json["valide"],
          erreur: json["erreur"]);

  Map<String, dynamic> toJson() => {
        "fichiers": Map.from(fichiers)
            .map((k, v) => MapEntry<String, dynamic>(k, v.to<PERSON>son())),
        "valide": valide,
        "erreur": erreur
      };
}
