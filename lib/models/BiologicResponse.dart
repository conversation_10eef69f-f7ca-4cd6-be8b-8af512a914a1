import 'dart:convert';
import 'package:theradom/models/biologic_analysis_data.dart';

BiologicResponse biologicResponseFromJson(String str) =>
    BiologicResponse.fromJson(json.decode(str));
String biologicResponseToJson(BiologicResponse data) =>
    json.encode(data.toJson());

class BiologicResponse {
  Map<String, BiologicAnalysisData> bio;
  String valide;
  String? erreur;

  BiologicResponse({required this.bio, required this.valide, this.erreur});

  factory BiologicResponse.fromJson(Map<String, dynamic> json) =>
      BiologicResponse(
          bio: Map.from(json["Bio"]).map((k, v) =>
              MapEntry<String, BiologicAnalysisData>(
                  k, BiologicAnalysisData.fromJson(v))),
          valide: json["valide"],
          erreur: json["erreur"]);

  Map<String, dynamic> toJson() => {
        "Bio": Map.from(bio)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "valide": valide,
        "erreur": erreur
      };
}
