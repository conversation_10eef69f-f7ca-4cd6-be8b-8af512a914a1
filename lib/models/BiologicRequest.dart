import 'dart:convert';

BiologicRequest biologicRequestFromJson(String str) =>
    BiologicRequest.fromJson(json.decode(str));
String biologicRequestToJson(BiologicRequest data) =>
    json.encode(data.toJson());

class BiologicRequest {
  String numpat;
  String from;
  String to;

  BiologicRequest({required this.numpat, required this.from, required this.to});

  factory BiologicRequest.fromJson(Map<String, dynamic> json) =>
      BiologicRequest(
          numpat: json["NUMPAT"], from: json["from"], to: json["to"]);

  Map<String, dynamic> toJson() => {"NUMPAT": numpat, "from": from, "to": to};
}
