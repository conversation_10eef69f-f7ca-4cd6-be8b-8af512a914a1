import 'dart:convert';

StatusResponse statusResponseFromJson(String str) =>
    StatusResponse.fromJson(json.decode(str));
String statusResponseToJson(StatusResponse data) => json.encode(data.toJson());

class StatusResponse {
  String valide;
  String? erreur;

  StatusResponse({required this.valide, this.erreur});

  factory StatusResponse.fromJson(Map<String, dynamic> json) =>
      StatusResponse(valide: json["valide"], erreur: json['erreur']);

  Map<String, dynamic> toJson() => {"valide": valide, "erreur": erreur};
}
