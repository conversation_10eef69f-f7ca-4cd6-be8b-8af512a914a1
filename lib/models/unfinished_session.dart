class UnfinishedSession {
  String dateSeanceNonFinie;
  String numTypeSuivi;
  String heureSeanceNonFinie;
  String sourceHemadom;

  UnfinishedSession({
    required this.dateSeanceNonFinie,
    required this.numTypeSuivi,
    required this.heureSeanceNonFinie,
    required this.sourceHemadom,
  });

  factory UnfinishedSession.fromJson(Map<String, dynamic> json) =>
      UnfinishedSession(
          dateSeanceNonFinie: json["dateSeance_nonFinie"],
          numTypeSuivi: json["num_type_suivi"],
          heureSeanceNonFinie: json["heureSeance_nonFinie"],
          sourceHemadom: json["sourceHemadom"]);

  Map<String, dynamic> toJson() => {
        "dateSeance_nonFinie": dateSeanceNonFinie,
        "num_type_suivi": numTypeSuivi,
        "heureSeance_nonFinie": heureSeanceNonFinie,
        "sourceHemadom": sourceHemadom
      };
}
