import 'dart:convert';

GetDrugsRequest getDrugsRequestFromJson(String str) =>
    GetDrugsRequest.fromJson(json.decode(str));

String getDrugsRequestToJson(GetDrugsRequest data) =>
    json.encode(data.toJson());

class GetDrugsRequest {
  String numpat;
  String numSeance;
  String suivi;
  String date;

  GetDrugsRequest(
      {required this.numpat,
      required this.numSeance,
      required this.suivi,
      required this.date});

  factory GetDrugsRequest.fromJson(Map<String, dynamic> json) =>
      GetDrugsRequest(
          numpat: json["NUMPAT"],
          numSeance: json["num_seance"],
          suivi: json["suivi"],
          date: json["cDATE"]);

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "num_seance": numSeance,
        "suivi": suivi,
        "cDate": date
      };
}
