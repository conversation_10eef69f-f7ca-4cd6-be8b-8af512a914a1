import 'dart:convert';
import 'package:theradom/models/appointment.dart';

GetAppointmentsResponse getAppointmentsResponseFromJson(String str) =>
    GetAppointmentsResponse.fromJson(json.decode(str));
String getAppointmentsResponseToJson(GetAppointmentsResponse data) =>
    json.encode(data.toJson());

class GetAppointmentsResponse {
  Map<String, Appointment> rdv;
  String valide;
  String? erreur;

  GetAppointmentsResponse(
      {required this.rdv, required this.valide, this.erreur});

  factory GetAppointmentsResponse.fromJson(Map<String, dynamic> json) =>
      GetAppointmentsResponse(
          rdv: Map.from(json["rdv"]).map((k, v) =>
              MapEntry<String, Appointment>(k, Appointment.fromJson(v))),
          valide: json["valide"],
          erreur: json["erreur"]);

  Map<String, dynamic> toJson() => {
        "rdv": Map.from(rdv)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "valide": valide,
        "erreur": erreur
      };
}
