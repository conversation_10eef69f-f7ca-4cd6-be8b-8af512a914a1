import 'dart:convert';
import 'package:theradom/models/PutDataDescription.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';

String putSessionDataToJson(PutSessionData data) => json.encode(data.toJson());
String putSessionDataSessionCreation(PutSessionData data) =>
    json.encode(data.sessionCreationToJson());

class PutSessionData {
  String numpat;
  String suivi;
  String numSeance;
  String dateSeance;
  bool autoSave;

  // optionnels si nouvelle séance
  String? heure;
  bool? normalite;
  String? commentaire;
  List<PutDataDescription>? listPutDataDescriptionUnique;
  bool? finSeance;
  String? heureFin;
  String? heureDeb;
  Map<String, dynamic>? tableau;

  PutSessionData(
      {required this.numpat,
      required this.suivi,
      required this.numSeance,
      required this.dateSeance,
      this.autoSave = false,
      this.heure,
      this.normalite,
      this.commentaire,
      this.listPutDataDescriptionUnique,
      this.finSeance,
      this.heureFin,
      this.heureDeb,
      this.tableau});

  Map<String, dynamic> toJson() {
    if (numSeance == "-1") {
      // création séance
      return {
        "NUMPAT": numpat,
        "suivi": suivi,
        "num_seance": numSeance,
        "date_seance": dateSeance,
      };
    } else {
      // push des data

      // "unique"
      Map unique = Map<String, dynamic>();
      listPutDataDescriptionUnique?.forEach((putDataDescription) {
        unique["${putDataDescription.idBD}"] = putDataDescription.toJson();
      });

      var objJson = {
        "NUMPAT": numpat,
        "suivi": suivi,
        "num_seance": numSeance,
        "date_seance": dateSeance,
        "auto_save": autoSave ? "1" : "0",
        "heure": heure,
        "normalite": normalite,
        "COMMENTAIRE": commentaire,
        "unique": json.decode(json.encode(unique)),
        "tableau": tableau
      };

      if (MonitoringUtils.isMonitoringSession(suivi)) {
        objJson["heure_deb"] = heureDeb;
        if (finSeance!) {
          objJson["fin_seance"] = finSeance;
          objJson["heure_fin"] = heureFin;
        }
      }

      objJson.remove("null");

      return objJson;
    }
  }

  Map<String, dynamic> sessionCreationToJson() => {
        "NUMPAT": numpat,
        "suivi": suivi,
        "num_seance": numSeance,
        "date_seance": dateSeance,
        "auto_save": autoSave
      };
}
