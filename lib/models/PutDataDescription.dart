import 'dart:convert';

String putDataDescriptionToJson(PutDataDescription data) =>
    json.encode(data.toJson());
String putDataDescriptionFromJson(PutDataDescription data) =>
    json.encode(data.toJson());

class PutDataDescription {
  String idBD;
  String libelle;
  dynamic value;

  PutDataDescription(
      {required this.idBD, required this.libelle, required this.value});

  Map<String, dynamic> toJson() =>
      {"libelle": "$libelle", "value": value is String ? "$value" : value};
}
