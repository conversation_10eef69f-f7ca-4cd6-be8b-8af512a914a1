class SessionUniqueData {
  String? heureDeb;
  bool? finSeance;
  String? heureFin;
  String commentaire;
  Map<String, dynamic> fieldID; // les autres champs dynamiques

  SessionUniqueData(
      {required this.heureDeb,
      required this.finSeance,
      required this.heureFin,
      required this.commentaire,
      required this.fieldID});

  factory SessionUniqueData.fromJson(Map<String, dynamic> json) =>
      SessionUniqueData(
          heureDeb: json["heure_deb"],
          finSeance: json["fin_seance"],
          heureFin: json["heure_fin"],
          commentaire: json["COMMENTAIRE"],
          fieldID:
              Map.from(json).map((k, v) => MapEntry<String, dynamic>(k, v)));

  Map<String, dynamic> toJson() => {
        "heure_deb": heureDeb,
        "fin_seance": finSeance,
        "heure_fin": heureFin,
        "COMMENTAIRE": commentaire,
        "fieldID":
            Map.from(fieldID).map((k, v) => MapEntry<String, dynamic>(k, v))
      };
}
