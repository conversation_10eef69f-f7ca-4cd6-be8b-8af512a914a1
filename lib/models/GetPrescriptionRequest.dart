import 'dart:convert';

GetPrescriptionRequest getPrescriptionRequestFromJson(String str) =>
    GetPrescriptionRequest.fromJson(json.decode(str));
String getPrescriptionRequestToJson(GetPrescriptionRequest data) =>
    json.encode(data.toJson());

class GetPrescriptionRequest {
  String numpat;
  String suivi;
  String numSeance;
  String cDate; //YYYYMMDD
  String details;
  String typeRecherche;

  GetPrescriptionRequest(
      {required this.numpat,
      required this.suivi,
      required this.numSeance,
      required this.cDate,
      required this.details,
      required this.typeRecherche});

  factory GetPrescriptionRequest.fromJson(Map<String, dynamic> json) =>
      GetPrescriptionRequest(
        numpat: json["NUMPAT"] == null ? null : json["NUMPAT"],
        suivi: json["suivi"] == null ? null : json["suivi"],
        numSeance: json["num_seance"] == null ? null : json["num_seance"],
        cDate: json["cDATE"] == null ? null : json["cDATE"],
        details: json["details"] == null ? null : json["details"],
        typeRecherche:
            json["typeRecherche"] == null ? null : json["typeRecherche"],
      );

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "suivi": suivi,
        "num_seance": numSeance,
        "cDATE": cDate,
        "details": details,
        "typeRecherche": typeRecherche
      };
}
