class RequestedField {
  List<String>? listFieldId;
  List<String>? listFiledName;

  RequestedField({required this.listFieldId, required this.listFiledName});

  factory RequestedField.fromJson(Map<String, dynamic> json) {
    return RequestedField(
        listFieldId: json.keys.toList(),
        listFiledName: json.values.toList() as List<String>);
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {};
    if (listFieldId != null && listFiledName != null) {
      for (int i = 0; i < listFieldId!.length; i++) {
        String id = listFieldId![i];
        String name = listFiledName![i];
        json[id] = name;
      }
    }
    return json;
  }
}
