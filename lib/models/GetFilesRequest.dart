import 'dart:convert';

GetFilesRequest getFilesRequestFromJson(String str) =>
    GetFilesRequest.fromJson(json.decode(str));
String getFilesRequestToJson(GetFilesRequest data) =>
    json.encode(data.toJson());

class GetFilesRequest {
  String numpat;
  String table;
  String numSeanceMail;
  String liste;
  String pjName;

  GetFilesRequest(
      {required this.numpat,
      required this.table,
      required this.numSeanceMail,
      required this.liste,
      required this.pjName});

  factory GetFilesRequest.fromJson(Map<String, dynamic> json) =>
      GetFilesRequest(
          numpat: json["NUMPAT"],
          table: json["table"],
          numSeanceMail: json["num_seance_mail"],
          liste: json["liste"],
          pjName: json["pjName"]);

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "table": table,
        "num_seance_mail": numSeanceMail,
        "liste": liste,
        "pjName": pjName
      };
}
