import 'dart:convert';
import 'package:theradom/models/file_object.dart';

NewAccountData newAccountDataFromJson(String str) =>
    NewAccountData.fromJson(json.decode(str));
String newAccountDataToJson(NewAccountData data) => json.encode(data.toJson());

class NewAccountData {
  String numpat;
  String option;

  /// option 1 -> changer l'ID du dernier questionnaire d'utilisabilité rempli.
  /// option 2 -> changer l'ID de la dernière page vue an premier
  /// option 3 -> token
  /// option 4 -> HMI user preferences
  String value;
  FileObject? file;

  /// required if option == "1"

  NewAccountData(
      {required this.numpat,
      required this.option,
      required this.value,
      this.file});

  factory NewAccountData.fromJson(Map<String, dynamic> json) => NewAccountData(
      numpat: json["NUMPAT"],
      option: json["option"],
      value: json["value"],
      file: FileObject.fromJson(json["file"]));

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "option": option,
        "value": value,
        if (option == "1") "file": file!.toJson()
      };
}
