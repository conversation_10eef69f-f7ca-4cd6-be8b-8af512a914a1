import 'package:theradom/models/available_field_list.dart';
import 'package:theradom/models/unique_list_types.dart';

class ListAllData {
  UniqueListTypes unique;
  Map<String, AvailableFieldList> table;
  Map<String, String> tags;

  ListAllData({
    required this.unique,
    required this.table,
    required this.tags,
  });

  factory ListAllData.fromJson(Map<String, dynamic> json) => ListAllData(
      unique: UniqueListTypes.fromJson(json["unique"]),
      table: Map.from(json["table"]).map((k, v) =>
          MapEntry<String, AvailableFieldList>(
              k, AvailableFieldList.fromJson(v))),
      tags:
          Map.from(json["tags"]).map((k, v) => MapEntry<String, String>(k, v)));

  Map<String, dynamic> toJson() => {
        "unique": unique.toJson(),
        "table": Map.from(table)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "tags": Map.from(tags).map((k, v) => MapEntry<String, dynamic>(k, v))
      };
}
