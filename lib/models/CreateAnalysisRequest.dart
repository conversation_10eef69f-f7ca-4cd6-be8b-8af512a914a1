import 'dart:convert';

CreateAnalysisRequest createAnalysisRequestFromJson(String str) =>
    CreateAnalysisRequest.fromJSON(json.decode(str));
String createAnalysisRequestToJson(CreateAnalysisRequest data) =>
    json.encode(data.toJson());

class CreateAnalysisRequest {
  String numpat;
  String table;
  String numSeanceMail;

  CreateAnalysisRequest(
      {required this.numpat, required this.table, required this.numSeanceMail});

  factory CreateAnalysisRequest.fromJSON(Map<String, dynamic> json) =>
      CreateAnalysisRequest(
          numpat: json["NUMPAT"],
          table: json["table"],
          numSeanceMail: json["num_seance_mail"]);

  Map<String, dynamic> toJson() =>
      {"NUMPAT": numpat, "table": table, "num_seance_mail": numSeanceMail};
}
