import 'dart:convert';
import 'package:theradom/models/requested_field.dart';

GetPreviousDataRequest getPreviousDataRequestFromJson(String str) =>
    GetPreviousDataRequest.fromJson(json.decode(str));
String getPreviousDataRequestToJson(GetPreviousDataRequest data) =>
    json.encode(data.toJson());

class GetPreviousDataRequest {
  String numpat;
  String suivi;
  bool hasList;
  String? from;
  String? to;
  String type;
  String? format;
  RequestedField champs;

  GetPreviousDataRequest({
    required this.numpat,
    required this.suivi,
    required this.hasList,
    required this.from,
    required this.to,
    required this.type,
    required this.format,
    required this.champs,
  });

  factory GetPreviousDataRequest.fromJson(Map<String, dynamic> json) =>
      GetPreviousDataRequest(
        numpat: json["NUMPAT"],
        suivi: json["suivi"],
        hasList: json["hasList"],
        from: json["from"],
        to: json["to"],
        type: json["type"],
        format: json["format"],
        champs: RequestedField.fromJson(json["champs"]),
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {};
    json["NUMPAT"] = numpat;
    json["suivi"] = suivi;
    json["hasList"] = hasList;
    if (hasList) {
      // hasList vaut true
      json["from"] = from;
      json["to"] = to;
      json["type"] = type;
      if (type == "table") {
        json["format"] = format;
      }
      json["champs"] = champs.toJson();
    }
    return json;
  }
}
