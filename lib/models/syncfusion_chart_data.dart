/// Modèle de données pour les graphiques Syncfusion
class SyncfusionChartData {
  final DateTime x;
  final double y;
  final String? label;

  SyncfusionChartData({
    required this.x,
    required this.y,
    this.label,
  });

  @override
  String toString() {
    return 'SyncfusionChartData(x: $x, y: $y, label: $label)';
  }
}

/// Série de données pour Syncfusion
class SyncfusionChartSeries {
  final String name;
  final List<SyncfusionChartData> data;
  final int colorIndex;

  SyncfusionChartSeries({
    required this.name,
    required this.data,
    required this.colorIndex,
  });

  @override
  String toString() {
    return 'SyncfusionChartSeries(name: $name, dataCount: ${data.length}, colorIndex: $colorIndex)';
  }
}
