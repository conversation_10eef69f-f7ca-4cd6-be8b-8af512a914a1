import 'dart:convert';

StatusMessageRequest getStatusMessageRequest(String str) =>
    StatusMessageRequest.fromJson(json.decode(str));
String getStatusMessageRequestToJson(StatusMessageRequest data) =>
    json.encode(data.toJson());

class StatusMessageRequest {
  String numpat;
  String numMessage;

  StatusMessageRequest({
    required this.numpat,
    required this.numMessage,
  });

  factory StatusMessageRequest.fromJson(Map<String, dynamic> json) =>
      StatusMessageRequest(
        numpat: json["NUMPAT"],
        numMessage: json["num_message"],
      );

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "num_message": numMessage,
      };
}
