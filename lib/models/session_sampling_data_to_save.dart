import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:theradom/extensions/string_extension.dart';
import 'package:theradom/models/session_sampling_data.dart';
import 'package:theradom/models/session_sampling_data_element.dart';
import 'package:theradom/models/session_sampling_data_page.dart';
import 'package:theradom/models/session_sampling_structure.dart';

SessionSamplingDataToSave sessionSamplingDataToSaveFromStructureAndValues(
        SessionSamplingStructure structure, SessionSamplingData values) =>
    SessionSamplingDataToSave.from(structure, values);
String sessionSamplingDataToSaveToJson(SessionSamplingDataToSave data) =>
    json.encode(data.toJson());

class SessionSamplingDataToSave {
  List<SessionSamplingDataPage> sessionSamplingDataPageList;
  SessionSamplingDataPage model;

  SessionSamplingDataToSave(
      {required this.sessionSamplingDataPageList, required this.model});

  void updatePage(int numPage, String id, dynamic value) {
    // change la valeur d'un champ à une page donnée
    int pageIndex = sessionSamplingDataPageList
        .indexWhere((element) => element.numPage == numPage);
    int fieldIndex = sessionSamplingDataPageList[pageIndex]
        .sessionSamplingDataElementList
        .indexWhere((element) => element.id == id);
    sessionSamplingDataPageList[pageIndex]
        .sessionSamplingDataElementList[fieldIndex]
        .value = value;
    sessionSamplingDataPageList[pageIndex].modifDate = DateTime.now();
    sessionSamplingDataPageList[pageIndex].modifTime = TimeOfDay.now();
  }

  void addPage(DateTime date, TimeOfDay time) {
    sessionSamplingDataPageList.add(SessionSamplingDataPage(
        numPage: sessionSamplingDataPageList.length + 1,
        date: date,
        time: time,
        modifDate: date,
        modifTime: time,
        sessionSamplingDataElementList:
            List.generate(model.sessionSamplingDataElementList.length, (index) {
          return SessionSamplingDataElement(
              libelle: model.sessionSamplingDataElementList[index].libelle,
              id: model.sessionSamplingDataElementList[index].id,
              value: "");
        })));
  }

  void removePage(int numPage) {
    int pageIndex = sessionSamplingDataPageList
        .indexWhere((element) => element.numPage == numPage);
    sessionSamplingDataPageList.removeAt(pageIndex);
    _updateAllNumPages();
  }

  void _updateAllNumPages() {
    int i = 1;
    sessionSamplingDataPageList.forEach((element) {
      element.numPage = i;
      i++;
    });
  }

  factory SessionSamplingDataToSave.from(
      SessionSamplingStructure structure, SessionSamplingData values) {
    SessionSamplingDataPage model = SessionSamplingDataPage(
        numPage: 0,
        time: null,
        date: null,
        modifTime: null,
        modifDate: null,
        mode: null,
        sessionSamplingDataElementList:
            List.generate(structure.fieldID.keys.length, (index) {
          return SessionSamplingDataElement(
              id: structure.fieldID.keys.toList()[index],
              libelle: structure.fieldID.values.toList()[index]["libelle"],
              value: null);
        }));
    List<SessionSamplingDataPage> sessionSamplingDataPageList = [];

    values.valueName.forEach((numPage, putDataDescription) {
      SessionSamplingDataPage sessionSamplingDataPage =
          SessionSamplingDataPage(numPage: int.parse(numPage));
      List<SessionSamplingDataElement> sessionSamplingDataElementList =
          List<SessionSamplingDataElement>.filled(
              model.sessionSamplingDataElementList.length,
              SessionSamplingDataElement(id: '', libelle: ''));

      putDataDescription.forEach((libelle, value) {
        switch ("$libelle") {
          case "Hrs":
            {
              int? timestamp = value != "" ? int.parse(value) * 1000 : null;
              DateTime? dateTime = timestamp != null
                  ? DateTime.fromMillisecondsSinceEpoch(timestamp)
                  : null;
              sessionSamplingDataPage.date = dateTime != null
                  ? DateTime(dateTime.year, dateTime.month, dateTime.day)
                  : null;
              sessionSamplingDataPage.time =
                  dateTime != null ? TimeOfDay.fromDateTime(dateTime) : null;
            }
            break;
          case "date_modif":
            {
              sessionSamplingDataPage.modifDate =
                  value != "" ? value.toString().yyyymmdd2DateTime() : null;
            }
            break;
          case "heure_modif":
            {
              sessionSamplingDataPage.modifTime =
                  value != "" ? value.toString().hhmm2TimeOfDay() : null;
            }
            break;
          case "Mode":
            {
              sessionSamplingDataPage.mode = value;
            }
            break;
          default:
            {
              structure.fieldID.forEach((String id, obj) {
                if (obj["libelle"] == libelle) {
                  // si on trouve le libelle
                  int replacementIndex = model.sessionSamplingDataElementList
                      .indexWhere((element) => element.id == id);
                  sessionSamplingDataElementList[replacementIndex] =
                      SessionSamplingDataElement(
                          libelle: libelle,
                          value: value == null ? "" : value,
                          id: id);
                }
              });
            }
            break;
        }
      });
      sessionSamplingDataPage.sessionSamplingDataElementList =
          sessionSamplingDataElementList;
      sessionSamplingDataPageList.add(sessionSamplingDataPage);
    });

    return SessionSamplingDataToSave(
        sessionSamplingDataPageList: sessionSamplingDataPageList, model: model);
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = Map<String, dynamic>();
    sessionSamplingDataPageList.forEach((sessionSamplingDataPage) {
      map["${sessionSamplingDataPage.numPage}"] =
          sessionSamplingDataPage.toJson();
    });
    return map;
  }
}
