import 'dart:convert';
import 'package:theradom/models/list_all_data.dart';

GetPreviousDataResponse getPreviousDataResponseSimpleFromJson(String str) =>
    GetPreviousDataResponse.fromJson(json.decode(str));
String getPreviousDataResponseSimpleToJson(GetPreviousDataResponse data) =>
    json.encode(data.toJson());

class GetPreviousDataResponse {
  String valide;
  String? erreur;
  ListAllData? seance;

  GetPreviousDataResponse(
      {required this.valide, this.erreur, required this.seance});

  factory GetPreviousDataResponse.fromJson(Map<String, dynamic> json) =>
      GetPreviousDataResponse(
          valide: json["valide"],
          erreur: json["erreur"],
          seance: json["Seance"] == ""
              ? null
              : ListAllData.fromJson(json["Seance"]));

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "Seance": seance == null ? null : seance!.toJson()
      };
}
