import 'package:theradom/models/doc.dart';

class ReportFile {
  Map<String, Doc> hebdo;

  ReportFile({
    required this.hebdo,
  });

  factory ReportFile.fromJson(Map<String, dynamic> json) => ReportFile(
      hebdo: Map.from(json["hebdo"])
          .map((k, v) => MapEntry<String, Doc>(k, Doc.fromJson(v))));

  Map<String, dynamic> toJson() => {
        "hebdo": Map.from(hebdo)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson()))
      };
}
