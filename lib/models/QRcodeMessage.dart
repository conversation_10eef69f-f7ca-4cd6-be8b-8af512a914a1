import 'dart:convert';

QRcodeMessage qrCodeMessagefromJson(String str) =>
    QRcodeMessage.fromJson(json.decode(str));
String qrCodeMessageToJson(QRcodeMessage data) => json.encode(data.toJson());

class QRcodeMessage {
  String numpat;
  String nom;
  String prenom;
  String login;
  String mail;
  String password;
  String INS;
  String domainName;

  QRcodeMessage(
      {required this.numpat,
      required this.nom,
      required this.prenom,
      required this.login,
      required this.password,
      required this.mail,
      required this.INS,
      required this.domainName});

  factory QRcodeMessage.fromJson(Map<String, dynamic> json) => QRcodeMessage(
      nom: json["name"],
      prenom: json["firstname"],
      login: json["login"],
      password: json["password"],
      mail: json["email"],
      INS: json["INS"],
      domainName: json['url'],
      numpat: json['id']);

  Map<String, dynamic> to<PERSON>son() => {
        "id": numpat,
        "name": nom,
        "ins": INS,
        "password": password,
        "email": mail,
        "firstname": prenom,
        "login": login,
        "url": domainName,
      };
}
