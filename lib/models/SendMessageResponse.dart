import 'dart:convert';

SendMessageResponse sendMessageResponseFromJson(String str) =>
    SendMessageResponse.fromJson(json.decode(str));
String sendMessageResponseToJson(SendMessageResponse data) =>
    json.encode(data.toJson());

class SendMessageResponse {
  String valide;
  String? erreur;
  Map<String, String> numMailMsg;
  Map<String, String> numMailCal;

  SendMessageResponse(
      {required this.valide,
      this.erreur,
      required this.numMailMsg,
      required this.numMailCal});

  factory SendMessageResponse.fromJson(Map<String, dynamic> json) =>
      SendMessageResponse(
          valide: json["valide"],
          erreur: json["erreur"],
          numMailMsg: Map.from(json["num_mail_msg"])
              .map((k, v) => MapEntry<String, String>(k, v)),
          numMailCal: Map.from(json["num_mail_cal"])
              .map((k, v) => MapEntry<String, String>(k, v)));

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "num_mail_msg":
            Map.from(numMailMsg).map((k, v) => MapEntry<String, dynamic>(k, v)),
        "num_mail_cal":
            Map.from(numMailCal).map((k, v) => MapEntry<String, dynamic>(k, v))
      };
}
