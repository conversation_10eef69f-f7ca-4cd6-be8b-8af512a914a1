import 'dart:convert';

UserEmail userEmailFromJson(String str) => UserEmail.fromJson(json.decode(str));
String userEmailToJson(UserEmail data) => json.encode(data.toJson());

class UserEmail {
  String email;

  UserEmail({
    required this.email,
  });

  factory UserEmail.fromJson(Map<String, dynamic> json) =>
      UserEmail(email: json["email"]);

  Map<String, dynamic> toJson() => {"email": email};
}
