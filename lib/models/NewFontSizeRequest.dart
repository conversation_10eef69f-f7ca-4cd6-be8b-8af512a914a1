import 'dart:convert';

NewFontSizeRequest newFontSizeRequestFromJson(String str) =>
    NewFontSizeRequest.fromJson(json.decode(str));
String newFontSizeRequestToJson(NewFontSizeRequest data) =>
    json.encode(data.toJson());

class NewFontSizeRequest {
  String numpat;
  int taillePolice;

  NewFontSizeRequest({required this.numpat, required this.taillePolice});

  factory NewFontSizeRequest.fromJson(Map<String, dynamic> json) =>
      NewFontSizeRequest(
          numpat: json["NUMPAT"], taillePolice: json["taille_police"]);

  Map<String, dynamic> toJson() =>
      {"NUMPAT": numpat, "taille_police": taillePolice};
}
