class SessionUniqueStructure {
  Map<String, dynamic> areaSructure;
  Map<String, dynamic> internal;

  SessionUniqueStructure({required this.internal, required this.areaSructure});

  factory SessionUniqueStructure.fromJson(Map<String, dynamic> json) =>
      SessionUniqueStructure(
          areaSructure:
              Map.from(json).map((k, v) => MapEntry<String, dynamic>(k, v)),
          internal: Map.from(json["internal"])
              .map((k, v) => MapEntry<String, dynamic>(k, v)));

  Map<String, dynamic> toJson() => {
        "areaStructure": Map.from(areaSructure)
            .map((k, v) => MapEntry<String, dynamic>(k, v)),
        "internal":
            Map.from(internal).map((k, v) => MapEntry<String, dynamic>(k, v))
      };
}
