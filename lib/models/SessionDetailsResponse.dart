import 'dart:convert';
import 'package:theradom/models/session_details.dart';
import 'package:theradom/extensions/string_extension.dart';

SessionDetailsResponse sessionDetailsResponseFromJson(String str) =>
    SessionDetailsResponse.fromJson(json.decode(str));
String sessionDetailsResponseToJson(SessionDetailsResponse data) =>
    json.encode(data.toJson());

class SessionDetailsResponse {
  String valide;
  String? erreur;
  DateTime dateSeance;
  bool hemabox;
  String idSeance;
  SessionDetails seance;

  SessionDetailsResponse(
      {required this.valide,
      this.erreur,
      required this.dateSeance,
      required this.hemabox,
      required this.idSeance,
      required this.seance});

  factory SessionDetailsResponse.fromJson(Map<String, dynamic> json) =>
      SessionDetailsResponse(
          valide: json["valide"],
          erreur: json["erreur"],
          dateSeance: json["date_seance"].toString().yyyymmdd2DateTime(),
          hemabox: json["hemabox"],
          idSeance: json["id_seance"],
          seance: SessionDetails.fromJson(json["Seance"]));

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "date_seance": dateSeance,
        "hemabox": hemabox,
        "id_seance": idSeance,
        "Seance": seance.toJson()
      };
}
