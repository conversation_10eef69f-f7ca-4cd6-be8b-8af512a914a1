import 'dart:convert';

TOURequest touRequestFromJson(String str) =>
    TOURequest.fromJson(json.decode(str));
String touRequestToJson(TOURequest data) => json.encode(data.toJson());

class TOURequest {
  String numpat;
  bool accordCGU;

  TOURequest({required this.numpat, required this.accordCGU});

  factory TOURequest.fromJson(Map<String, dynamic> json) =>
      TOURequest(numpat: json["NUMPAT"], accordCGU: json["accord_CGU"]);

  Map<String, dynamic> toJson() => {"NUMPAT": numpat, "accord_CGU": accordCGU};
}
