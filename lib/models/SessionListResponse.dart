import 'dart:convert';
import 'package:theradom/models/seance_precedente.dart';

SessionListResponse sessionListResponseFromJson(String str) =>
    SessionListResponse.fromJson(json.decode(str));

String sessionListResponseToJson(SessionListResponse data) =>
    json.encode(data.toJson());

class SessionListResponse {
  String valide;
  String? erreur;
  Map<String, SeancePrecedente> seancesPrecedentes;

  SessionListResponse(
      {required this.valide, this.erreur, required this.seancesPrecedentes});

  factory SessionListResponse.fromJson(Map<String, dynamic> json) =>
      SessionListResponse(
          seancesPrecedentes: Map.from(json["seances_precedentes"]).map(
              (k, v) => MapEntry<String, SeancePrecedente>(
                  k, SeancePrecedente.fromJson(v))),
          valide: json["valide"],
          erreur: json['erreur']);

  Map<String, dynamic> toJson() => {
        "seances_precedentes": Map.from(seancesPrecedentes)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "valide": valide,
        "erreur": erreur
      };
}
