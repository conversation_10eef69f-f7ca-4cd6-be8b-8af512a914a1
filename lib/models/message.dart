import 'package:theradom/utils/html_helper.dart';

class Message {
  String zDate;
  String zheure;
  bool dejaLu;
  String emetteur;
  String destinataire;
  String motif;
  String numEnregMessagerie;
  String zMessage;
  String typeMessage;

  static HtmlHelper _htmlHelper = HtmlHelper();

  Message(
      {required this.zDate,
      required this.zheure,
      required this.dejaLu,
      required this.emetteur,
      required this.destinataire,
      required this.motif,
      required this.numEnregMessagerie,
      required this.zMessage,
      required this.typeMessage});

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
        zDate: json["zDATE"],
        zheure: json["zheure"],
        dejaLu: json["deja_lu"],
        emetteur: _htmlHelper.convert(json["emetteur"]),
        destinataire: _htmlHelper.convert(json["destinataire"]),
        motif: _htmlHelper.convert(json["motif"]),
        numEnregMessagerie: json["num_enreg_messagerie"],
        zMessage: _htmlHelper.convert(json["zMESSAGE"]),
        typeMessage: json["type_message"]);
  }

  Map<String, dynamic> toJson() => {
        "zDATE": zDate,
        "zheure": zheure,
        "deja_lu": dejaLu,
        "emetteur": emetteur,
        "destinataire": destinataire,
        "motif": motif,
        "num_enreg_messagerie": numEnregMessagerie,
        "zMESSAGE": zMessage,
        "type_message": typeMessage
      };
}
