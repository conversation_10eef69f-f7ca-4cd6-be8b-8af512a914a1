import 'package:theradom/models/available_field_list.dart';

class UniqueListTypes {
  Map<String, AvailableFieldList> received;
  Map<String, AvailableFieldList> basics;

  UniqueListTypes({
    required this.received,
    required this.basics,
  });

  factory UniqueListTypes.fromJson(Map<String, dynamic> json) =>
      UniqueListTypes(
          received: Map.from(json["received"]).map((k, v) =>
              MapEntry<String, AvailableFieldList>(
                  k, AvailableFieldList.fromJson(v))),
          basics: Map.from(json["basics"]).map((k, v) =>
              MapEntry<String, AvailableFieldList>(
                  k, AvailableFieldList.fromJson(v))));

  Map<String, dynamic> toJson() => {
        "received": Map.from(received)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "basics": Map.from(basics)
            .map((k, v) => MapEntry<String, dynamic>(k, v.to<PERSON><PERSON>()))
      };
}
