import 'dart:convert';

SavedMessageData savedMessageDataFromJson(String str) =>
    SavedMessageData.fromJson(json.decode(str));
String savedMessageDataToJson(SavedMessageData data) =>
    json.encode(data.toJson());

class SavedMessageData {
  String destinataireAffichage;
  String destinataireMessage;
  String objet;
  String contenu;

  SavedMessageData(
      {required this.destinataireAffichage,
      required this.destinataireMessage,
      required this.objet,
      required this.contenu});

  factory SavedMessageData.fromJson(Map<String, dynamic> json) =>
      SavedMessageData(
          destinataireAffichage: json["destinataireAffichage"],
          destinataireMessage: json["destinataireMessage"],
          objet: json["objet"],
          contenu: json["contenu"]);

  Map<String, dynamic> toJson() => {
        "destinataireAffichage": destinataireAffichage,
        "destinataireMessage": destinataireMessage,
        "objet": objet,
        "contenu": contenu
      };
}
