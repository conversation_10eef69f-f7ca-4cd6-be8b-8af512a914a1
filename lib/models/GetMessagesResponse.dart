import 'dart:convert';
import 'package:theradom/models/message.dart';

GetMessagesResponse getMessagesResponseFromJson(String str) =>
    GetMessagesResponse.fromJson(json.decode(str));
String getMessagesResponseToJson(GetMessagesResponse data) =>
    json.encode(data.toJson());

class GetMessagesResponse {
  String valide;
  String? erreur;
  int totalAvailable;
  Map<String, Message> messages;

  GetMessagesResponse({
    required this.valide,
    this.erreur,
    required this.totalAvailable,
    required this.messages,
  });

  factory GetMessagesResponse.fromJson(Map<String, dynamic> json) =>
      GetMessagesResponse(
          valide: json["valide"],
          erreur: json['erreur'],
          totalAvailable: int.parse(json["totalAvailable"]),
          messages: Map.from(json["messages"]).map(
              (k, v) => MapEntry<String, Message>(k, Message.fromJson(v))));

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "totalAvalable": totalAvailable.toString(),
        "messages": Map.from(messages)
            .map((k, v) => MapEntry<String, dynamic>(k, v.toJson()))
      };
}
