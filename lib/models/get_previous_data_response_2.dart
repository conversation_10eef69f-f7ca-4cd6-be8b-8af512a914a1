import 'dart:convert';

GetPreviousDataResponse2 getPreviousDataResponseTableFromJson(String str) =>
    GetPreviousDataResponse2.fromJson(json.decode(str));
String getPreviousDataResponseTableToJson(GetPreviousDataResponse2 data) =>
    json.encode(data.toJson());

class GetPreviousDataResponse2 {
  Map<String, Map<String, String>>? seance;
  String valide;
  String? erreur;

  GetPreviousDataResponse2(
      {required this.seance, required this.valide, this.erreur});

  factory GetPreviousDataResponse2.fromJson(Map<String, dynamic> json) =>
      GetPreviousDataResponse2(
          seance: json["Seance"] == ""
              ? null
              : Map.from(json["Seance"]).map((k, v) =>
                  MapEntry<String, Map<String, String>>(
                      k,
                      Map.from(v)
                          .map((k, v) => MapEntry<String, String>(k, v)))),
          valide: json["valide"],
          erreur: json["erreur"]);

  Map<String, dynamic> toJson() => {
        "Seance": seance == null
            ? ""
            : Map.from(seance!).map((k, v) => MapEntry<String, dynamic>(
                k, Map.from(v).map((k, v) => MapEntry<String, dynamic>(k, v)))),
        "valide": valide,
        "erreur": erreur
      };
}
