import 'dart:convert';

SessionType sessionTypeFromJson(String str) =>
    SessionType.fromJson(json.decode(str));
String sessionTypeToJson(SessionType data) => json.encode(data.toJson());

class SessionType {
  String numpat;
  String suivi;

  SessionType({required this.numpat, required this.suivi});

  factory SessionType.fromJson(Map<String, dynamic> json) => SessionType(
        numpat: json["NUMPAT"],
        suivi: json["suivi"],
      );

  toJson() {
    return {"NUMPAT": numpat, "suivi": suivi};
  }
}
