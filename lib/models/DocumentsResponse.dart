import 'dart:convert';
import 'package:theradom/models/doc.dart';

DocumentsResponse documentsResponseFromJson(String str) =>
    DocumentsResponse.fromJson(json.decode(str));
String documentsResponseToJson(DocumentsResponse data) =>
    json.encode(data.toJson());

class DocumentsResponse {
  String valide;
  String? erreur;
  Map<String, Map<String, Doc>> fichiers;

  DocumentsResponse(
      {required this.valide, this.erreur, required this.fichiers});

  factory DocumentsResponse.fromJson(Map<String, dynamic> json) =>
      DocumentsResponse(
        valide: json["valide"] == null ? null : json["valide"],
        erreur: json["erreur"] == null ? null : json["erreur"],
        fichiers: Map.from(json["fichiers"]).map((k, v) =>
            MapEntry<String, Map<String, Doc>>(
                k,
                Map.from(v)
                    .map((k, v) => MapEntry<String, Doc>(k, Doc.fromJson(v))))),
      );

  Map<String, dynamic> toJson() => {
        "valide": valide,
        "erreur": erreur,
        "fichiers": Map.from(fichiers).map((k, v) => MapEntry<String, dynamic>(
            k,
            Map.from(v)
                .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())))),
      };
}
