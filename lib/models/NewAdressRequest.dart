import 'dart:convert';

NewAdressRequest newAdressRequestFromJson(String str) =>
    NewAdressRequest.fromJson(json.decode(str));
String newAdressRequestToJson(NewAdressRequest data) =>
    json.encode(data.toJson());

class NewAdressRequest {
  String numpat;
  String newMail;

  NewAdressRequest({required this.numpat, required this.newMail});

  factory NewAdressRequest.fromJson(Map<String, dynamic> json) =>
      NewAdressRequest(numpat: json["NUMPAT"], newMail: json["new_mail"]);

  Map<String, dynamic> toJson() => {"NUMPAT": numpat, "new_mail": newMail};
}
