import 'dart:convert';

FieldDescription fieldDescriptionFromJson(String str) =>
    FieldDescription.fromJson(json.decode(str));
String fieldDescriptionToJson(FieldDescription data) =>
    json.encode(data.toJson());

class FieldDescription {
  String libelle;
  String typeAlerte;
  dynamic valeur; // "0": "azery" ou ListAlarmValues
  String typeOperateur;
  String idBD;
  String champSpecial;
  String order;
  String zone;
  String format; // String or ListValues
  String unite;

  FieldDescription(
      {required this.libelle,
      required this.typeAlerte,
      this.valeur,
      required this.typeOperateur,
      required this.idBD,
      required this.champSpecial,
      required this.order,
      required this.zone,
      required this.format,
      required this.unite});

  factory FieldDescription.fromJson(Map<String, dynamic> json) =>
      FieldDescription(
          libelle: json["libelle"] != null ? json["libelle"] : "",
          typeAlerte:
              json["typealerte"] != null ? json["typealerte"].toString() : "",
          valeur: json["valeur"] != null ? json["valeur"] : "",
          typeOperateur:
              json["typeoperateur"] != null ? json["typeoperateur"] : "",
          idBD: json["id_BD"] != null ? json["id_BD"] : "",
          champSpecial:
              json["champSpecial"] != null ? json["champSpecial"] : "",
          order: json["order"] != null ? json["order"] : "",
          zone: json["zone"] != null ? json["zone"] : "",
          format: jsonEncode(json["format"]),
          unite: json["unite"] != null ? json["unite"] : "");

  Map<String, dynamic> toJson() => {
        "libelle": libelle,
        "typealerte": typeAlerte,
        "valeur": valeur,
        "typeoperateur": typeOperateur,
        "id-BD": idBD,
        "champSpecial": champSpecial,
        "order": order,
        "zone": zone,
        "format": format,
        "unite": unite
      };
}
