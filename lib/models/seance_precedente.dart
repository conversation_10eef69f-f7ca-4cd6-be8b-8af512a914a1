import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/extensions/string_extension.dart';

class SeancePrecedente {
  String numSeance;
  DateTime dateSeance;
  bool verrou;
  bool? termine;

  SeancePrecedente(
      {required this.numSeance,
      required this.dateSeance,
      required this.verrou,
      this.termine});

  factory SeancePrecedente.fromJson(Map<String, dynamic> json) => SeancePrecedente(
      numSeance: json["num_seance"],
      dateSeance: json["date_seance"].toString().yyyymmdd2DateTime(),
      verrou: json["verrou"],
      // Vdu : une séance précédente verrouillée est considérée terminée, si elle est unlocked, vérifier le champ "termine" le cas échéant
      termine: (json["verrou"] || (json["termine"] ?? false)));

  Map<String, dynamic> toJson() => {
        "num_seance": numSeance,
        "date_seance": dateSeance.toYYYYMMDD(),
        "verrou": verrou,
        "termine": (!verrou && termine!)
      };
}
