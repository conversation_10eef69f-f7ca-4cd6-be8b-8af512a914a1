import 'dart:convert';

GetMessagesRequest getMessagesRequestFromJson(String str) =>
    GetMessagesRequest.fromJSON(json.decode(str));
String getMessagesRequestToJson(GetMessagesRequest data) =>
    json.encode(data.toJson());

class GetMessagesRequest {
  String numpat;
  String option;
  int from;
  int to;

  GetMessagesRequest(
      {required this.numpat,
      required this.option,
      required this.from,
      required this.to});

  factory GetMessagesRequest.fromJSON(Map<String, dynamic> json) =>
      GetMessagesRequest(
          numpat: json["NUMPAT"],
          option: json["option"],
          from: json["from"],
          to: json["to"]);

  Map<String, dynamic> toJson() =>
      {"NUMPAT": numpat, "option": option, "from": from, "to": to};
}
