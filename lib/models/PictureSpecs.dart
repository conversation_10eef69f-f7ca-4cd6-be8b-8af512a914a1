import 'dart:convert';

PictureSpecs pictureSpecsFromJson(String str) =>
    PictureSpecs.fromJson(json.decode(str));
String pictureSpecsToJson(PictureSpecs data) => json.encode(data.toJson());

class PictureSpecs {
  String numpat;
  String color1;
  String color2;
  String color3;
  String width;
  String height;
  String fontSize;

  PictureSpecs(
      {required this.numpat,
      required this.color1,
      required this.color2,
      required this.color3,
      required this.width,
      required this.height,
      required this.fontSize});

  factory PictureSpecs.fromJson(Map<String, dynamic> json) => PictureSpecs(
      numpat: json["NUMPAT"],
      color1: json["color1"],
      color2: json["color2"],
      color3: json["color3"],
      width: json["width"],
      height: json["height"],
      fontSize: json["fontSize"]);

  Map<String, dynamic> toJson() => {
        "NUMPAT": numpat,
        "color1": color1,
        "color2": color2,
        "color3": color3,
        "width": width,
        "height": height,
        "fontSize": fontSize
      };
}
