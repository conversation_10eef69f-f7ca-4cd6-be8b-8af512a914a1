import 'dart:convert';

CreateAnalysisResponse createAnalysisResponseFromJson(String str) =>
    CreateAnalysisResponse.fromJSON(json.decode(str));
String createAnalysisResponseToJson(CreateAnalysisResponse data) =>
    json.encode(data.toJson());

class CreateAnalysisResponse {
  String valide;
  String? erreur;
  String chemin;

  CreateAnalysisResponse(
      {required this.valide, this.erreur, required this.chemin});

  factory CreateAnalysisResponse.fromJSON(Map<String, dynamic> json) =>
      CreateAnalysisResponse(
          valide: json["valide"],
          erreur: json["erreur"],
          chemin: json["chemin"]??"");

  Map<String, dynamic> toJson() =>
      {"valide": valide, "erreur": erreur, "chemin": chemin};
}
