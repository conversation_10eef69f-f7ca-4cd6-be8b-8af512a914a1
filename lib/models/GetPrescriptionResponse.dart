import 'dart:convert';
import 'package:theradom/models/drug_short.dart';

GetPrescriptionResponse getPrescriptionResponseFromJson(String str) =>
    GetPrescriptionResponse.fromJson(json.decode(str));

class GetPrescriptionResponse {
  String valide;
  String? erreur;
  List<DrugShort> traitements;
  dynamic prescription;

  GetPrescriptionResponse({
    required this.valide,
    this.erreur,
    required this.traitements,
    required this.prescription,
  });

  factory GetPrescriptionResponse.fromJson(Map<String, dynamic> json) {
    List<DrugShort> drugShortList = [];
    (json["traitements"] ?? {})
        .forEach((k, v) => drugShortList.add(DrugShort.fromJson(v)));
    return GetPrescriptionResponse(
        valide: json['valide'],
        erreur: json['erreur'],
        traitements: drugShortList,
        prescription: json['prescription'] ?? {});
  }
}
