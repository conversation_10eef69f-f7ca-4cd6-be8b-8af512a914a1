import 'package:flutter/material.dart';
import 'package:theradom/extensions/string_extension.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/extensions/timeofday_extension.dart';
import 'package:theradom/utils/Translations.dart';

class Appointment {
  DateTime date;
  TimeOfDay time;
  String titre;
  String commentaire;
  String ctype;
  bool delivre;
  String borneInf;
  String cleFac;
  bool messDelivrer;
  String borneSup;
  bool teleconsultation;

  Appointment(
      {required this.date,
      required this.time,
      required this.titre,
      required this.commentaire,
      required this.ctype,
      required this.delivre,
      required this.borneInf,
      required this.cleFac,
      required this.messDelivrer,
      required this.borneSup,
      required this.teleconsultation});

  factory Appointment.fromJson(Map<String, dynamic> json) => Appointment(
      date: json["DATU"].toString().yyyymmdd2DateTime(),
      time: json["cHEURE"].toString().hhmm2TimeOfDay(),
      titre: json["TITRE"] == "" ? "RDV" : json["TITRE"],
      commentaire: json["COMMENTAIRE"],
      ctype: json["ctype"]
          .toString()
          .substring(json["ctype"].toString().length - 1),
      delivre: json["delivre"],
      borneInf: json["borne_inf"],
      cleFac: json["CLE_FAC"],
      messDelivrer: json["mess_delivrer"],
      borneSup: json["borne_sup"],
      teleconsultation: json["teleconsultation"]);

  Map<String, dynamic> toJson() => {
        "DATU": date.toYYYYMMDD(),
        "cHEURE": "${time.hour}:${time.minute}",
        "TITRE": titre,
        "COMMENTAIRE": commentaire,
        "ctype": ctype,
        "delivre": delivre,
        "borne_inf": borneInf,
        "CLE_FAC": cleFac,
        "mess_delivrer": messDelivrer,
        "borne_sup": borneSup,
        "teleconsultation": teleconsultation
      };

  @override
  toString() {
    return "$commentaire\n${allTranslations.text('on')} "
        "${date.toLanguageString()} ${allTranslations.text('at', {}, true)} "
        "${time.toLanguageString()}";
  }
}
