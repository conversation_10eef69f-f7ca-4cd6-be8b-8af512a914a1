import 'dart:convert';

GetAppointmentsRequest getAppointmentsRequestFromJson(String str) =>
    GetAppointmentsRequest.fromJson(json.decode(str));
String getAppointmentsRequestToJson(GetAppointmentsRequest data) =>
    json.encode(data.toJson());

class GetAppointmentsRequest {
  String numpat;
  String from;
  String to;

  GetAppointmentsRequest(
      {required this.numpat, required this.from, required this.to});

  factory GetAppointmentsRequest.fromJson(Map<String, dynamic> json) =>
      GetAppointmentsRequest(
        to: json["to"],
        numpat: json["NUMPAT"],
        from: json["from"],
      );

  toJson() {
    return {"NUMPAT": numpat, "from": from, "to": to};
  }
}
