// GENERATED CODE - DO NOT MODIFY BY HAND
 
part of 'config.dart';
 
// **************************************************************************
// EnviedGenerator
// **************************************************************************
 
class _Config {
  static const List<int> _enviedkeywsUser = [
    4013579870,
    4069238297,
    95188414,
    2640072337,
    732434635,
    1614940917,
    3258081788,
    2655709161,
    856220480
  ];
  static const List<int> _envieddatawsUser = [
    4013579803,
    4069238356,
    95188479,
    2640072390,
    732434616,
    1614940832,
    3258081679,
    2655709068,
    856220466
  ];
  static final String wsUser = String.fromCharCodes(
    List.generate(_envieddatawsUser.length, (i) => i, growable: false)
        .map((i) => _envieddatawsUser[i] ^ _enviedkeywsUser[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeywsPassword = [
    3352733701,
    504615725,
    355524631,
    4120035647,
    2826309175,
    576226850,
    994155285,
    3704364027,
    3820982672,
    1587064866,
    1958726227,
    297787878,
    2919221486,
    2152480936,
    2919689296,
    4024781945
  ];
  static const List<int> _envieddatawsPassword = [
    3352733773,
    504615788,
    355524730,
    4120035678,
    2826309203,
    576226891,
    994155380,
    3704363927,
    3820982761,
    1587064913,
    1958726198,
    297787860,
    2919221470,
    2152480921,
    2919689313,
    4024781912
  ];
  static final String wsPassword = String.fromCharCodes(
    List.generate(_envieddatawsPassword.length, (i) => i, growable: false)
        .map((i) => _envieddatawsPassword[i] ^ _enviedkeywsPassword[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeyaesSecretKey = [
    3782541441,
    1212918715,
    2775435366,
    128948025,
    1883640421,
    3607166110,
    3479670683,
    3565507834,
    2991315081,
    3387062478,
    2980060112,
    1020142135,
    583394375,
    1431228611,
    1402151707,
    3842330445
  ];
  static const List<int> _envieddataaesSecretKey = [
    3782541553,
    1212918746,
    2775435285,
    128948042,
    1883640338,
    3607166193,
    3479670761,
    3565507742,
    2991315128,
    3387062520,
    2980060067,
    1020142158,
    583394346,
    1431228577,
    1402151796,
    3842330401
  ];
  static final String aesSecretKey = String.fromCharCodes(
    List.generate(_envieddataaesSecretKey.length, (i) => i, growable: false)
        .map((i) => _envieddataaesSecretKey[i] ^ _enviedkeyaesSecretKey[i])
        .toList(growable: false),
  );
  static final int _enviedkeyaValue = 230354050;
  static final int aValue = _enviedkeyaValue ^ 230353288;
  static final int _enviedkeybValue = 4074511866;
  static final int bValue = _enviedkeybValue ^ 4074511865;
  static const List<int> _enviedkeyidDouleur = [
    3816001902,
    1438052372,
    1937274777,
    3043600893,
    1340851629
  ];
  static const List<int> _envieddataidDouleur = [
    3816001887,
    1438052391,
    1937274799,
    3043600802,
    1340851611
  ];
  static final String idDouleur = String.fromCharCodes(
    List.generate(_envieddataidDouleur.length, (i) => i, growable: false)
        .map((i) => _envieddataidDouleur[i] ^ _enviedkeyidDouleur[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeytableFakeRapports = [3143160359, 1310013771];
  static const List<int> _envieddatatableFakeRapports = [
    3143160330,
    1310013818
  ];
  static final String tableFakeRapports = String.fromCharCodes(
    List.generate(_envieddatatableFakeRapports.length, (i) => i,
            growable: false)
        .map((i) =>
            _envieddatatableFakeRapports[i] ^ _enviedkeytableFakeRapports[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeytableFakeDocs = [1431063526, 3096650387];
  static const List<int> _envieddatatableFakeDocs = [1431063499, 3096650401];
  static final String tableFakeDocs = String.fromCharCodes(
    List.generate(_envieddatatableFakeDocs.length, (i) => i, growable: false)
        .map((i) => _envieddatatableFakeDocs[i] ^ _enviedkeytableFakeDocs[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeytableBiologicAnalysis = [
    1247108026,
    4135088046
  ];
  static const List<int> _envieddatatableBiologicAnalysis = [
    1247107982,
    4135088029
  ];
  static final String tableBiologicAnalysis = String.fromCharCodes(
    List.generate(_envieddatatableBiologicAnalysis.length, (i) => i,
            growable: false)
        .map((i) =>
            _envieddatatableBiologicAnalysis[i] ^
            _enviedkeytableBiologicAnalysis[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeytableNephrologyConsultation = [
    2941468610,
    4229713768
  ];
  static const List<int> _envieddatatableNephrologyConsultation = [
    2941468656,
    4229713752
  ];
  static final String tableNephrologyConsultation = String.fromCharCodes(
    List.generate(_envieddatatableNephrologyConsultation.length, (i) => i,
            growable: false)
        .map((i) =>
            _envieddatatableNephrologyConsultation[i] ^
            _enviedkeytableNephrologyConsultation[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeytablePostTransplantConsultation = [
    194421581,
    1516285127
  ];
  static const List<int> _envieddatatablePostTransplantConsultation = [
    194421627,
    1516285183
  ];
  static final String tablePostTransplantConsultation = String.fromCharCodes(
    List.generate(_envieddatatablePostTransplantConsultation.length, (i) => i,
            growable: false)
        .map((i) =>
            _envieddatatablePostTransplantConsultation[i] ^
            _enviedkeytablePostTransplantConsultation[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeytableDialysisSession = [
    108616551,
    600331188
  ];
  static const List<int> _envieddatatableDialysisSession = [
    108616532,
    600331148
  ];
  static final String tableDialysisSession = String.fromCharCodes(
    List.generate(_envieddatatableDialysisSession.length, (i) => i,
            growable: false)
        .map((i) =>
            _envieddatatableDialysisSession[i] ^
            _enviedkeytableDialysisSession[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeytableMailing = [
    1287111452,
    88601574,
    3497843064
  ];
  static const List<int> _envieddatatableMailing = [
    1287111469,
    88601557,
    3497843008
  ];
  static final String tableMailing = String.fromCharCodes(
    List.generate(_envieddatatableMailing.length, (i) => i, growable: false)
        .map((i) => _envieddatatableMailing[i] ^ _enviedkeytableMailing[i])
        .toList(growable: false),
  );
  static const List<int> _enviedkeyevalFormReceptionAddress = [
    928632305,
    188710671,
    786931430,
    1883089296,
    12343920,
    681783195,
    1042084795,
    128649496,
    3814877263,
    761111065,
    2278220820,
    2066650027,
    3699652230,
    3761585835,
    103009303,
    1738185748,
    3248201727,
    3275118629,
    679001677,
    797798718,
    1056283419,
    3087907464,
    1758285320
  ];
  static const List<int> _envieddataevalFormReceptionAddress = [
    928632192,
    188710778,
    786931335,
    1883089404,
    12343833,
    681783279,
    1042084830,
    128649560,
    3814877223,
    761111164,
    2278220921,
    2066650058,
    3699652322,
    3761585858,
    103009398,
    1738185848,
    3248201606,
    3275118678,
    679001640,
    797798672,
    1056283512,
    3087907559,
    1758285413
  ];
  static final String evalFormReceptionAddress = String.fromCharCodes(
    List.generate(_envieddataevalFormReceptionAddress.length, (i) => i,
            growable: false)
        .map((i) =>
            _envieddataevalFormReceptionAddress[i] ^
            _enviedkeyevalFormReceptionAddress[i])
        .toList(growable: false),
  );
}
 