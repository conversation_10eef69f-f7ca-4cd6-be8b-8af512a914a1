/// Classe de constantes pour les codes d'erreur de l'application.
/// 
/// Cette classe centralise tous les codes d'erreur retournés par le backend.
/// Les codes sont au format "XXX" où XXX est un nombre à 3 chiffres.
/// Ces codes sont utilisés dans les messages d'erreur au format "code|description"
/// et sont traduits via les clés '_Error4D_code' dans les fichiers de traduction.
class ErrorCodes {
  /// Codes d'erreur liés à l'authentification et aux credentials (001-012)
  static const String LOGIN_EMPTY = "001";           // Login vide
  static const String PASSWORD_EMPTY = "002";        // Mot de passe vide
  static const String OPTION_EMPTY = "003";          // Option vide
  static const String INVALID_CREDENTIALS = "004";   // Login ou mot de passe incorrect
  static const String UNKNOWN_OPTION = "005";        // Option inconnue
  static const String EMAIL_UNKNOWN = "006";         // Email non trouvé ou non renseigné
  static const String PHP_ERROR = "007";             // Erreur PHP
  static const String INCORRECT_NAME = "008";        // Nom incorrect
  static const String UNKNOWN_PATIENT_ID = "009";    // ID patient inconnu
  static const String ACCOUNT_DISABLED = "010";      // Compte désactivé
  static const String ACCOUNT_DEACTIVATED = "011";   // Compte désactivé
  static const String UNKNOWN_PATIENT_STATUS = "012"; // Statut du patient inconnu

  /// Codes d'erreur liés aux données patient (013-015)
  static const String INCORRECT_PATIENT_NAME = "013"; // Nom du patient incorrect
  static const String NO_DOCTOR_FOUND = "014";       // Aucun médecin trouvé
  static const String NO_HEMA_USER = "015";          // Aucun utilisateur Hémadialyse trouvé

  /// Codes d'erreur liés aux sessions et enregistrements (016-017)
  static const String SESSION_LOCKED = "016";        // Session en cours de visualisation
  static const String RECORD_LOCKED = "017";         // Enregistrement verrouillé

  /// Codes d'erreur liés aux emails (018-021)
  static const String SENDER_ERROR = "018";          // Erreur d'émetteur
  static const String SMTP_ERROR = "019";            // Erreur SMTP
  static const String INVALID_EMAIL = "020";         // Adresse mail destinataire non valide
  static const String SMTP_ATTACHMENT_ERROR = "021"; // Erreur SMTP pièce jointe

  /// Codes d'erreur liés aux fichiers et pièces jointes (022-031)
  static const String EMPTY_TABLE_NUMBER = "022";    // Numéro de table vide
  static const String FILE_NOT_FOUND = "023";        // Fichier non trouvé
  static const String BLOB_ERROR = "024";            // Erreur document vers blob
  static const String EMPTY_ATTACHMENT = "025";      // Pièce jointe vide
  static const String MISSING_ATTACHMENT = "026";    // Absence du champ pièce jointe
  static const String INCORRECT_TABLE_NUMBER = "027"; // Numéro de table incorrecte
  static const String DIRECTORY_CREATE_ERROR = "028"; // Impossible de créer le répertoire
  static const String FILE_NOT_CREATED = "029";      // Fichier non créé
  static const String INCORRECT_ATTACHMENT = "030";   // Numéro de pièce jointe incorrecte
  static const String FILE_NOT_DELETED = "031";      // Fichier non supprimé

  /// Codes d'erreur liés aux rendez-vous (032-036)
  static const String INVALID_PATIENT_TABLE = "032"; // ID patient ou numéro de table incorrect
  static const String INVALID_APPOINTMENT_DATA = "033"; // Données de RDV incorrectes
  static const String INCORRECT_APPOINTMENT_NAME = "034"; // Nom incorrect pour le RDV
  static const String INVALID_APPOINTMENT_ID = "035";    // Id de RDV incorrect
  static const String APPOINTMENT_MODIFY_UNAVAILABLE = "036"; // Modification RDV indisponible

  /// Codes d'erreur liés aux séances (037-046)
  static const String NO_SESSION_AT_DATE = "037";    // Aucune séance à cette date
  static const String INVALID_PRESCRIPTION = "038";   // Prescription incorrecte/inactive
  static const String NXR_SAVE_FAILED = "039";       // Échec enregistrement NXR
  static const String SESSION_TOO_OLD = "040";       // Séance trop ancienne
  static const String INVALID_FOLLOW_UP_ID = "041";  // Id de suivi incorrect
  static const String INVALID_FOLLOW_UP_ID_2 = "042"; // Id de suivi incorrect
  static const String TREATMENT_RECORD_NOT_FOUND = "043"; // Fiche traitement non trouvée
  static const String INVALID_PATIENT_LOCATION = "044"; // ID patient/localisation incorrect
  static const String SESSION_IN_PROGRESS = "045";   // Séance déjà en cours
  static const String EMPTY_NXR_INFO = "046";        // Infos séance NXR vides

  /// Codes d'erreur liés aux enregistrements et consultations (047-052)
  static const String DIRECTORY_NOT_FOUND = "047";   // Répertoire non trouvé
  static const String RECORD_NOT_FOUND = "048";      // Enregistrement non trouvé
  static const String VIRTUAL_RECORD_UNSUPPORTED = "049"; // Enregistrement virtuel non supporté
  static const String CONSULTATION_IN_PROGRESS = "050"; // Consultation déjà en cours
  static const String INVALID_CONSULTATION_ID = "051"; // Id consultation incorrect
  static const String MULTIPLE_CONSULTATIONS = "052"; // Plusieurs consultations pour cet id

  /// Codes d'erreur liés aux traitements et suivis (054-057)
  static const String TREATMENT_NOT_FOUND = "054";   // Traitement non trouvé
  static const String UNKNOWN_FOLLOW_UP_TYPE = "055"; // Type de suivi inconnu
  static const String EMAIL_NOT_FOUND = "056";       // Email non trouvé
  static const String TRANSPLANT_RECORD_NOT_FOUND = "057"; // Enregistrement greffe non trouvé

  /// Codes d'erreur liés à la sécurité (058-060)
  static const String IDENTITY_VALIDATION_FAILED = "058"; // Identité non validée
  static const String SECURITY_KEY_EXPIRED = "059";    // Clé de sécurité expirée
  static const String SECURITY_KEY_INVALID = "060";    // Clé de sécurité invalide

  /// Construit la clé de traduction complète pour un code d'erreur.
  /// 
  /// Ajoute le préfixe '_Error4D_' au code pour correspondre aux clés 
  /// dans les fichiers de traduction.
  /// 
  /// Exemple:
  /// ```dart
  /// String key = ErrorCodes.getFullErrorCode("060");
  /// // Retourne '_Error4D_060'
  /// ```
  /// 
  /// [code] Le code d'erreur à 3 chiffres
  /// [return] La clé de traduction complète
  static String getFullErrorCode(String code) => '_Error4D_$code';
}
