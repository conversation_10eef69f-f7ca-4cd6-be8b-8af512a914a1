import 'package:envied/envied.dart';
import 'package:flutter/material.dart';
import 'package:theradom/models/UserAccountInfos.dart';
import 'package:theradom/models/NotificationResponse.dart';
import 'package:theradom/models/BasicUserInfos.dart';
import 'package:theradom/models/SamplingTimerInfos.dart';

part 'config.g.dart';

const String _ENVIRONMENT = 'dev'; // dev, test, prod

/// Pour générer un nouveau fichier config.g.dart après modification des variables d'environnement:
/// flutter pub run build_runner build --delete-conflicting-outputs

@Envied(path: 'env/$_ENVIRONMENT.env')
abstract class Config {
  @EnviedField(varName: 'WS_USER', obfuscate: true)
  static final String wsUser = _Config.wsUser;

  @EnviedField(varName: 'WS_PASSWORD', obfuscate: true)
  static final String wsPassword = _Config.wsPassword;

  @EnviedField(varName: 'AES_SECRET_KEY', obfuscate: true)
  static final String aesSecretKey = _Config.aesSecretKey;

  @EnviedField(varName: 'A_VALUE', obfuscate: true)
  static final int aValue = _Config.aValue;

  @EnviedField(varName: 'B_VALUE', obfuscate: true)
  static final int bValue = _Config.bValue;

  @EnviedField(varName: 'ID_DOULEUR', obfuscate: true)
  static final String idDouleur = _Config.idDouleur;

  @EnviedField(varName: 'TABLE_FAKE_RAPPORTS', obfuscate: true)
  static final String tableFakeRapports = _Config.tableFakeRapports;

  @EnviedField(varName: 'TABLE_FAKE_DOCS', obfuscate: true)
  static final String tableFakeDocs = _Config.tableFakeDocs;

  @EnviedField(varName: 'TABLE_BIOLOGIC_ANALYSIS', obfuscate: true)
  static final String tableBiologicAnalysis = _Config.tableBiologicAnalysis;

  @EnviedField(varName: 'TABLE_NEPHROLOGY_CONSULTATION', obfuscate: true)
  static final String tableNephrologyConsultation =
      _Config.tableNephrologyConsultation;

  @EnviedField(varName: 'TABLE_POST_TRANSPLANT_CONSULTATION', obfuscate: true)
  static final String tablePostTransplantConsultation =
      _Config.tablePostTransplantConsultation;

  @EnviedField(varName: 'TABLE_DIALYSIS_SESSION', obfuscate: true)
  static final String tableDialysisSession = _Config.tableDialysisSession;

  @EnviedField(varName: 'TABLE_MAILING', obfuscate: true)
  static final String tableMailing = _Config.tableMailing;

  @EnviedField(varName: 'EVAL_FORM_RECEPTION_ADDRESS', obfuscate: true)
  static final String evalFormReceptionAddress =
      _Config.evalFormReceptionAddress;

  /// clés des préférences utilisateur
  static const String prefIsConnected = "is_connected";
  static const String prefDomainName = "domainName";
  static const String prefUsername = "username";
  static const String prefPassword = "password";
  static const String prefLanguageCode = "language_code";
  static const String prefMessageData = "messageData";
  static const String prefFakeAssetsNumber = "fakeAssetsNumber";
  static const String prefDurBeforeLogOut = "durBeforeLogOut";
  static const String prefSavedQrCode = "savedQrCode";
  static const String prefFirstUseState = "firstUseState";
  static const String prefUseBiometricID = "biometricID";
  static const String prefSamplingReminderValue = "samplingReminder";
  static const String prefDoNotShowPrescriptionWarning = "prescriptionWarning";
  static const String prefShowUsabilityQuestionnaireCount =
      "showUsabilityQuestionnaireCount";

  /// Infos de l'app
  static const String appName = 'Théradom';
  static const String appVersion = '3.1.0'; // correspondant au .yaml !
  static const String appPublisher =
      'Société EMA (Engineering Medical Application)';
  static const String appHMWcompatibility = "5.4.1";
  static const String appHMDcompatibility = "4.3.7";
  static const String appPublisherWebsite = 'https://www.hemasystem.com';
  static const String appPublisherAddress =
      "9 boulevard François et Emile Zola, 13100, Aix-en-Provence, France";
  static const String appHotlineContact = "0825 890 531\n04 42 03 97 52";
  static const String appSupportContact = "<EMAIL>";
  static const String appCEmarkingYear = "2021";

  /// Variables globales
  static const Map langageMap = {
    // code et langues de l'application
    'fr': 'Français',
    'it': 'Italiano',
    'en': 'English'
  };
  static const int audioFileMaxDuration =
      2; // durée maximale d'un fichier audio (en minute)
  static const int pjMaxSize =
      10000000; // taille max de pj à envoyer en une fois
  static const int pjMaxNumber = 3; // nb max de pj à envoyer en une fois
  static const int messageFrom = 1; // premier message
  static const int messageTo = 10; // dernier Message
  static const int statsMaxLines =
      8; // nombre de courbes max à afficher en simultané
  static const int pdfMafPages = 5; // nombre de page max pour un pdf
  static const int durationBeforeSessionSave =
      60; // temps avant la sauvegarde automatique de la séance (en secondes)
  static const int evalFormVersion =
      3; // numéro de version du formulaire de d'utilisabilité
  static const String idPoidsDebut = "38_7";
  static const String idPoidsSec = "1_70";
  static const String idApportsRealises = "38_184";
  static const String idPoidsApres = "38_8";
  static int durationBeforeLogout =
      5; // temps avant deconnexion auto / défaut à 5 minutes
  static int samplingDurationReminder =
      30; // temps avant le rappel de saisie des donn"es d'échantillonage / défaut à 30min
  static String database = "HEMADIALYSE"; // a supprimer à terme
  static String nomDomaine = "";
  static NotificationResponse? notificationResponse;
  //State de la notification au moment de l'initialisation
  static NotificationResponse? notificationResponseInit;
  static bool isBiometricsValidated = false;
  static BasicUserInfos? basicUserInfos;
  static int defaultScreen =
      0; // page de l'appli qui apparait en première / voir DefaultScreenController // 0 -> suivis, 1 -> calendrier, 2 -> messagerie, 3 -> bio
  static UserAccountInfos? userAccountInfos;
  static int?
      firstUseState; // 0 -> à l'installation, 1 -> password modifié la première fois, 2 -> authentifié avec succès
  static bool? useBiometricID; // le user autorise le touchID/faceId
  static SamplingTimerInfos? samplingTimerInfos;
  static bool doNotShowPrescriptionWarning = false;
  static int? showUsabilityQuestionnaireCount;
  // variables globales du code
  static GlobalKey<NavigatorState> appKey = GlobalKey<NavigatorState>();
  static bool blockBackBtnPop = true;
  static const int appBarHeight = 80;
  static const String privacyPolicyUrl =
      "https://hemasystem.com/politique-de-confidentialite/";
}
