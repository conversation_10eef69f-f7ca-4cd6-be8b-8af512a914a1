import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/services/repositories/mobile_send_new_password_repository.dart';
import 'package:theradom/services/snackbar_service.dart';
import 'package:theradom/exceptions/business_exception.dart';
import 'package:theradom/utils/Helper.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseNotificationService.dart';
import 'package:theradom/blocs/login/StateLogin.dart';
import 'package:theradom/utils/Translations.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';

class NewPasswordDialog extends StatefulWidget {
  final String emailParam;

  const NewPasswordDialog({
    Key? key,
    required this.emailParam,
  }) : super(key: key);

  @override
  _NewPasswordDialogState createState() => _NewPasswordDialogState();
}

class _NewPasswordDialogState extends State<NewPasswordDialog> {
  final _formKey = GlobalKey<FormState>();

  // Contrôleurs pour les champs de formulaire
  final TextEditingController _otpController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  /// Exemple de pattern de validation pour le mot de passe.
  final String _passwordPattern =
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0)),
      child: Container(
        padding: const EdgeInsets.all(30.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.0),
          color: Colors.white,
        ),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Nouveau mot de passe',
                  style: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  "Veuillez saisir l'OTP que vous venez de recevoir sur votre boîte mail ainsi que votre nouveau mot de passe.",
                  style: TextStyle(fontSize: 14.0, color: Colors.grey),
                ),
                const SizedBox(height: 20),

                // Champ OTP
                TextFormField(
                  controller: _otpController,
                  decoration: InputDecoration(
                    labelText: 'OTP',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    contentPadding: EdgeInsets.all(20.0),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer l\'OTP';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),

                // Champ Nouveau mot de passe
                TextFormField(
                  controller: _newPasswordController,
                  decoration: InputDecoration(
                    labelText: 'Nouveau mot de passe',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    contentPadding: EdgeInsets.all(20.0),
                  ),
                  obscureText: true,
                  validator: _validatePassword,
                ),
                const SizedBox(height: 15),

                // Champ Confirmation du mot de passe
                TextFormField(
                  controller: _confirmPasswordController,
                  decoration: InputDecoration(
                    labelText: 'Confirmer le mot de passe',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    contentPadding: EdgeInsets.all(20.0),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value != _newPasswordController.text) {
                      return 'Les mots de passe ne correspondent pas';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      await onEmailSent(
                        context,
                        Config.userAccountInfos!.INS,
                        Config.userAccountInfos!.login,
                        widget.emailParam,
                        _otpController.text,
                        true,
                        _newPasswordController.text,
                      );
                    }
                  },
                  child: const Text('Valider'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Valide le mot de passe selon les critères suivants:
  /// - Au moins 8 caractères
  /// - Au moins une minuscule
  /// - Au moins une majuscule
  /// - Au moins un chiffre
  /// - Au moins un caractère spécial
  /// @param value Le mot de passe à valider
  /// @return null si le mot de passe est valide, sinon un message d'erreur
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Veuillez entrer un mot de passe';
    }
    RegExp regex = RegExp(_passwordPattern);
    if (!regex.hasMatch(value)) {
      return 'Le mot de passe doit contenir au moins 8 caractères, '
          'une minuscule, une majuscule, un chiffre et un caractère spécial';
    }
    return null;
  }

  /// Chiffre le mot de passe en utilisant l'algorithme MD5
  /// @param password Le mot de passe à chiffrer
  /// @return Le mot de passe chiffré en hexadécimal
  String encryptPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = md5.convert(bytes);
    return digest.toString();
  }

  /// Envoie une requête pour changer le mot de passe
  /// @param context Le contexte de build Flutter
  /// @param ins L'identifiant INS de l'utilisateur
  /// @param login Le login de l'utilisateur
  /// @param email L'email de l'utilisateur
  /// @param otp Le code OTP reçu par email
  /// @param save_pwd Indique si le mot de passe doit être sauvegardé
  /// @param password Le nouveau mot de passe
  /// @return 1 si le changement a réussi, 0 sinon
  Future<int> onEmailSent(BuildContext context, String ins, String login,
      String email, String otp, bool save_pwd, String password) async {
    MobileSendNewPasswordRepository passwordResetRepo =
        MobileSendNewPasswordRepository();

    String appToken = await FirebaseNotificationService.instance
        .getDeviceToken(); // Remplacez par votre appToken

    password = encryptPassword(password);

    try {
      String deviceName = await Helper.getDeviceName();
      var result = await passwordResetRepo.sendNewPassword(
          appToken, ins, login, email, otp, save_pwd, password, deviceName);

      // Vérifier si le résultat est un StatusResponse et contient la propriété 'valide' avec la valeur 'OK'
      if (result == 'OK') {
        Navigator.of(context, rootNavigator: true).pop();
        (StateLoginModifyPassword(
            success: true,
            title: allTranslations.text("txt_validEmail"),
            message: "${allTranslations.text("txt_messageSentTo")} $email"));
        return 1;
      } else {
        SnackBarService.showErrorSnackBar(
          Config.appKey.currentContext!,
          message: 'Une erreur est survenue lors de l\'envoi de l\'email',
        );
        return 0;
      }
    } catch (e) {
      if (e is MobileSendNewPasswordException) {
        SnackBarService.showErrorSnackBar(
          Config.appKey.currentContext!,
          message: e.toString(),
        );
      } else {
        SnackBarService.showErrorSnackBar(
          Config.appKey.currentContext!,
          message: e.toString(),
        );
      }
      return 0;
    }
  }
}
