import 'package:flutter/material.dart';
import 'package:theradom/exceptions/business_exception.dart';
import 'package:theradom/services/repositories/mobile_password_reset_repository.dart';
import 'package:theradom/services/snackbar_service.dart';
import 'package:theradom/utils/Helper.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:theradom/config.dart';
import 'package:crypto/crypto.dart';
import 'package:theradom/services/repositories/mobile_send_new_password_repository.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseNotificationService.dart';
import 'package:theradom/blocs/login/StateLogin.dart';
import 'package:theradom/utils/Translations.dart';

class ModifyPasswordForm extends StatefulWidget {
  ModifyPasswordForm({MobileResetPasswordRepository? passwordRepository});

  @override
  _ModifyPasswordFormState createState() => _ModifyPasswordFormState();
}

class _ModifyPasswordFormState extends State<ModifyPasswordForm> {
  final _formKey = GlobalKey<FormState>();
  final ModifyPasswordForm passwordForm = ModifyPasswordForm();

  final TextEditingController securityKeyController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  /// Valide le mot de passe selon les critères suivants:
  /// - Au moins 8 caractères
  /// - Au moins une minuscule
  /// - Au moins une majuscule
  /// - Au moins un chiffre
  /// - Au moins un caractère spécial
  /// @param value Le mot de passe à valider
  /// @return null si le mot de passe est valide, sinon un message d'erreur
  String? _validatePassword(String? value) {
    String pattern =
        r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$';
    RegExp regex = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return 'Veuillez entrer un mot de passe';
    } else if (!regex.hasMatch(value)) {
      return 'Le mot de passe doit contenir au moins 8 caractères, une minuscule, une majuscule, un chiffre et un caractère spécial';
    }
    return null;
  }

  /// Construit le formulaire de modification de mot de passe
  /// - Affiche les champs de saisie (clé de sécurité, nouveau mot de passe, confirmation)
  /// - Gère la validation des champs
  /// - Gère la soumission du formulaire
  /// @param context Le contexte de build Flutter
  /// @return Le widget Dialog contenant le formulaire
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: EdgeInsets.all(30.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 0.0,
              blurRadius: 10.0,
            ),
          ],
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Modifier mot de passe',
                    style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue,
                    ),
                  ),
                  IconButton(
                    iconSize: 30,
                    icon: Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              SizedBox(height: 15),
              TextFormField(
                controller: securityKeyController,
                decoration: InputDecoration(
                  labelText: 'Clé de sécurité',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: false,
                style: TextStyle(fontSize: 18),
              ),
              SizedBox(height: 15),
              TextFormField(
                controller: newPasswordController,
                decoration: InputDecoration(
                  labelText: 'Nouveau mot de passe',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: true,
                style: TextStyle(fontSize: 18),
                validator: _validatePassword,
              ),
              SizedBox(height: 15),
              TextFormField(
                controller: confirmPasswordController,
                decoration: InputDecoration(
                  labelText: 'Confirmer le mot de passe',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: true,
                style: TextStyle(fontSize: 18),
                validator: (value) {
                  if (value != newPasswordController.text) {
                    return 'Les mots de passe ne correspondent pas';
                  }
                  return null;
                },
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding:
                          EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                      textStyle: TextStyle(fontSize: 18),
                    ),
                    child: Text('Confirmer',
                        style: TextStyle(color: Colors.white)),
                    onPressed: () async {
                      await onEmailSent(
                          context,
                          Config.userAccountInfos!.INS,
                          Config.userAccountInfos!.login,
                          Config.userAccountInfos!.email,
                          securityKeyController.text,
                          true,
                          newPasswordController.text);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Envoie une requête de réinitialisation de mot de passe à l'API
/// @param password Le nouveau mot de passe (sera chiffré)
/// @param securitykey La clé de sécurité fournie
/// @param numpat Le numéro du patient
/// @return La réponse HTTP de l'API
Future postPasswordReset(
    String password, String securitykey, String numpat) async {
  var cryptedPassword = encryptPassword(password);
  final response = await http.post(
    Uri.parse(
        'https://api.theradom.com/reset-password'), //changer l'adresse pour qu'elle corresponde à l'API
    headers: <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: jsonEncode(<String, String>{
      'password': cryptedPassword,
      'securitykey': securitykey,
      'numpat': numpat,
    }),
  );
  return response;
}

/// Chiffre le mot de passe en utilisant l'algorithme MD5
/// @param password Le mot de passe à chiffrer
/// @return Le mot de passe chiffré en hexadécimal
String encryptPassword(String password) {
  var bytes = utf8.encode(password);
  var digest = md5.convert(bytes);
  return digest.toString();
}

/// Envoie une requête pour changer le mot de passe
/// @param context Le contexte de build Flutter
/// @param ins L'identifiant INS de l'utilisateur
/// @param login Le login de l'utilisateur
/// @param email L'email de l'utilisateur
/// @param otp Le code OTP reçu
/// @param save_pwd Indique si le mot de passe doit être sauvegardé
/// @param password Le nouveau mot de passe
/// @return 1 si le changement a réussi, 0 sinon
Future<int> onEmailSent(BuildContext context, String ins, String login,
    String email, String otp, bool save_pwd, String password) async {
  MobileSendNewPasswordRepository passwordResetRepo =
      MobileSendNewPasswordRepository();
  String appToken = await FirebaseNotificationService.instance
      .getDeviceToken(); // Remplacez par votre appToken
  password = encryptPassword(password);

  try {
    String deviceName = await Helper.getDeviceName();
    var result = await passwordResetRepo.sendNewPassword(
        appToken, ins, login, email, otp, save_pwd, password, deviceName);

    if (result == 'OK') {
      Navigator.of(context).pop();
      (StateLoginModifyPassword(
          success: true,
          title: allTranslations.text("txt_validEmail"),
          message: "${allTranslations.text("txt_messageSentTo")} $email"));
      return 1;
    } else {
      //print("Email not sent");
      return 0;
    }
  } catch (e) {
    if (e is MobileSendNewPasswordException) {
      SnackBarService.showErrorSnackBar(
        context,
        message: 'Une erreur est survenue lors de l\'envoi de l\'email',
      );
    }
    return 0;
  }
}
