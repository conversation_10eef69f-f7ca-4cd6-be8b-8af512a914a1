abstract class EventLogin {
  const EventLogin();

  List<Object> get props => [];
}

// clic sur le bouton 'Entrer'
class EventLogginBtnPressed extends EventLogin {
  final String user;
  final String password;
  final bool biometricAuth;

  const EventLogginBtnPressed(
      {required this.user,
      required this.password,
      required this.biometricAuth});

  @override
  List<Object> get props => [user, password, biometricAuth];
}

// clic sur le bouton 'Mot de passe oublié'
class EventLoginForgotPassword extends EventLogin {
  final String email;

  const EventLoginForgotPassword({required this.email});

  @override
  List<Object> get props => [email];
}

class EventLoginNewDevice extends EventLogin {
  final String ins;

  const EventLoginNewDevice({required this.ins});

  @override
  List<Object> get props => [ins];
}

class EventLoginEmailSent extends EventLogin {
  final bool success;

  const EventLoginEmailSent({required this.success});

  @override
  List<Object> get props => [success];
}
