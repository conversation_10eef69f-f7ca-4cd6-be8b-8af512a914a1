import 'package:flutter/material.dart';
import 'package:theradom/services/repositories/mobile_password_reset_repository.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:theradom/config.dart';
import 'package:crypto/crypto.dart';

class ForcePasswordChange extends StatefulWidget {
  ForcePasswordChange({MobileResetPasswordRepository? passwordRepository});

  @override
  _ForcePasswordChangeState createState() => _ForcePasswordChangeState();
}

class _ForcePasswordChangeState extends State<ForcePasswordChange> {
  final _formKey = GlobalKey<FormState>();
  final ForcePasswordChange passwordForm = ForcePasswordChange();

  final TextEditingController securityKeyController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  String? _validatePassword(String? value) {
    String pattern =
        r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$';
    RegExp regex = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return 'Veuillez entrer un mot de passe';
    } else if (!regex.hasMatch(value)) {
      return 'Le mot de passe doit contenir au moins 8 caractères, une minuscule, une majuscule, un chiffre et un caractère spécial';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: EdgeInsets.all(30.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 0.0,
              blurRadius: 10.0,
            ),
          ],
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Modifier mot de passe',
                    style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue,
                    ),
                  ),

                  /* IconButton(
                    iconSize: 30,
                    icon: Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Navigator.of(context).pop(),
                  ), */
                ],
              ),
              SizedBox(height: 10),
              Text(
                "Votre mot de passe doit être renouvellé . Pour des raisons de sécurité, veuillez le changer.",
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
              SizedBox(height: 15),
              TextFormField(
                controller: newPasswordController,
                decoration: InputDecoration(
                  labelText: 'Ancien mot de passe',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: true,
                style: TextStyle(fontSize: 18),
                validator: _validatePassword,
              ),
              SizedBox(height: 15),
              TextFormField(
                controller: newPasswordController,
                decoration: InputDecoration(
                  labelText: 'Nouveau mot de passe',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: true,
                style: TextStyle(fontSize: 18),
                validator: _validatePassword,
              ),
              SizedBox(height: 15),
              TextFormField(
                controller: confirmPasswordController,
                decoration: InputDecoration(
                  labelText: 'Confirmer le mot de passe',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: true,
                style: TextStyle(fontSize: 18),
                validator: (value) {
                  if (value != newPasswordController.text) {
                    return 'Les mots de passe ne correspondent pas';
                  }
                  return null;
                },
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding:
                          EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                      textStyle: TextStyle(fontSize: 18),
                    ),
                    child: Text('Confirmer',
                        style: TextStyle(color: Colors.white)),
                    onPressed: () async {
                      var password = newPasswordController.text;
                      var securitykey = securityKeyController.text;
                      var numpat = Config.basicUserInfos!.numpat;
                      await postPasswordReset(password, securitykey, numpat);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future postPasswordReset(
    String password, String securitykey, String numpat) async {
  var cryptedPassword = encryptPassword(password);
  final response = await http.post(
    Uri.parse(
        'https://api.theradom.com/reset-password'), //changer l'adresse pour qu'elle corresponde à l'API
    headers: <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: jsonEncode(<String, String>{
      'password': cryptedPassword,
      'securitykey': securitykey,
      'numpat': numpat,
    }),
  );
  return response;
}

String encryptPassword(String password) {
  var bytes = utf8.encode(password);
  var digest = md5.convert(bytes);
  return digest.toString();
}
