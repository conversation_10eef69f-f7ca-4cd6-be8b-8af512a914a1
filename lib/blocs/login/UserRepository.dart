import 'package:theradom/config.dart';
import 'package:theradom/utils/SharedPreferences.dart';

class UserRepository {
  Future<void> disconnectUser() async {
    // en cas de log out
    //await Future.delayed(Duration(seconds: 1));
    await SharedPreferencesUtil.instance.setBool(Config.prefIsConnected, false);
    return;
  }

  Future<void> connectUser() async {
    // en cas de login confirmé
    //await Future.delayed(Duration(seconds: 1));
    await SharedPreferencesUtil.instance.setBool(Config.prefIsConnected, true);
    return;
  }

  Future<bool> getConnectionUserState() async {
    await Future.delayed(Duration(seconds: 1));
    // on regarde si l'utilisateur est déjà passé par l'authentification
    bool userConnected;
    try {
      userConnected =
          await SharedPreferencesUtil.instance.getBool(Config.prefIsConnected);
      if (userConnected == true) {
        return true;
      } else {
        return false;
      }
    } catch (_) {
      return false;
    }
  }
}
