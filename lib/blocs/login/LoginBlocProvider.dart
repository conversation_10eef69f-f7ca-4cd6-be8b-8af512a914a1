import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'BlocLogin.dart';

class LoginBlocProvider extends StatelessWidget {
  final Widget child;

  LoginBlocProvider({required this.child}) : super();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return BlocLogin(
            blocAuthentification: BlocProvider.of<BlocAuthentication>(context));
      },
      child: child,
    );
  }
}
