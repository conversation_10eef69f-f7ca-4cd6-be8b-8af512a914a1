abstract class StateLogin {
  const StateLogin();

  List<Object> get props => [];
}

// état initial du formulaire
class StateLoginInitial extends StateLogin {}

// etat apres validation du user password / vérification du user et password
// chargement
class StateLoginLoading extends StateLogin {
  final bool loading;

  StateLoginLoading({required this.loading});

  @override
  List<Object> get props => [loading];
}

// état après que le loggin a échoué
class StateLoginFailure extends StateLogin {
  final String titleError;
  final String messageError;

  const StateLoginFailure(
      {required this.titleError, required this.messageError});

  @override
  List<Object> get props => [titleError, messageError];
}


class StateLoginEmailUnknown extends StateLogin {
  final String titleError;
  final String messageError;

  const StateLoginEmailUnknown(
      {required this.titleError, required this.messageError});

  @override
  List<Object> get props => [titleError, messageError];
}

class StateLoginAccountBlocked extends StateLogin {
  final String titleError;
  final String messageError;

  const StateLoginAccountBlocked(
      {required this.titleError, required this.messageError});

  @override
  List<Object> get props => [titleError, messageError];
}


class StateOTPRequired extends StateLogin {
  final String title;
  final String message;
  final String email;
  final String ins;

  const StateOTPRequired(
      {required this.title,
      required this.message,
      required this.email,
      required this.ins});

  @override
  List<Object> get props => [title, message, email, ins];
}

// état quand le mot de passe a été oublié
class StateLoginModifyPassword extends StateLogin {
  final bool success;
  final String title;
  final String message;

  const StateLoginModifyPassword(
      {required this.success, required this.title, required this.message});

  @override
  List<Object> get props => [success, title, message];
}

class StateLoginPasswordModified extends StateLogin {
  final bool success;
  final String password;
  final String oldPassword;

  const StateLoginPasswordModified(
      {required this.success,
      required this.password,
      required this.oldPassword});

  @override
  List<Object> get props => [success, password, oldPassword];
}

class StateLoginEmailSent extends StateLogin {
  final bool success;

  const StateLoginEmailSent({required this.success});
  @override
  List<Object> get props => [success];
}

class StateNewDeviceDetected extends StateLogin {
  final bool success;

  const StateNewDeviceDetected({required this.success});
  @override
  List<Object> get props => [success];
}

class StateLoginBlocked extends StateLogin {
  final String titleError;
  final String messageError;

  StateLoginBlocked({required this.titleError, required this.messageError});
}
