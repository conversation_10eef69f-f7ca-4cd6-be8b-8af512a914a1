import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:bloc/bloc.dart';
import 'package:theradom/models/GetMessagesRequest.dart';
import 'package:theradom/models/SendMessageRequest.dart';
import 'package:theradom/models/StatusMessageRequest.dart';
import 'package:theradom/models/message.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_messagerie_repository.dart';
import 'package:theradom/config.dart';
import 'package:theradom/blocs/messaging/event_messaging.dart';
import 'package:theradom/blocs/messaging/state_messaging.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/GetMessagesResponse.dart';
import 'package:theradom/utils/Translations.dart';

class BlocMessaging extends Bloc<EventMessaging, StateMessaging> {
  late final MobileMessagerieRepository _mobileMessagerieRepository;
  final int _pjMaxSize = Config.pjMaxSize;
  final int _pjMaxNumber = Config.pjMaxNumber;
  final int _initialMessageFromNumber = Config.messageFrom;
  final int _initialMessageToNumber = Config.messageTo;

  BlocMessaging({MobileMessagerieRepository? mobileMessagerieRepository})
      : super(StateMessagingUninitialized()) {
    _mobileMessagerieRepository =
        mobileMessagerieRepository ?? MobileMessagerieRepository();
    on<EventMessagingMailbox>(_getReceivedMessages);
    on<EventMessagingSentMessages>(_getSentMessages);
    on<EventMessagingTrash>(_getDeletedMessages);
    on<EventMessagingAcknowledgeMessaging>(_acknowledgeMessage);
    on<EventMessagingDeleteMessage>(_deleteMessage);
    on<EventMessagingSendNewMessage>(_sendMessage);
    on<EventMessagingReadMessage>(_readMessage);
    on<EventMessagingUpdateMailBox>(_updateMailbox);
  }

  void _getReceivedMessages(
      EventMessagingMailbox event, Emitter<StateMessaging> emit) async {
    // // requête des 10 premiers messages de la mailbox
    emit(StateMessagingUninitialized());
    try {
      GetMessagesRequest getMessagesRequest = GetMessagesRequest(
          numpat: Config.basicUserInfos!.numpat,
          option: "0",
          from: _initialMessageFromNumber,
          to: _initialMessageToNumber);
      String getMessagesRequestJson =
          getMessagesRequestToJson(getMessagesRequest);
      GetMessagesResponse getMessagesResponse =
          await _mobileMessagerieRepository
              .fetchMessages(getMessagesRequestJson);
      emit(StateMessagingMailbox(
          receivedMessages: getMessagesResponse,
          errorDetails: null,
          totalAvailable: getMessagesResponse.totalAvailable));
    } on WebServiceCallException catch (e) {
      emit(StateMessagingMailbox(
          receivedMessages: null,
          errorDetails: ErrorDetails(code: "500", message: e.toString()),
          totalAvailable: null));
    }
  }

  void _getSentMessages(
      EventMessagingSentMessages event, Emitter<StateMessaging> emit) async {
    // requête des 10 premiers messages de la boîte d'envoi
    emit(StateMessagingUninitialized());
    try {
      GetMessagesRequest getMessagesRequest = GetMessagesRequest(
          numpat: Config.basicUserInfos!.numpat,
          option: "1",
          from: _initialMessageFromNumber,
          to: _initialMessageToNumber);
      String getMessagesRequestJson =
          getMessagesRequestToJson(getMessagesRequest);
      GetMessagesResponse getMessagesResponse =
          await _mobileMessagerieRepository
              .fetchMessages(getMessagesRequestJson);
      emit(StateMessagingSentMessages(
          sentMessages: getMessagesResponse,
          errorDetails: null,
          totalAvailable: getMessagesResponse.totalAvailable));
    } on WebServiceCallException catch (e) {
      emit(StateMessagingSentMessages(
          sentMessages: null,
          errorDetails: ErrorDetails(code: "500", message: e.toString()),
          totalAvailable: null));
    }
  }

  void _getDeletedMessages(
      EventMessagingTrash event, Emitter<StateMessaging> emit) async {
    // requête de 10 premiers messages de la corbeille
    emit(StateMessagingUninitialized());
    try {
      GetMessagesRequest getMessagesRequest = GetMessagesRequest(
          numpat: Config.basicUserInfos!.numpat,
          option: "2",
          from: _initialMessageFromNumber,
          to: _initialMessageToNumber);
      String getMessagesRequestJson =
          getMessagesRequestToJson(getMessagesRequest);
      GetMessagesResponse getMessagesResponse =
          await _mobileMessagerieRepository
              .fetchMessages(getMessagesRequestJson);
      emit(StateMessagingTrash(
          deletedMessages: getMessagesResponse,
          errorDetails: null,
          totalAvailable: getMessagesResponse.totalAvailable));
    } on WebServiceCallException catch (e) {
      emit(StateMessagingTrash(
          deletedMessages: null,
          errorDetails: ErrorDetails(code: "500", message: e.toString()),
          totalAvailable: null));
    }
  }

  void _acknowledgeMessage(EventMessagingAcknowledgeMessaging event,
      Emitter<StateMessaging> emit) async {
    // acknowledge un message lu
    try {
      StatusMessageRequest statusMessageRequest = StatusMessageRequest(
          numpat: Config.basicUserInfos!.numpat,
          numMessage: event.message.numEnregMessagerie);
      String statusMessageRequestJson =
          getStatusMessageRequestToJson((statusMessageRequest));
      await _mobileMessagerieRepository
          .acknowledgeMessage(statusMessageRequestJson);
      emit(StateMessagingAcknowledgeMessage(message: event.message));
    } on WebServiceCallException catch (_) {}
  }

  void _deleteMessage(
      EventMessagingDeleteMessage event, Emitter<StateMessaging> emit) async {
    // suppression d'un message
    try {
      StatusMessageRequest statusMessageRequest = StatusMessageRequest(
          numpat: Config.basicUserInfos!.numpat,
          numMessage: event.messageToDelete.numEnregMessagerie);
      String statusMessageRequestJson =
          getStatusMessageRequestToJson((statusMessageRequest));
      await _mobileMessagerieRepository.deleteMessage(statusMessageRequestJson);
      emit(StateMessagingMessageDeleted(
          success: true,
          message: allTranslations.text(
              'msg_delete_success', {"sender": event.messageToDelete.emetteur}),
          reloadList: !event.slideRemoval));
    } on WebServiceCallException catch (_) {
      emit(StateMessagingMessageDeleted(
          success: false,
          message: allTranslations.text(
              'msg_delete_fail', {"sender": event.messageToDelete.emetteur}),
          reloadList: !event.slideRemoval));
    }
  }

  void _sendMessage(
      EventMessagingSendNewMessage event, Emitter<StateMessaging> emit) async {
    // envoi d'un message et des pièces jointes associées
    emit(StateMessagingSendMessageLoading(loading: true));

    ErrorDetails? yErrorDetailsPj;
    ErrorDetails? yErrorDetailsSendMessage;

    final destinataire = event.destinataire;
    final objet = event.objet;
    final contenu = event.contenu;
    final fileList = event.fileList;
    final File? audioFile = event.audioFile;

    if (fileList.length > _pjMaxNumber) {
      // trop de pj
      yErrorDetailsPj = ErrorDetails(
          code: "",
          message: allTranslations
              .text('msg_max_attachments', {"nb": "$_pjMaxNumber"}));
    } else {
      final pjContent = await _getPjContentAndNames(
          destinataire, objet, contenu, fileList, audioFile);
      if (pjContent is ErrorDetails) {
        yErrorDetailsSendMessage = pjContent;
      } else {
        final List<String> nameList = pjContent[0];
        final List<String> contentList = pjContent[1];

        try {
          SendMessageRequest sendMessageRequest = SendMessageRequest(
              numpat: Config.basicUserInfos!.numpat,
              destinataire: destinataire,
              objet: objet,
              contenu: contenu,
              pjBase64List: contentList,
              pjNameList: nameList);
          final sendMessageRequestJson =
              sendMessageRequestToJson(sendMessageRequest);
          await _mobileMessagerieRepository.sendMessage(sendMessageRequestJson);
        } on WebServiceCallException catch (e) {
          yErrorDetailsSendMessage =
              ErrorDetails(code: "", message: e.toString());
        }
      }
    }

    emit(StateMessagingSendMessageLoading(loading: false));
    emit(StateMessagingMessageSentSuccess(
        errorDetailsPj: yErrorDetailsPj,
        errorDetailsMessage: yErrorDetailsSendMessage,
        context: event.context,
        isDraft: event.isDraft));
  }

  void _readMessage(
      EventMessagingReadMessage event, Emitter<StateMessaging> emit) async {
    // ouverutre d'un message
    emit(StateMessagingReadMessage(
        boite: event.boite, message: event.messageToRead));
  }

  void _updateMailbox(
      EventMessagingUpdateMailBox event, Emitter<StateMessaging> emit) async {
    // met à jour la boite avec 10 nouveaux messages plus anciens

    try {
      GetMessagesRequest getMessagesRequest = GetMessagesRequest(
          numpat: Config.basicUserInfos!.numpat,
          option: event.option,
          from: event.from,
          to: event.to);
      String getMessagesRequestJson =
          getMessagesRequestToJson(getMessagesRequest);
      GetMessagesResponse getMessagesResponse =
          await _mobileMessagerieRepository
              .fetchMessages(getMessagesRequestJson);
      List<Message> newMessageList = [];
      getMessagesResponse.messages.forEach((key, message) {
        newMessageList.add(message);
      });
      emit(StateMessagingUpdatedMailbox(
          newMessageList: newMessageList, errorDetails: null));
    } on WebServiceCallException catch (e) {
      emit(StateMessagingUpdatedMailbox(
          newMessageList: [],
          errorDetails: ErrorDetails(code: "", message: e.toString())));
    }
  }

  Future<dynamic> _getPjContentAndNames(String destinataire, String objet,
      String contenu, List<File?> fileList, File? audioFile) async {
    // envoi un message avec ses PJ associées

    List<String> nameList = [];
    List<String> contentList = [];

    int i = 0;
    int pjSize = 0;

    if (audioFile != null) {
      // remove le file associé à l'emplacement du fichier audio
      fileList.removeWhere((file) => file == null);
    }

    while (i < fileList.length) {
      nameList.add("PJ_00" +
          (i + 1).toString() +
          ".jpg"); // création du nom pour les images
      File file = fileList[i]!;
      Uint8List uint8list = file.readAsBytesSync();
      pjSize = pjSize + uint8list.length;
      String base64String = base64.encode(uint8list);
      contentList.add(base64String);
      i++;
    }

    // création du nom pour le fichier audio
    if (audioFile != null) {
      nameList.add(
          "PJ_00" + (i + 1).toString() + "." + audioFile.path.split(".").last);
      Uint8List uint8listAudioFile = audioFile.readAsBytesSync();
      pjSize = pjSize + uint8listAudioFile.length;
      String base64String = base64.encode(uint8listAudioFile);
      contentList.add(base64String);
    }

    if (pjSize > _pjMaxSize) {
      return ErrorDetails(
          code: "", message: allTranslations.text('msg_attachments_too_large'));
    } else {
      return [nameList, contentList];
    }
  }
}
