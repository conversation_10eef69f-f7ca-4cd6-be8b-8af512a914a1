import 'package:flutter/material.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/GetMessagesResponse.dart';
import 'package:theradom/models/message.dart';

abstract class StateMessaging {
  List<Object> get props => [];
}

// non initialisé
class StateMessagingUninitialized extends StateMessaging {}

// loading pedant l'envoi du message
class StateMessagingSendMessageLoading extends StateMessaging {
  final bool loading;

  StateMessagingSendMessageLoading({
    required this.loading,
  });

  @override
  List<Object> get props => [loading];
}

// visualisation de la boite de reception
class StateMessagingMailbox extends StateMessaging {
  final GetMessagesResponse? receivedMessages;
  final ErrorDetails? errorDetails;
  final int? totalAvailable;

  StateMessagingMailbox(
      {required this.receivedMessages,
      required this.errorDetails,
      required this.totalAvailable});

  @override
  List<Object> get props => [receivedMessages!, errorDetails!, totalAvailable!];
}

// visualisation des messages envoyés
class StateMessagingSentMessages extends StateMessaging {
  final GetMessagesResponse? sentMessages;
  final ErrorDetails? errorDetails;
  final int? totalAvailable;

  StateMessagingSentMessages(
      {required this.sentMessages,
      required this.errorDetails,
      required this.totalAvailable});

  @override
  List<Object> get props => [sentMessages!, errorDetails!, totalAvailable!];
}

// visualisation des messages supprimés
class StateMessagingTrash extends StateMessaging {
  final GetMessagesResponse? deletedMessages;
  final ErrorDetails? errorDetails;
  final int? totalAvailable;

  StateMessagingTrash(
      {required this.deletedMessages,
      required this.errorDetails,
      required this.totalAvailable});

  List<Object> get props => [deletedMessages!, errorDetails!, totalAvailable!];
}

// message envoyé avec succès
class StateMessagingMessageSentSuccess extends StateMessaging {
  final ErrorDetails? errorDetailsMessage;
  final ErrorDetails? errorDetailsPj;
  final BuildContext context;
  final bool isDraft;

  StateMessagingMessageSentSuccess(
      {required this.errorDetailsMessage,
      required this.errorDetailsPj,
      required this.context,
      required this.isDraft});

  List<Object> get props =>
      [errorDetailsMessage!, errorDetailsPj!, context, isDraft];
}

class StateMessagingAcknowledgeMessage extends StateMessaging {
  final Message message;

  StateMessagingAcknowledgeMessage({required this.message});

  List<Object> get props => [message];
}

// suppression d'un message
class StateMessagingMessageDeleted extends StateMessaging {
  final bool success;
  final String message;
  final bool reloadList;

  StateMessagingMessageDeleted(
      {required this.success, required this.message, required this.reloadList});

  List<Object> get props => [success, message, reloadList];
}

// lecture d'un message
class StateMessagingReadMessage extends StateMessaging {
  final int boite;
  final Message message;

  StateMessagingReadMessage({required this.boite, required this.message});

  List<Object> get props => [boite, message];
}

class StateMessagingUpdatedMailbox extends StateMessaging {
  final ErrorDetails? errorDetails;
  final List<Message> newMessageList;

  StateMessagingUpdatedMailbox({
    required this.errorDetails,
    required this.newMessageList,
  });

  List<Object> get props => [errorDetails!, newMessageList];
}
