import 'dart:io';
import 'package:flutter/material.dart';
import 'package:theradom/models/message.dart';

abstract class EventMessaging {
  List<Object> get props => [];
}

// clic pour voir les message reçus
class EventMessagingMailbox extends EventMessaging {}

// clic pour voir les messages envoyés
class EventMessagingSentMessages extends EventMessaging {}

// clic pour voir les messages dans la corbeille
class EventMessagingTrash extends EventMessaging {}

// charger d'autres message de la boite actuelle
class EventMessagingUpdateMailBox extends EventMessaging {
  final String option;
  final int from;
  final int to;

  EventMessagingUpdateMailBox(
      {required this.option, required this.from, required this.to});

  @override
  List<Object> get props => [option, from, to];
}

// clic pour envoyer un nouveau message
class EventMessagingSendNewMessage extends EventMessaging {
  final String destinataire;
  final String objet;
  final String contenu;
  final List<File?> fileList;
  final BuildContext context;
  final File? audioFile;
  final bool isDraft;

  EventMessagingSendNewMessage(
      {required this.destinataire,
      required this.objet,
      required this.contenu,
      required this.fileList,
      required this.context,
      required this.audioFile,
      required this.isDraft});

  @override
  List<Object> get props =>
      [destinataire, objet, contenu, fileList, context, audioFile!, isDraft];
}

// event quand le patient lit un nouveau message
class EventMessagingAcknowledgeMessaging extends EventMessaging {
  final Message message;

  EventMessagingAcknowledgeMessaging({required this.message});

  @override
  List<Object> get props => [message];
}

// event suppression d'un message
class EventMessagingDeleteMessage extends EventMessaging {
  final Message messageToDelete;
  final bool slideRemoval;

  EventMessagingDeleteMessage(
      {required this.messageToDelete, required this.slideRemoval});

  @override
  List<Object> get props => [messageToDelete, slideRemoval];
}

// lecture d'un message
class EventMessagingReadMessage extends EventMessaging {
  final int boite;
  final Message messageToRead;

  EventMessagingReadMessage({required this.boite, required this.messageToRead});

  @override
  List<Object> get props => [boite, messageToRead];
}
