import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/messaging/bloc_messaging.dart';
import 'package:theradom/blocs/messaging/event_messaging.dart';

class MessagingBlocProvider extends StatelessWidget {
  final Widget child;

  MessagingBlocProvider({required this.child}) : super();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) {
          return BlocMessaging()..add(EventMessagingMailbox());
        },
        child: child);
  }
}
