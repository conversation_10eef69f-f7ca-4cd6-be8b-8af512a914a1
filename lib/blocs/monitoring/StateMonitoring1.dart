import 'package:theradom/models/seance_precedente.dart';

abstract class StateMonitoring1 {
  const StateMonitoring1();

  List<Object> get props => [];
}

// interface non initialisée
class StateMonitoringUninitialized extends StateMonitoring1 {}

// loading apres validation de la séance
class StateMonitoringLoading extends StateMonitoring1 {
  final bool loading;

  StateMonitoringLoading({
    required this.loading,
  });

  @override
  List<Object> get props => [loading];
}

// interface du menu pricipal
class StateMonitoringInitial extends StateMonitoring1 {
  final List<SeancePrecedente> sessionListResponse;
  final String? error;

  const StateMonitoringInitial(
      {required this.sessionListResponse, required this.error});

  @override
  List<Object> get props => [sessionListResponse, error!];
}

class StateMonitoringNewSession extends StateMonitoring1 {
  final SeancePrecedente? seance;
  final String? error;

  StateMonitoringNewSession({required this.seance, required this.error});

  @override
  get props => [seance!, error!];
}
