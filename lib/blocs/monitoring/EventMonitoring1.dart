abstract class EventMonitoring1 {
  List<Object> get props => [];
}

// event de base de premier chargement de l'écran
class EventMonitorigMainScreen extends EventMonitoring1 {
  final String suivi;

  EventMonitorigMainScreen({required this.suivi});

  @override
  get props => [suivi];
}

// clic sur une nouvelle séance
class EventMonitoringNewSession extends EventMonitoring1 {
  final String suivi;
  final DateTime date;

  EventMonitoringNewSession({required this.suivi, required this.date});

  @override
  get props => [suivi, date];
}

// clic sur le btn création d'une séance passée
class EventMonitoringNewPreviousSession extends EventMonitoring1 {}
