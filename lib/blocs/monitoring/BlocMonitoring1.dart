import 'package:bloc/bloc.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/NotificationResponse.dart';
import 'package:theradom/models/PutSessionDataResponse.dart';
import 'package:theradom/models/SessionType.dart';
import 'package:theradom/models/UserID.dart';
import 'package:theradom/models/finished_list.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_notification_repository.dart';
import 'package:theradom/services/repositories/mobile_seance_repository.dart';
import 'package:theradom/blocs/monitoring/EventMonitoring1.dart';
import 'package:theradom/blocs/monitoring/StateMonitoring1.dart';
import 'package:theradom/models/BasicUserInfos.dart';
import 'package:theradom/models/PutSessionData.dart';
import 'package:theradom/models/SessionListResponse.dart';
import 'package:theradom/extensions/datetime_extension.dart';

class BlocMonitoring1 extends Bloc<EventMonitoring1, StateMonitoring1> {
  late final MobileSeanceRepository _mobileSeanceRepository;
  late final MobileNotificationRepository _mobileNotificationRepository;
  final BasicUserInfos _basicUserInfos = Config.basicUserInfos!;

  BlocMonitoring1(
      {MobileSeanceRepository? mobileSeanceRepository,
      MobileNotificationRepository? mobileNotificationRepository})
      : super(StateMonitoringUninitialized()) {
    _mobileSeanceRepository =
        mobileSeanceRepository ?? MobileSeanceRepository();
    _mobileNotificationRepository =
        mobileNotificationRepository ?? MobileNotificationRepository();
    on<EventMonitorigMainScreen>(_fetchSessions);
    on<EventMonitoringNewSession>(_createSession);
  }

  void _fetchSessions(
      EventMonitorigMainScreen event, Emitter<StateMonitoring1> emit) async {
    emit(StateMonitoringUninitialized());

    List<SeancePrecedente> previousSessions = [];
    String? error;

    try {
      SessionType sessionType =
          SessionType(numpat: _basicUserInfos.numpat, suivi: event.suivi);
      String sessionTypeJson = sessionTypeToJson(sessionType);
      SessionListResponse sessionListResponse =
          await _mobileSeanceRepository.fetchLastSessions(sessionTypeJson);
      previousSessions = sessionListResponse.seancesPrecedentes.values.toList();
    } on WebServiceCallException catch (e) {
      error = e.toString();
    }

    // Update des fake notifications pour mettre à jour la liste des séances non terminées
    try {
      UserID userID = UserID(numpat: Config.basicUserInfos!.numpat);
      String userIdJson = userIdToJson(userID);
      NotificationResponse notificationResponse =
          await _mobileNotificationRepository
              .fetchFakeNotifications(userIdJson);
      Config.notificationResponse = notificationResponse;
    } on WebServiceCallException catch (_) {
      Config.notificationResponse = NotificationResponse(
          valide: "OK",
          notifRdvAujourdhui: Config.notificationResponse != null
              ? Config.notificationResponse!.notifRdvAujourdhui
              : 0,
          notifMail: Config.notificationResponse != null
              ? Config.notificationResponse!.notifMail
              : 0,
          notifRdVnew: Config.notificationResponse != null
              ? Config.notificationResponse!.notifRdVnew
              : 0,
          notifRdVsoon: Config.notificationResponse != null
              ? Config.notificationResponse!.notifRdVsoon
              : 0,
          endSeance: Config.notificationResponse != null
              ? Config.notificationResponse!.endSeance
              : FinishedList(empty: true),
          teleconsultation: Config.notificationResponse != null
              ? Config.notificationResponse!.teleconsultation
              : "",
          seanceNonFinie: Config.notificationResponse != null
              ? Config.notificationResponse!.seanceNonFinie
              : {});
    }

    emit(StateMonitoringInitial(
        sessionListResponse: previousSessions, error: error));
  }

  void _createSession(
      EventMonitoringNewSession event, Emitter<StateMonitoring1> emit) async {
    emit(StateMonitoringLoading(loading: true));

    try {
      PutSessionData putSessionData = PutSessionData(
          numpat: _basicUserInfos.numpat,
          suivi: event.suivi,
          numSeance: "-1",
          dateSeance: event.date.toYYYYMMDD());
      String putSessionDataJson = putSessionDataToJson(putSessionData);
      PutSessionDataResponse putSessionDataResponse =
          await _mobileSeanceRepository.pushSessionData(putSessionDataJson);
      SeancePrecedente seance = SeancePrecedente(
          verrou: false,
          numSeance: putSessionDataResponse.numSeance,
          dateSeance: event.date);
      emit(StateMonitoringLoading(loading: false));
      emit(StateMonitoringNewSession(error: null, seance: seance));
    } on WebServiceCallException catch (e) {
      emit(StateMonitoringLoading(loading: false));
      emit(StateMonitoringNewSession(
        seance: null,
        error: e.toString(),
      ));
    }
  }
}
