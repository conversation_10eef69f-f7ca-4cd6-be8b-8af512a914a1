import 'dart:convert';
import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:theradom/blocs/usability_questionnaire/event_usability_questionnaire.dart';
import 'package:theradom/blocs/usability_questionnaire/state_usability_questionnaire.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/file_object.dart';
import 'package:theradom/models/new_account_data.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_parametres_repository.dart';
import 'package:theradom/services/repositories/send_email_repository.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

class BlocUsabilityQuestionnaire
    extends Bloc<EventUsabilityQuestionnaire, StateUsabiltyQuestionnaire> {
  late final SendEmailRepository _sendEmailRepository;
  late final MobileParametresRepository _mobileParametresRepository;

  BlocUsabilityQuestionnaire({
    SendEmailRepository? sendEmailRepository,
    MobileParametresRepository? mobileParametresRepository,
  }) : super(QuestionnaireToFill()) {
    _sendEmailRepository = sendEmailRepository ?? SendEmailRepository();
    _mobileParametresRepository =
        mobileParametresRepository ?? MobileParametresRepository();
    on<SendCompletedQuestionnaire>(_sendQuestionnaire);
  }

  void _sendQuestionnaire(
    SendCompletedQuestionnaire event,
    Emitter<StateUsabiltyQuestionnaire> emit,
  ) async {
    bool success = true;
    String message = "L'équipe EMA vous remercie pour votre retour !";

    emit(QuestionnaireSaving(loading: true));

    String fileName = DateTime.now().toYYYYMMDD() + ".csv";

    File csvFile = await HelperPj.createCsvFile(event.csvContent, fileName);
    String csvContent = csvFile.readAsStringSync();

    String csvContentHtml =
        csvContent.replaceAll('\r\n', '<br>').replaceAll('\n', '<br>');

    String base64Content = base64Encode(csvFile.readAsBytesSync());

    String rawMessage = "Date: ${DateTime.now().toLanguageString()}\n"
        "Heure: ${TimeOfDay.now().toLanguageString()}\n\n"
        "Évaluation d'un patient concernant Théradom ${Config.appVersion}.\n"
        "Version du formulaire utilisé: v.${event.formVersion}";

    String htmlMessage = rawMessage.replaceAll('\n', '<br>');

    try {
      await _sendEmailRepository.sendEmail(
        to: Config.evalFormReceptionAddress,
        subject: "[THERADOM] Questionnaire d'utilisabilité complété",
        message: htmlMessage,
        fileName: fileName,
        fileContent: csvContentHtml,
      );

      try {
        NewAccountData newAccountData = NewAccountData(
          numpat: Config.basicUserInfos!.numpat,
          option: "1",
          value: event.formVersion.toString(),
          file: FileObject(name: fileName, content: base64Content),
        );

        String newAccountDataJson = newAccountDataToJson(newAccountData);

        await _mobileParametresRepository
            .changeUserInformationAccount(newAccountDataJson);

        Config.basicUserInfos!.lastEvaluation = event.formVersion;
      } on WebServiceCallException catch (e) {
        success = false;
        message = e.toString();
      }
    } on WebServiceCallException catch (e) {
      success = false;
      message = e.toString();
    }

    emit(QuestionnaireSaving(loading: false));
    emit(QuestionnaireSent(success: success, message: message));
  }
}
