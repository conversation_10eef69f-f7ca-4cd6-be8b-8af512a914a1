abstract class StateUsabiltyQuestionnaire {
  List<Object> get props => [];
}

class QuestionnaireToFill extends StateUsabiltyQuestionnaire {}

class QuestionnaireSaving extends StateUsabiltyQuestionnaire {
  final bool loading;

  QuestionnaireSaving({required this.loading});

  @override
  List<Object> get props => [loading];
}

class QuestionnaireSent extends StateUsabiltyQuestionnaire {
  final bool success;
  final String message;

  QuestionnaireSent({required this.success, required this.message});

  @override
  List<Object> get props => [success, message];
}
