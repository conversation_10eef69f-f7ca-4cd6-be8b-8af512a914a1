import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/drug_short.dart';

abstract class StatePrescription {
  List<Object> get props => [];
}

// état non initialisée
class StatePrescriptionUninitialized extends StatePrescription {}

// état affichage prescription
class StatePrescriptionScreen extends StatePrescription {
  final List<DrugShort> drugShortList;
  final Map<String, dynamic> prescriptionMap;
  final ErrorDetails?
      errorDetails; // true si c'est une séance, false si c'est une consultation

  StatePrescriptionScreen(
      {required this.drugShortList,
      required this.prescriptionMap,
      required this.errorDetails});

  @override
  List<Object> get props => [drugShortList, prescriptionMap, errorDetails!];
}
