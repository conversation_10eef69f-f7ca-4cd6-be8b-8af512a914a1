import 'package:bloc/bloc.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/GetPrescriptionRequest.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_seance_repository.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/blocs/prescription/EventPrescription.dart';
import 'package:theradom/blocs/prescription/StatePrescription.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/GetPrescriptionResponse.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/extensions/datetime_extension.dart';

class BlocPrescription extends Bloc<EventPrescription, StatePrescription> {
  final String suivi;
  late final MobileSeanceRepository _mobileSeanceRepository;

  BlocPrescription(
      {required this.suivi, MobileSeanceRepository? mobileSeanceRepository})
      : super(StatePrescriptionUninitialized()) {
    _mobileSeanceRepository =
        mobileSeanceRepository ?? MobileSeanceRepository();
    on<EventPrescriptionScreen>(_loadPrescription);
  }

  void _loadPrescription(
      EventPrescriptionScreen event, Emitter<StatePrescription> emit) async {
    // demande de la prescription
    emit(StatePrescriptionUninitialized());
    bool estSeance = MonitoringUtils.isMonitoringSession(suivi);
    String date = DateTime.now().toYYYYMMDD();

    try {
      GetPrescriptionRequest getPrescriptionRequest = GetPrescriptionRequest(
          numpat: Config.basicUserInfos!.numpat,
          suivi: suivi,
          numSeance: "-1",
          cDate: date,
          details: "total",
          typeRecherche: "2");
      String getPrescriptionRequestJson =
          getPrescriptionRequestToJson(getPrescriptionRequest);
      GetPrescriptionResponse getPrescriptionResponse =
          await _mobileSeanceRepository
              .fetchMonitoringPrecription(getPrescriptionRequestJson);
      List listObjPrescription =
          _prescriptionObjToList(getPrescriptionResponse, estSeance);
      emit(StatePrescriptionScreen(
          errorDetails: null,
          drugShortList: listObjPrescription[0],
          prescriptionMap: listObjPrescription[1]));
    } on WebServiceCallException catch (e) {
      emit(StatePrescriptionScreen(
          errorDetails: ErrorDetails(code: "", message: e.toString()),
          drugShortList: [],
          prescriptionMap: {}));
    }
  }

  List _prescriptionObjToList(
      GetPrescriptionResponse getPrescriptionResponse, bool estSeance) {
    // retourne les différents éléments au bon format pour l'affichage

    Map<String, dynamic> prescriptionMap = {};
    Map<String, String> othersMap = {};

    getPrescriptionResponse.prescription
        .forEach((String areaName, jsonPrescription) {
      if ((jsonPrescription.toString().contains('{')) &&
          (jsonPrescription.toString().contains('}'))) {
        // c'est un nested json
        Map<String, String> nestedMap = {};
        jsonPrescription.forEach((key, jsonData) {
          nestedMap[key] = _getCorrectedName(jsonData);
        });
        prescriptionMap[areaName] = nestedMap;
      } else {
        // simple
        othersMap[areaName] = _getCorrectedName(jsonPrescription);
      }
    });

    if (othersMap.isNotEmpty) {
      String otherStr = othersMap.keys.length > 1
          ? allTranslations.text('other_plural')
          : allTranslations.text('other');
      prescriptionMap[otherStr] = othersMap;
    }

    return [getPrescriptionResponse.traitements, prescriptionMap];
  }

  String _getCorrectedName(dynamic info) {
    // correction car peut être un booléen...
    String res;
    if (info is bool) {
      res = info ? allTranslations.text('true') : allTranslations.text('false');
    } else {
      res = info.toString();
    }
    return res;
  }
}
