import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/login/UserRepository.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/PictureSpecs.dart';
import 'package:theradom/models/UserAccountInfos.dart';
import 'package:theradom/services/repositories/mobile_donne_bandeau_repository.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:theradom/utils/fakenotifications/FakeNotifTimerUtils.dart';
import 'package:theradom/utils/log_out_timer/logout_timer_stream.dart';
import 'package:theradom/utils/Translations.dart';
import 'StateAuthentication.dart';
import 'package:theradom/services/web_service_call_exception.dart';

class BlocAuthentication
    extends Bloc<EventAuthentication, StateAuthentication> {
  MobileDonneBandeauRepository? _mobileDonneBandeauRepository;
  late final UserRepository _userRepository;

  BlocAuthentication(
      {MobileDonneBandeauRepository? mobileDonneBandeauRepository,
      UserRepository? userRepository})
      : super(StateAuthenticationAppFirstState()) {
    _userRepository = userRepository ?? UserRepository();
    on<EventAppStarted>(_onAppStart);
    on<EventLoggedIn>(_onLogIn);
    on<EventAuthenticationAskConfirmation>(_onAskAuthConfirmation);
    on<EventAuthenticationIdentityConfirmed>(_onIdentificationConfirmed);
    on<EventLoggedOut>(_onLogOut);
    on<EventAuthenticationFirstPasswordChange>(_onFirstPwdChange);
  }

  void _onAppStart(
      EventAppStarted event, Emitter<StateAuthentication> emit) async {
    // suppression fichiers temp
    HelperPj.deleteTempFiles();

    if ((Config.nomDomaine == "") || (Config.firstUseState == 0)) {
      // aucun QR code n'a jamais été flashé || user n'a pas modifié son password, affiche l'onboarding de login
      emit(StateAuthenticationScanQrCode());
    } else {
      emit(StateAuthenticationUnauthenticated());
    }
  }

  void _onLogIn(EventLoggedIn event, Emitter<StateAuthentication> emit) async {
    emit(StateAuthenticationLoading(
        message: allTranslations.text("connection_loading")));

    await _userRepository.connectUser();
    Config.basicUserInfos = event.basicUserInfos;
    _startTimer();
    await _startFakeNotifTimer();

    try {
      PictureSpecs pictureSpecs = PictureSpecs(
          numpat: event.basicUserInfos.numpat,
          color1: "rgb(10,10,10)",
          color2: "rgb(10,10,10)",
          color3: "rgb(10,10,10)",
          width: "100",
          height: "100",
          fontSize: "20");
      String pictureSpecsJson = pictureSpecsToJson(pictureSpecs);
      if (_mobileDonneBandeauRepository == null)
        _mobileDonneBandeauRepository = MobileDonneBandeauRepository();
      UserAccountInfos userAccountInfos =
          await _mobileDonneBandeauRepository!.fetchUserInfos(pictureSpecsJson);
      Config.userAccountInfos = userAccountInfos;
      Config.userAccountInfos!.login = event.login;
      Config.userAccountInfos!.passwordHash = event.password;
      if (event.newAppId) {
        emit(StateAuthentificationOTPRequired(
            title: "nouvel appareil detecté",
            message: "entrer le code reçu par email",
            email: Config.basicUserInfos!.mail,
            ins: Config.userAccountInfos!.INS));
      } else {
        emit(StateAuthenticationAuthenticated());
      }
      emit(StateAuthenticationConfirmIdentity(
          newAppId: event.newAppId, MFA: event.MFA));
    } on WebServiceCallException catch (_) {
      // print("NUMPAT : " + Config.userAccountInfos!.login);
      // print(_.toString());
      emit(StateAuthenticationUnauthenticated());
    }
  }

  void _onAskAuthConfirmation(EventAuthenticationAskConfirmation event,
      Emitter<StateAuthentication> emit) async {
    emit(StateAuthenticationConfirmIdentity(newAppId: false, MFA: false));
  }

  void _onIdentificationConfirmed(EventAuthenticationIdentityConfirmed event,
      Emitter<StateAuthentication> emit) async {
    emit(StateAuthenticationAuthenticated());
  }

  void _onLogOut(
      EventLoggedOut event, Emitter<StateAuthentication> emit) async {
    emit(StateAuthenticationLoading(
        message: allTranslations.text("deconnection_loading")));
    _stopTimer();
    _stopFakeNotifTimer();
    await _userRepository.disconnectUser();
    Config.basicUserInfos = null;
    await Future.delayed(Duration(seconds: 1));
    emit(StateAuthenticationUnauthenticated());
  }

  void _onFirstPwdChange(EventAuthenticationFirstPasswordChange event,
      Emitter<StateAuthentication> emit) async {
    emit(StateAuthenticationChangePassword(userIdentity: event.userIdentity));
  }

  Future _startFakeNotifTimer() async {
    // démarre le timer pour récupérer les fake notifs et les initialise
    FakeNotifTimerUtils();
    await FakeNotifTimerUtils.startTimer();
  }

  void _stopFakeNotifTimer() {
    // stop le timer des fake notifications
    FakeNotifTimerUtils.stopTimer();
  }

  void _startTimer() {
    // start le timer de déconnexion auto si il y en a un défini
    LogOutTimerStream.instance.start();
  }

  void _stopTimer() {
    // start le timer de déconnexion auto
    LogOutTimerStream.instance.stop();
  }
}
