import 'package:theradom/models/BasicUserInfos.dart';

abstract class EventAuthentication {
  const EventAuthentication();

  List<Object> get props => [];
}

// pour notifier le bloc qu'il faut vérifier si l'utilisateur est authentifié ou non
class EventAppStarted extends EventAuthentication {}

// l'utilisateur s'est authentifié avec succès
class EventLoggedIn extends EventAuthentication {
  final String login;
  final String password;
  final BasicUserInfos basicUserInfos;
  final bool newAppId;
  final bool MFA;

  const EventLoggedIn(
      {required this.login,
      required this.password,
      required this.basicUserInfos,
      required this.newAppId,
      required this.MFA});

  @override
  String toString() {
    return 'EventLoggedIn{props: $props, basicUserInfos: $basicUserInfos}';
  }

  List<Object> get props => [basicUserInfos];
}

// l'utilisateur se déconnecte
class EventLoggedOut extends EventAuthentication {}

class EventNotificationPwdChange extends EventAuthentication {}

// l'utilisateur va devoir confirmer ou non son identité
class EventAuthenticationAskConfirmation extends EventAuthentication {}

// EventAuthenticationIdentityConfirmed
class EventAuthenticationIdentityConfirmed extends EventAuthentication {}

// premier changement de mot de passe
class EventAuthenticationFirstPasswordChange extends EventAuthentication {
  final String userIdentity;

  const EventAuthenticationFirstPasswordChange({required this.userIdentity});

  @override
  List<Object> get props => [userIdentity];
}
