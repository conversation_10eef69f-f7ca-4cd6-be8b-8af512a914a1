import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/login/UserRepository.dart';

class AuthenticationBlocProvider extends StatelessWidget {
  final Widget child;

  AuthenticationBlocProvider({required this.child});

  final userRepository = UserRepository();

  @override
  Widget build(BuildContext context) {
    return BlocProvider<BlocAuthentication>(
      create: (context) {
        return BlocAuthentication(userRepository: userRepository)
          ..add(EventAppStarted());
      },
      child: child,
    );
  }
}
