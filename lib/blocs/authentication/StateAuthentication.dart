abstract class StateAuthentication {
  const StateAuthentication();

  List<Object> get props => [];
}

class StateAuthenticationAppFirstState extends StateAuthentication {}

// authentification réussie
class StateAuthenticationAuthenticated extends StateAuthentication {}

// authentification échouée
class StateAuthenticationUnauthenticated extends StateAuthentication {}

// en cours d'authentification
class StateAuthenticationLoading extends StateAuthentication {
  final String message;

  StateAuthenticationLoading({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

class StateAuthenticationScanQrCode extends StateAuthentication {}

class StateAuthenticationChangePassword extends StateAuthentication {
  final String userIdentity;

  const StateAuthenticationChangePassword({required this.userIdentity});

  @override
  List<Object> get props => [userIdentity];
}

class StateAuthentificationOTPRequired extends StateAuthentication {
  final String title;
  final String message;
  final String email;
  final String ins;

  const StateAuthentificationOTPRequired(
      {required this.title,
      required this.message,
      required this.email,
      required this.ins});

  @override
  List<Object> get props => [title, message, email, ins];
}

class StateAuthenticationConfirmIdentity extends StateAuthentication {
  final bool newAppId;
  final bool MFA;

  const StateAuthenticationConfirmIdentity(
      {required this.newAppId, required this.MFA});

  @override
  List<Object> get props => [newAppId, MFA];
}
