import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/monitoring_details/bloc_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/event_monitoring_details.dart';
import 'package:theradom/models/seance_precedente.dart';

class MonitoringDetailsBlocProvider extends StatelessWidget {
  final Widget child;
  final bool estTerminee;
  final String suivi;
  final SeancePrecedente seancePrecedente;

  MonitoringDetailsBlocProvider(
      {required this.child,
      required this.estTerminee,
      required this.suivi,
      required this.seancePrecedente});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<BlocMonitoringDetails>(
        create: (context) {
          return BlocMonitoringDetails(suivi: suivi, seance: seancePrecedente)
            ..add(EventMonitoringDetailsPreviousSession(
                estTerminee: estTerminee, tabReload: false));
        },
        child: child);
  }
}
