import 'package:flutter/material.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/GetDrugsResponse.dart';
import 'package:theradom/models/session_sampling_data_to_save.dart';
import 'package:theradom/models/session_sampling_structure.dart';

abstract class StateMonitoringDetails {
  const StateMonitoringDetails();

  List<Object> get props => [];
}

// interface non initialisée
class StateMonitoringDetailsUninitialized extends StateMonitoringDetails {}

// loading apres validation de la séance
class StateMonitoringDetailsLoading extends StateMonitoringDetails {
  final bool loading;

  StateMonitoringDetailsLoading({
    required this.loading,
  });

  @override
  List<Object> get props => [loading];
}

class StateMonitoringDetailsSession extends StateMonitoringDetails {
  final bool estTerminee;
  final SessionSamplingDataToSave? sessionSamplingManualData;
  final SessionSamplingDataToSave? sessionSamplingBoxData;
  final Widget sectionsListBody;
  final SessionSamplingStructure? sessionSamplingStructureTab;
  final SessionSamplingStructure? sessionSamplingStructureBox;
  final ErrorDetails? errorDetailsSessionData;
  final GetDrugsResponse? getDrugsResponse;
  final ErrorDetails? errorDetailsDrugsResponse;
  final TimeOfDay? heureFinSeance;

  StateMonitoringDetailsSession(
      {required this.estTerminee,
      required this.sessionSamplingManualData,
      required this.sessionSamplingBoxData,
      required this.sectionsListBody,
      required this.sessionSamplingStructureTab,
      required this.sessionSamplingStructureBox,
      required this.errorDetailsSessionData,
      required this.getDrugsResponse,
      required this.errorDetailsDrugsResponse,
      required this.heureFinSeance});

  @override
  get props => [
        estTerminee,
        sessionSamplingManualData!,
        sessionSamplingBoxData!,
        sectionsListBody,
        sessionSamplingStructureTab!,
        sessionSamplingStructureBox!,
        errorDetailsSessionData!,
        getDrugsResponse!,
        errorDetailsDrugsResponse!,
        heureFinSeance!
      ];
}

class StateMonitoringDetailsSessionValidation extends StateMonitoringDetails {
  final bool autoSave;
  final ErrorDetails? errorDetailsData;
  final ErrorDetails? errorDetailsDrugStatus;
  final bool rebuild;

  StateMonitoringDetailsSessionValidation(
      {required this.autoSave,
      required this.errorDetailsData,
      required this.errorDetailsDrugStatus,
      required this.rebuild});

  @override
  get props => [autoSave, errorDetailsData!, errorDetailsDrugStatus!, rebuild];
}

class StateMonitoringDetailsReloadFiles extends StateMonitoringDetails {
  final String? error;
  final bool
      fileAdded; // indique si un fichier viens d'être ajouté, true, false si fichier supprimé

  StateMonitoringDetailsReloadFiles(
      {required this.error, required this.fileAdded});

  @override
  get props => [error!, fileAdded];
}

class StateMonitoringDetailsSaveSessionAfterSamplingEditing
    extends StateMonitoringDetails {}
