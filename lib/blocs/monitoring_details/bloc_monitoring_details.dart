import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:theradom/blocs/monitoring_details/event_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/state_monitoring_details.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/DeleteFileRequest.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/GetDrugsRequest.dart';
import 'package:theradom/models/GetDrugsResponse.dart';
import 'package:theradom/models/NewFileRequest.dart';
import 'package:theradom/models/PutDataDescription.dart';
import 'package:theradom/models/PutDrugStatus.dart';
import 'package:theradom/models/PutSessionData.dart';
import 'package:theradom/models/SessionDetailsResponse.dart';
import 'package:theradom/models/SessionIDInfo.dart';
import 'package:theradom/models/consumable_details.dart';
import 'package:theradom/models/drug_details.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/models/session_sampling_data_to_save.dart';
import 'package:theradom/models/session_sampling_structure.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_pj_repository.dart';
import 'package:theradom/services/repositories/mobile_seance_repository.dart';
import 'package:theradom/services/repositories/mobile_stock_repository.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsSectionListBuilder.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/extensions/timeofday_extension.dart';
import 'package:theradom/extensions/string_extension.dart';

class BlocMonitoringDetails
    extends Bloc<EventMonitoringDetails, StateMonitoringDetails> {
  final SeancePrecedente seance;
  final String suivi;
  late final MobileSeanceRepository _mobileSeanceRepository;
  late final MobileStockRepository _mobileStockRepository;
  late final MobilePjRepository _mobilePjRepository;
  bool? _normalite;
  final String _numpat = Config.basicUserInfos!.numpat;
  late DateTime? _dateDebutSeance;
  late TimeOfDay? _heureDebutSeance;
  TextEditingController _commentController = TextEditingController();
  late List<String> _fileNameDbList = [];
  late List<File> _listPickedFiles = []; // files à ajouter
  late List<ConsumableDetails> _listConsumablesUpdated = [];
  late List<DrugDetails> _listTraitementUpdated = [];
  late List<PutDataDescription> _putDataDescriptionList = [];
  TimeOfDay? _heureFin;
  late bool _finSeance;
  late SessionSamplingDataToSave? _sessionSamplingDataToSave;
  late SessionSamplingStructure? _sessionSamplingStructureTab;
  late SessionSamplingStructure? _sessionSamplingStructureBox;
  late bool _isSession;

  BlocMonitoringDetails(
      {required this.seance,
      required this.suivi,
      MobileSeanceRepository? mobileSeanceRepository,
      MobileStockRepository? mobileStockRepository,
      MobilePjRepository? mobilePjRepository})
      : super(StateMonitoringDetailsUninitialized()) {
    _mobileSeanceRepository =
        mobileSeanceRepository ?? MobileSeanceRepository();
    _mobileStockRepository = mobileStockRepository ?? MobileStockRepository();
    _mobilePjRepository = mobilePjRepository ?? MobilePjRepository();
    on<EventMonitoringDetailsPreviousSession>(_fetchSessionDetails);
    on<EventMonitoringDetailsValidateSession>(_saveSessionData);
    on<EventMonitoringDetailsSaveSessionAfterSamplingEditing>(
        _saveSessionDataAfterSamplingEdition);
    on<EventMonitoringDetailsSaveFile>(_saveSessionFile);
    on<EventMonitoringDetailsDeleteCommentFile>(_deleteSessionFile);
  }

  void _fetchSessionDetails(EventMonitoringDetailsPreviousSession event,
      Emitter<StateMonitoringDetails> emit) async {
    // suppression du contenu des variables
    _clearBlocData(true);

    // variables pour instance du state
    ErrorDetails? yErrorDetailsSessionData;
    GetDrugsResponse? yGetDrugsResponse;
    ErrorDetails? yErrorDetailsDrugsResponse;
    SessionSamplingDataToSave? sessionSamplingManualData;
    SessionSamplingDataToSave? sessionSamplingBoxData;
    Widget sectionListBody;
    SessionDetailsResponse? sessionDetailsResponse;

    _isSession = MonitoringUtils.isMonitoringSession(suivi);

    if (!event.tabReload) emit(StateMonitoringDetailsUninitialized());

    //////////////////////////////

    // get les data de la séance
    try {
      final sessionIDinfo = SessionIDInfo(
          numpat: Config.basicUserInfos!.numpat,
          numSeance: seance.numSeance,
          suivi: suivi,
          locked: seance.verrou,
          past: true);
      final sessionIdInfoJson = sessionIDInfoToJson(sessionIDinfo);
      sessionDetailsResponse =
          await _mobileSeanceRepository.fetchSessionData(sessionIdInfoJson);
      yErrorDetailsSessionData = null;
      _normalite = sessionDetailsResponse!.seance.structure.normalite;

      // date de début de séance
      _dateDebutSeance = seance.dateSeance;

      // échantillonage
      _sessionSamplingStructureTab =
          sessionDetailsResponse.seance.structure.table;
      _sessionSamplingStructureBox =
          sessionDetailsResponse.seance.structure.box;
      sessionSamplingManualData =
          sessionDetailsResponse.seance.structure.table.fieldID.isEmpty
              ? null
              : sessionSamplingDataToSaveFromStructureAndValues(
                  _sessionSamplingStructureTab!,
                  sessionDetailsResponse.seance.values.table);
      sessionSamplingBoxData =
          sessionDetailsResponse.seance.structure.box.fieldID.isEmpty
              ? null
              : sessionSamplingDataToSaveFromStructureAndValues(
                  _sessionSamplingStructureBox!,
                  sessionDetailsResponse.seance.values.box);
      _sessionSamplingDataToSave = sessionSamplingManualData;

      // commentaire
      _commentController.text =
          sessionDetailsResponse.seance.values.unique.commentaire;

      if (_isSession) {
        // produits et traitements en séance
        if ((suivi != "6") && (suivi != "7")) {
          String dateSeanceStr = seance.dateSeance.toYYYYMMDD();
          try {
            final getDrugsRequest = GetDrugsRequest(
                numSeance: seance.numSeance,
                numpat: Config.basicUserInfos!.numpat,
                suivi: suivi,
                date: dateSeanceStr);
            final getDrugsRequestJson = getDrugsRequestToJson(getDrugsRequest);
            GetDrugsResponse getDrugsResponse = await _mobileStockRepository
                .fetchDrugsAndConsumables(getDrugsRequestJson);
            yGetDrugsResponse = getDrugsResponse;
            yErrorDetailsDrugsResponse = null;
          } on WebServiceCallException catch (_) {
            yGetDrugsResponse = null;
            yErrorDetailsDrugsResponse = ErrorDetails(
                code: "",
                message: allTranslations.text("error_fetching_treatments"));
          }
        }

        // heure et date de fin de séance
        _finSeance = sessionDetailsResponse.seance.values.unique.finSeance!;
        _heureFin = sessionDetailsResponse.seance.values.unique.heureFin!
            .hhmm2TimeOfDay();
        String heureDebutStr =
            sessionDetailsResponse.seance.values.unique.heureDeb!;
        _heureDebutSeance = heureDebutStr.hhmm2TimeOfDay();
      }
    } on WebServiceCallException catch (e) {
      yErrorDetailsSessionData = ErrorDetails(code: "", message: e.toString());
    }

    // zones de saisies
    sectionListBody = SessionMonitorinfDetailsSectionListBuilder(
        seance: seance,
        suivi: suivi,
        onTimeSelected: _updateHeureDeb,
        sessionDetailsResponse: sessionDetailsResponse,
        getDrugsResponse: yGetDrugsResponse,
        onTreatmentUpdate: _updateGetDrugsResponseDrug,
        onConsumableUpdate: _updateGetDrugsResponseConsumable,
        normalite: _normalite!,
        initialSamplingBoxData: sessionSamplingBoxData,
        initialSamplingManualData: sessionSamplingManualData,
        onSessionSamplingDataUpdate: (sessionSamplingDataToSave) =>
            _sessionSamplingDataToSave = sessionSamplingDataToSave,
        commentTextfield: TextField(
          cursorColor: AppTheme.primaryColor,
          style: TextStyle(
              fontSize:
                  FontSizeController.of(Config.appKey.currentContext!).fontSize,
              color: seance.verrou
                  ? AppTheme.backgroundColor
                  : AppTheme.primaryColor,
              fontFamily: "OpenSans"),
          keyboardType: TextInputType.multiline,
          enabled: !seance.verrou,
          controller: _commentController,
          autofocus: false,
          autocorrect: true,
          obscureText: false,
          maxLines: 10,
          textAlignVertical: TextAlignVertical.top,
          decoration: InputDecoration(
            border: InputBorder.none,
            hintText: allTranslations.text('comment'),
            hintStyle: TextStyle(
                color:
                    seance.verrou ? Colors.transparent : AppTheme.primaryColor,
                fontSize: FontSizeController.of(Config.appKey.currentContext!)
                    .fontSize),
          ),
        ),
        onDbFilesFetched: _updateDbFilesList,
        onValueSelected: _updateSessionDetailsResponse,
        addToPutDataDesctiptionList: _addToPutDataDescriptionList);

    emit(StateMonitoringDetailsSession(
        estTerminee: event.estTerminee,
        sessionSamplingManualData: sessionSamplingManualData,
        sessionSamplingBoxData: sessionSamplingBoxData,
        sectionsListBody: sectionListBody,
        sessionSamplingStructureTab: _sessionSamplingStructureTab,
        sessionSamplingStructureBox: _sessionSamplingStructureBox,
        errorDetailsDrugsResponse: yErrorDetailsDrugsResponse,
        errorDetailsSessionData: yErrorDetailsSessionData,
        getDrugsResponse: yGetDrugsResponse,
        heureFinSeance: _heureFin));
  }

  void _saveSessionData(EventMonitoringDetailsValidateSession event,
      Emitter<StateMonitoringDetails> emit) async {
    ErrorDetails? yErrorDetailsData;
    ErrorDetails? yErrorDetailsDrugStatus;

    if (!event.autoSave) emit(StateMonitoringDetailsLoading(loading: true));

    String? heureSeanceTerminee = _isSession
        ? event.heureFinSeance != null
            ? event.heureFinSeance?.toLongLanguageString()
            : _heureFin!.isMidnight()
                ? null
                : _heureFin!.toLongLanguageString()
        : null;

    if (_isSession) {
      //cas d'une séance

      // check la temporalité des traitements et produits renseignés
      List checkDrugStatusList = _checkTreatmentsDates(
          heureSeanceTerminee != null
              ? heureSeanceTerminee.hhmm2TimeOfDay()
              : null,
          event.dateFinSeance);

      // check la cohérence de date pour la prise des traitements/produits
      bool canSaveDrugs = checkDrugStatusList[0];
      List<String> wrongDrugList = checkDrugStatusList[1];

      // check la cohérence des dates pour l'échantillonage manuel
      bool isSamplingOk = _isSession
          ? _checkSamplingDates(event.heureFinSeance, event.dateFinSeance,
              _sessionSamplingDataToSave)
          : true;

      // check la cohérence des dates pour les dates de début et de fin de séance
      bool isSessionOk = _isSession
          ? _checkSessionDates(event.heureFinSeance, event.dateFinSeance)
          : true;

      // check la cohérence des dates de début et fin de séance
      bool isTimeOk = _isSession
          ? _checkSessionTimes(event.heureFinSeance, event.dateFinSeance)
          : true;

      if (canSaveDrugs && isSamplingOk && isSessionOk && isTimeOk) {
        // on peu sauvegarder la séance et les traitements

        // sauvegarde des traitements et produits
        try {
          PutDrugStatus putDrugStatus = PutDrugStatus(
              numpat: _numpat,
              numSeance: seance.numSeance,
              suivi: suivi,
              produits: _listConsumablesUpdated,
              traitements: _listTraitementUpdated);
          String putDrugStatusJson = putDrugStatusToJson(putDrugStatus);
          await _mobileStockRepository
              .pushDrugsAndConsumablesStatus(putDrugStatusJson);
        } on WebServiceCallException catch (e) {
          yErrorDetailsDrugStatus =
              ErrorDetails(code: "", message: e.toString());
        }

        // sauvegarde des données de la séance
        bool terminerSeance = _isSession
            ? _finSeance
                ? true
                : event.finSeance
            : false; // si c'est une séance, puis si séance déjà terminée
        final rep = await _pushSessionData(
            event.autoSave, terminerSeance, heureSeanceTerminee ?? "");
        if (rep is ErrorDetails) {
          yErrorDetailsData = rep;
        }
      } else {
        // on ne peut pas sauvegarder la séance ni les traitements
        String? errorSaveMessage;
        if (!canSaveDrugs)
          errorSaveMessage = allTranslations.text(
              'save_drug_date_time_error_msg', {"elemList": "$wrongDrugList"});
        if (!isSamplingOk)
          errorSaveMessage = (errorSaveMessage != null ? "\n\n" : "") +
              allTranslations.text('check_sampling_dates_error');
        if (!isSessionOk)
          errorSaveMessage = (errorSaveMessage != null ? "\n\n" : "") +
              allTranslations.text('check_dates_error');
        if (!isTimeOk)
          errorSaveMessage = (errorSaveMessage != null ? "\n\n" : "") +
              allTranslations.text('check_time_error');
        yErrorDetailsDrugStatus =
            ErrorDetails(code: "", message: errorSaveMessage!);
      }
    } else {
      // cas d'un suivi

      // sauvegarde des données de la séance
      final rep = await _pushSessionData(
          event.autoSave, false, heureSeanceTerminee ?? "");
      if (rep is ErrorDetails) {
        yErrorDetailsData = rep;
      }
    }

    if (!event.autoSave) emit(StateMonitoringDetailsLoading(loading: false));

    if ((yErrorDetailsDrugStatus == null) && (yErrorDetailsData == null)) {
      _clearBlocData(false);
    }

    emit(StateMonitoringDetailsSessionValidation(
        autoSave: event.autoSave,
        errorDetailsDrugStatus: yErrorDetailsDrugStatus,
        errorDetailsData: yErrorDetailsData,
        rebuild:
            ((yErrorDetailsDrugStatus == null) && (yErrorDetailsData == null))
                ? event.finSeance
                : false));
  }

  void _saveSessionDataAfterSamplingEdition(
      EventMonitoringDetailsSaveSessionAfterSamplingEditing event,
      Emitter<StateMonitoringDetails> emit) async {
    emit(StateMonitoringDetailsSaveSessionAfterSamplingEditing());
  }

  void _saveSessionFile(EventMonitoringDetailsSaveFile event,
      Emitter<StateMonitoringDetails> emit) async {
    emit(StateMonitoringDetailsLoading(loading: true));
    var rep = await _saveFile(event.file);
    emit(StateMonitoringDetailsLoading(loading: false));
    if (rep is ErrorDetails) {
      emit(StateMonitoringDetailsReloadFiles(
          error: rep.message, fileAdded: true));
    } else {
      emit(StateMonitoringDetailsReloadFiles(error: null, fileAdded: true));
    }
  }

  void _deleteSessionFile(EventMonitoringDetailsDeleteCommentFile event,
      Emitter<StateMonitoringDetails> emit) async {
    emit(StateMonitoringDetailsLoading(loading: true));
    String tableId = MonitoringUtils.getCorrespondingTable(suivi);
    String name = event.file.path.split("/").last;
    try {
      DeleteFileRequest deleteFileRequest = DeleteFileRequest(
          numpat: Config.basicUserInfos!.numpat,
          table: tableId,
          numSeanceMail:
              _isSession ? seance.numSeance : seance.numSeance.padLeft(12, "0"),
          numPj: name);
      String deleteFileRequestJson = deleteFileRequestToJson(deleteFileRequest);
      await _mobilePjRepository.deleteFile(deleteFileRequestJson);
      emit(StateMonitoringDetailsLoading(loading: false));
      emit(StateMonitoringDetailsReloadFiles(error: null, fileAdded: false));
    } on WebServiceCallException catch (e) {
      emit(StateMonitoringDetailsLoading(loading: false));
      emit(StateMonitoringDetailsReloadFiles(
          error: e.toString(), fileAdded: false));
    }
  }

  Future<dynamic> _pushSessionData(
      bool autoSave, bool terminerSeance, String? heureFin) async {
    try {
      PutSessionData putSessionData = PutSessionData(
          numpat: _numpat,
          suivi: suivi,
          numSeance: seance.numSeance,
          dateSeance: seance.dateSeance.toYYYYMMDD(),
          autoSave: autoSave,
          normalite: _normalite,
          commentaire: _commentController.text,
          listPutDataDescriptionUnique: _putDataDescriptionList,
          finSeance: terminerSeance,
          heureFin: heureFin,
          heureDeb: _heureDebutSeance == null
              ? ""
              : _heureDebutSeance!.toLanguageString(),
          heure: TimeOfDay.now().toLongLanguageString(),
          tableau: _sessionSamplingDataToSave != null
              ? _sessionSamplingDataToSave!.toJson()
              : {});
      String putSessionDataJson = putSessionDataToJson(putSessionData);
      return await _mobileSeanceRepository.pushSessionData(putSessionDataJson);
    } on WebServiceCallException catch (e) {
      return ErrorDetails(code: "", message: e.toString());
    }
  }

  void _addToPutDataDescriptionList(PutDataDescription putDataDescription) {
    _putDataDescriptionList.add(putDataDescription);
  }

  Future<dynamic> _saveFile(File file) async {
    String tableId = MonitoringUtils.getCorrespondingTable(suivi);

    // trouver lastPjNumber
    int lastPjNumber = 0;
    _fileNameDbList.forEach((name) {
      String str = name.split(".").first;
      String nStr = str.substring(str.length - 1);
      int n = int.parse(nStr);
      if (n > lastPjNumber) {
        lastPjNumber = n;
      }
    });

    // création du nom
    String fileName = "PJ_00" +
        (lastPjNumber + 1).toString() +
        "." +
        file.path.split(".").last;

    try {
      var newFileRequest = NewFileRequest(
          numpat: Config.basicUserInfos!.numpat,
          table: tableId,
          numSeanceMail:
              _isSession ? seance.numSeance : seance.numSeance.padLeft(12, "0"),
          pj: fileName);
      String newFileRequestJson = newFileRequestToJson(newFileRequest);
      return await _mobilePjRepository.pushFile(
          newFileRequestJson, file.readAsBytesSync());
    } on WebServiceCallException catch (e) {
      return ErrorDetails(code: "", message: e.toString());
    }
  }

  /// update des valeurs modifiées

  void _updateHeureDeb(TimeOfDay newTimeOfDay) {
    // met à jour l'heure de début de la séance
    _heureDebutSeance = newTimeOfDay;
  }

  void _updateSessionDetailsResponse(
      String idBD, String libelle, dynamic newValue) {
    // met à jour le champ modifié (checkbox, énumération ou textfield
    int index = _putDataDescriptionList.indexWhere((putDataDescription) =>
        ((libelle == putDataDescription.libelle) &&
            (idBD == putDataDescription.idBD)));
    //_putDataDescriptionList[index].value = newValue;
    // VDU : si le champ "idBD" n'existe pas dans _putDataDescriptionList, on crée l'index
    if (index >= 0)
      _putDataDescriptionList[index].value = newValue;
    else {
      _addToPutDataDescriptionList(new PutDataDescription(
          idBD: idBD, libelle: libelle, value: newValue));
    }
  }

  void _updateGetDrugsResponseConsumable(
      ConsumableDetails consumable, bool nonAdmin) {
    // met à jour le consommable

    if ((!nonAdmin) && (!consumable.administre)) {
      int index = _listConsumablesUpdated.indexWhere(
          (produit) => consumable.consumableID == produit.consumableID);
      if (index != -1) {
        _listConsumablesUpdated.removeAt(index);
      }
    } else {
      int index = _listConsumablesUpdated.indexWhere(
          (produit) => consumable.consumableID == produit.consumableID);
      if (index == -1) {
        _listConsumablesUpdated.add(consumable);
      } else {
        _listConsumablesUpdated[index] = consumable;
      }
    }
  }

  void _updateGetDrugsResponseDrug(DrugDetails drug, bool nonAdmin) {
    // met à jour le traitement

    if ((!nonAdmin) && (!drug.administre)) {
      int index = _listTraitementUpdated
          .indexWhere((traitement) => drug.drugID == traitement.drugID);
      if (index != -1) {
        _listTraitementUpdated.removeAt(index);
      }
    } else {
      int index = _listTraitementUpdated
          .indexWhere((traitement) => drug.drugID == traitement.drugID);
      if (index == -1) {
        _listTraitementUpdated.add(drug);
      } else {
        _listTraitementUpdated[index] = drug;
      }
    }
  }

  void _updateDbFilesList(List<String> newDbFilesList) {
    _fileNameDbList = newDbFilesList;
  }

  void _clearBlocData(bool onLoad) {
    // supprimer le contenu des variables du bloc
    // onLoad == true -> chargement de la séance
    // onLoad == false -> après une sauvegarde

    if (onLoad) {
      _sessionSamplingStructureBox = null;
      _sessionSamplingStructureTab = null;
      _listTraitementUpdated.clear();
      _listConsumablesUpdated.clear();
      _putDataDescriptionList.clear();
      _heureDebutSeance = null;
      _dateDebutSeance = null;
    }
    _fileNameDbList.clear();
    _listPickedFiles.clear();
  }

  bool _checkSessionDates(TimeOfDay? heureFinSeance, DateTime? dateFinSeance) {
    // check les dates et heures de début et fin de séance
    bool res = true;

    DateTime dateTimeDebutSeance = DateTime(
        _dateDebutSeance!.year,
        _dateDebutSeance!.month,
        _dateDebutSeance!.day,
        _heureDebutSeance!.hour,
        _heureDebutSeance!.minute);
    DateTime? dateTimeFinSeance = dateFinSeance != null
        ? DateTime(dateFinSeance.year, dateFinSeance.month, dateFinSeance.day,
            heureFinSeance!.hour, heureFinSeance.minute)
        : null;

    if (dateTimeFinSeance != null) {
      if (dateTimeFinSeance.isBefore(dateTimeDebutSeance)) {
        res = false;
      }
    }

    return res;
  }

  bool _checkSessionTimes(TimeOfDay? heureFinSeance, DateTime? dateFinSeance) {
    // check les heure de début et de fin de séance pour bloquer la validation
    if (dateFinSeance != null) {
      return !(heureFinSeance!.isMidnight() || _heureDebutSeance!.isMidnight());
    } else {
      return !_heureDebutSeance!.isMidnight();
    }
  }

  bool _checkSamplingDates(TimeOfDay? heureFinSeance, DateTime? dateFinSeance,
      SessionSamplingDataToSave? sessionSamplingDataToSave) {
    // check la cohérence de l'ensemble des dates et heures saisies

    bool res = true;

    if (_sessionSamplingDataToSave != null) {
      DateTime dateTimeDebutSeance = DateTime(
          _dateDebutSeance!.year,
          _dateDebutSeance!.month,
          _dateDebutSeance!.day,
          _heureDebutSeance!.hour,
          _heureDebutSeance!.minute);
      DateTime? dateTimeFinSeance = dateFinSeance != null
          ? DateTime(dateFinSeance.year, dateFinSeance.month, dateFinSeance.day,
              heureFinSeance!.hour, heureFinSeance.minute)
          : null;

      if (dateTimeFinSeance == null) {
        // séance pas terminée
        sessionSamplingDataToSave!.sessionSamplingDataPageList
            .forEach((pageSamplingData) {
          DateTime samplingDateTime = DateTime(
              pageSamplingData.date!.year,
              pageSamplingData.date!.month,
              pageSamplingData.date!.day,
              pageSamplingData.time!.hour,
              pageSamplingData.time!.minute);
          if (samplingDateTime.isBefore(dateTimeDebutSeance)) {
            res = false;
          }
        });
      } else {
        // séance terminée
        sessionSamplingDataToSave!.sessionSamplingDataPageList
            .forEach((pageSamplingData) {
          DateTime samplingDateTime = DateTime(
              pageSamplingData.date!.year,
              pageSamplingData.date!.month,
              pageSamplingData.date!.day,
              pageSamplingData.time!.hour,
              pageSamplingData.time!.minute);
          bool takenBefore = samplingDateTime.isBefore(dateTimeDebutSeance);
          bool takenAfter = samplingDateTime.isAfter(dateTimeFinSeance);
          if (takenBefore || takenAfter) {
            res = false;
          }
        });
      }
    }

    return res;
  }

  List _checkTreatmentsDates(
      TimeOfDay? heureFinSeance, DateTime? dateFinSeance) {
    // check la cohérence des dates de prise des traitements et produits en séance

    DateTime dateTimeDebutSeance = DateTime(
        _dateDebutSeance!.year,
        _dateDebutSeance!.month,
        _dateDebutSeance!.day,
        _heureDebutSeance!.hour,
        _heureDebutSeance!.minute);
    DateTime? dateTimeFinSeance =
        (heureFinSeance != null && dateFinSeance != null)
            ? DateTime(dateFinSeance.year, dateFinSeance.month,
                dateFinSeance.day, heureFinSeance.hour, heureFinSeance.minute)
            : null;

    bool res = true;
    List<String> wrongDrugList = [];

    if (dateTimeFinSeance == null) {
      // séance pas terminée, vérifier que la date et l'heure du traitement est après la date de la séance

      _listConsumablesUpdated.forEach((consumable) {
        // pour les produits
        DateTime dateTimeTaken = DateTime(
            consumable.date!.year,
            consumable.date!.month,
            consumable.date!.day,
            consumable.heure!.hour,
            consumable.heure!.minute);
        if ((dateTimeTaken.isBefore(dateTimeDebutSeance)) &&
            (consumable.administre)) {
          wrongDrugList.add(consumable.nom!);
          res = false;
        }
      });

      _listTraitementUpdated.forEach((traitement) {
        // pour les traitements

        DateTime dateTimeTaken = DateTime(
            traitement.date!.year,
            traitement.date!.month,
            traitement.date!.day,
            traitement.heure!.hour,
            traitement.heure!.minute);

        int heureArretInMinutes =
            traitement.heureArret!.hour * 60 + traitement.heureArret!.minute;
        int heureDebutSeanceInMinutes =
            _heureDebutSeance!.hour * 60 + _heureDebutSeance!.minute;
        if (((dateTimeTaken.isBefore(dateTimeDebutSeance)) &&
                (traitement.administre)) ||
            (heureArretInMinutes <= heureDebutSeanceInMinutes) &&
                !traitement.heureArret!.isMidnight()) {
          wrongDrugList.add(traitement.nom!);
          res = false;
        }
      });
    } else {
      // séance terminée

      _listConsumablesUpdated.forEach((consumable) {
        // pour les produits
        DateTime dateTimeTaken = DateTime(
            consumable.date!.year,
            consumable.date!.month,
            consumable.date!.day,
            consumable.heure!.hour,
            consumable.heure!.minute);
        bool takenBefore = dateTimeTaken.isBefore(dateTimeDebutSeance);
        bool takenAfter = dateTimeTaken.isAfter(dateTimeFinSeance);
        if (takenBefore || takenAfter) {
          if (consumable.administre) {
            wrongDrugList.add(consumable.nom!);
            res = false;
          }
        }
      });

      _listTraitementUpdated.forEach((traitement) {
        // pour les produits
        DateTime dateTimeTaken = DateTime(
            traitement.date!.year,
            traitement.date!.month,
            traitement.date!.day,
            traitement.heure!.hour,
            traitement.heure!.minute);

        bool takenBefore = dateTimeTaken.isBefore(dateTimeDebutSeance);
        bool takenAfter = dateTimeTaken.isAfter(dateTimeFinSeance);

        int heureArretInMinutes =
            traitement.heureArret!.hour * 60 + traitement.heureArret!.minute;
        int heureDebutSeanceInMinutes =
            _heureDebutSeance!.hour * 60 + _heureDebutSeance!.minute;
        int heureFinSeanceInMinutes =
            heureFinSeance!.hour * 60 + heureFinSeance.minute;

        bool stoppedBefore = heureArretInMinutes <= heureDebutSeanceInMinutes;
        bool stoppedAfter = heureArretInMinutes >= heureFinSeanceInMinutes;

        if (takenBefore || takenAfter || stoppedBefore || stoppedAfter) {
          if (traitement.administre) {
            wrongDrugList.add(traitement.nom!);
            res = false;
          }
        }
      });
    }

    return [res, wrongDrugList];
  }
}
