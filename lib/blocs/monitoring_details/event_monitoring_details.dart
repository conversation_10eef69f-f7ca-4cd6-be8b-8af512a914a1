import 'dart:io';
import 'package:flutter/material.dart';

abstract class EventMonitoringDetails {
  List<Object> get props => [];
}

// clic sur une séance précédente
class EventMonitoringDetailsPreviousSession extends EventMonitoringDetails {
  final bool estTerminee;
  final bool tabReload;

  EventMonitoringDetailsPreviousSession(
      {required this.estTerminee, required this.tabReload});

  @override
  get props => [estTerminee, tabReload];
}

// clic sur le bouton "valider" du formulaire de saisie
class EventMonitoringDetailsValidateSession extends EventMonitoringDetails {
  final bool autoSave;
  final bool finSeance;
  final TimeOfDay? heureFinSeance;
  final DateTime? dateFinSeance;

  EventMonitoringDetailsValidateSession({
    required this.autoSave,
    required this.finSeance,
    required this.heureFinSeance,
    required this.dateFinSeance,
  });

  @override
  get props => [
        autoSave,
        finSeance,
        heureFinSeance!,
        dateFinSeance!,
      ];
}

// event pour sauver la séance après avoir modifié l'échantillonage manuel
class EventMonitoringDetailsSaveSessionAfterSamplingEditing
    extends EventMonitoringDetails {}

class EventMonitoringDetailsSaveFile extends EventMonitoringDetails {
  final File file;

  EventMonitoringDetailsSaveFile({required this.file});

  @override
  get props => [file];
}

class EventMonitoringDetailsDeleteCommentFile extends EventMonitoringDetails {
  final File file;

  EventMonitoringDetailsDeleteCommentFile({
    required this.file,
  });

  @override
  get props => [file];
}
