import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/models/biologic_data_content.dart';

abstract class EventBilansBio {
  List<Object> get props => [];
}

// event lors d'un clic sur le bouton de recherche des bilans
class EventBilansBioSearch extends EventBilansBio {
  final DateTime startDate;
  final DateTime endDate;

  EventBilansBioSearch({required this.startDate, required this.endDate});

  List<Object> get props => [startDate, endDate];
}

// event lors d'un clic sur le bouton d'ajout d'un bilan
class EventBilansBioAddBilan extends EventBilansBio {
  String path;
  DateTime date;

  EventBilansBioAddBilan({required this.path, required this.date});

  List<Object> get props => [path, date];
}

// event pour choisir comment on visualise les données
class EventBilansBioChooseDataType extends EventBilansBio {
  final int dataType;

  EventBilansBioChooseDataType({required this.dataType});

  List<Object> get props => [dataType];
}

class EventBilanBioUninitialised extends EventBilansBio {}

class EventBilanBioGetBilan extends EventBilansBio {
  final DateTime bilanDate;
  final String numEnreg;

  EventBilanBioGetBilan({required this.bilanDate, required this.numEnreg});

  List<Object> get props => [bilanDate, numEnreg];
}

class EventBilanBioOpenCharts extends EventBilansBio {
  final BiologicDataContent biologicDataContent;

  EventBilanBioOpenCharts({
    required this.biologicDataContent,
  });

  List<Object> get props => [biologicDataContent];
}

class EventBilansBioCreateAnalysis {
  final String path;
  final String date;
  final String num;

  EventBilansBioCreateAnalysis(
      {required this.path, required this.date, required this.num});

  List<Object> get props => [path, date, num];
}

class EventBilansBioDeleteBilan extends EventBilansBio {
  final BiologicAnalysisData biologicAnalysisData;

  EventBilansBioDeleteBilan({
    required this.biologicAnalysisData,
  });

  List<Object> get props => [biologicAnalysisData];
}
