import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/bilans_bio/bloc_bilans_bio.dart';
import 'package:theradom/blocs/bilans_bio/event_bilans_bio.dart';

class BilansBioBlocProvider extends StatelessWidget {
  final Widget child;

  BilansBioBlocProvider({required this.child});

  @override
  Widget build(BuildContext context) {
    DateTime now = DateTime.now();
    return BlocProvider<BlocBilanBio>(
      create: (context) {
        return BlocBilanBio()
          ..add(EventBilansBioSearch(
              startDate: DateTime(now.year, now.month - 2, now.day),
              endDate: now));
      },
      child: child,
    );
  }
}
