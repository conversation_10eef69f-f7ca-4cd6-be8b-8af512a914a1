import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/models/biologic_data_content.dart';
import 'package:fl_chart/fl_chart.dart';

abstract class StateBilansBio {
  List<Object> get props => [];
}

// state pendant un loading
class StateBilansBioUninitialised extends StateBilansBio {}

// state du dataType selectionné
class StateBilansBioDataTypePicked extends StateBilansBio {
  final List<BiologicDataContent> biologicDataContentList;
  final List<BiologicAnalysisData> biologicAnalysisDataList;
  final String? error;

  StateBilansBioDataTypePicked(
      {required this.biologicDataContentList,
      required this.biologicAnalysisDataList,
      required this.error});

  List<Object> get props =>
      [biologicDataContentList, biologicAnalysisDataList, error!];
}

class StateBilansBioLoading extends StateBilansBio {
  final bool loading;

  StateBilansBioLoading({required this.loading});

  List<Object> get props => [loading];
}

class StateBilansBioShowCharts extends StateBilansBio {
  final BiologicDataContent biologicDataContent;
  final List<dynamic> iLineDataSetList;
  final List<dynamic> limitLineList;

  StateBilansBioShowCharts(
      {required this.biologicDataContent,
      required this.iLineDataSetList,
      required this.limitLineList});

  List<Object> get props =>
      [biologicDataContent, iLineDataSetList, limitLineList];
}

class StateBilansBioDeleted extends StateBilansBio {
  final bool success;
  final String message;
  final BiologicAnalysisData biologicAnalysisData;

  StateBilansBioDeleted(
      {required this.success,
      required this.message,
      required this.biologicAnalysisData});

  List<Object> get props => [success, message, biologicAnalysisData];
}

class StateBilansBioNotif extends StateBilansBio {
  final bool success;
  final String message;

  StateBilansBioNotif({
    required this.success,
    required this.message,
  });

  List<Object> get props => [success, message];
}
