import 'dart:io';
import 'dart:typed_data';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:theradom/blocs/bilans_bio/event_bilans_bio.dart';

import 'package:theradom/models/BiologicRequest.dart';
import 'package:theradom/models/CreateAnalysisRequest.dart';
import 'package:theradom/models/DeleteFileRequest.dart';
import 'package:theradom/models/GetFilesRequest.dart';
import 'package:theradom/models/NewFileRequest.dart';
import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/models/biologic_data_content.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_document_repository.dart';
import 'package:theradom/services/repositories/mobile_pj_repository.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/Helper.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:theradom/blocs/bilans_bio/state_bilans_bio.dart';
import 'package:theradom/models/BiologicResponse.dart';
import 'package:theradom/models/CreateAnalysisResponse.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/models/GetFilesResponse.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:fl_chart/fl_chart.dart';

class BlocBilanBio extends Bloc<EventBilansBio, StateBilansBio> {
  BiologicResponse _biologicResponse = BiologicResponse(bio: {}, valide: "OK");
  late final MobileDocumentsRepository _mobileDocumentsRepository;
  late final MobilePjRepository _mobilePjRepository;

  BlocBilanBio({
    MobileDocumentsRepository? mobileDocumentsRepository,
    MobilePjRepository? mobilePjRepository,
  }) : super(StateBilansBioUninitialised()) {
    _mobileDocumentsRepository =
        mobileDocumentsRepository ?? MobileDocumentsRepository();
    _mobilePjRepository = mobilePjRepository ?? MobilePjRepository();
    on<EventBilansBioSearch>(_search);
    on<EventBilanBioGetBilan>(_openPdfFile);
    on<EventBilanBioOpenCharts>(_loadCharts);
    on<EventBilansBioAddBilan>(_pushFile);
    on<EventBilansBioDeleteBilan>(_deleteFile);
  }

  void _search(EventBilansBioSearch event, Emitter<StateBilansBio> emit) async {
    // recherche des bilans bio diponibles
    emit(StateBilansBioUninitialised());

    final String startDate = event.startDate.toYYYYMMDD();
    final String endDate = event.endDate.toYYYYMMDD();
    try {
      BiologicRequest biologicRequest = BiologicRequest(
          numpat: Config.basicUserInfos!.numpat, from: startDate, to: endDate);
      String biologicRequestJson = biologicRequestToJson(biologicRequest);
      _biologicResponse = await _mobileDocumentsRepository
          .fetchBiologicAnalysis(biologicRequestJson);
      List<BiologicDataContent> biologicDataContentList = [];
      List<BiologicAnalysisData> biologicAnalysisDataList = [];

      // ajout des objets dans les tableaux correspondants
      _biologicResponse.bio.forEach((key, biologicAnalysisData) {
        biologicAnalysisDataList.add(biologicAnalysisData);
        biologicAnalysisData.donnees?.forEach((nomDonnee, biologicDataContent) {
          bool add = true;
          biologicDataContentList.forEach((biologicDataContentElemInList) {
            if ((biologicDataContentElemInList.typeBilan ==
                    biologicDataContent.typeBilan) &&
                (biologicDataContent.valeurAp != "") &&
                (biologicDataContent.valeurAv != "")) {
              add = false;
            }
          });
          if ((add) && (biologicDataContent.type == "1")) {
            biologicDataContentList.add(biologicDataContent);
          }
        });
      });

      //tri alphabétique sur les tableaux
      biologicDataContentList.sort((bioDataContent1, bioDataContent2) {
        return bioDataContent1.typeBilan
            .toLowerCase()
            .compareTo(bioDataContent2.typeBilan.toLowerCase());
      });
      biologicAnalysisDataList.sort((bioAnalysisData1, bioAnalysisData2) {
        String bioAnalysisDataDateStr1 = bioAnalysisData1.cDate.toYYYYMMDD();
        String bioAnalysisDataDateStr2 = bioAnalysisData2.cDate.toYYYYMMDD();
        return int.parse(bioAnalysisDataDateStr1)
            .compareTo(int.parse(bioAnalysisDataDateStr2));
      });

      emit(StateBilansBioDataTypePicked(
        error: null,
        biologicDataContentList: biologicDataContentList,
        biologicAnalysisDataList: biologicAnalysisDataList,
      ));
    } on WebServiceCallException catch (e) {
      emit(StateBilansBioDataTypePicked(
          biologicAnalysisDataList: [],
          biologicDataContentList: [],
          error: e.toString()));
    }
  }

  void _openPdfFile(
      EventBilanBioGetBilan event, Emitter<StateBilansBio> emit) async {
    // download le pdf sélectionné
    emit(StateBilansBioLoading(loading: true));
    String dateBilanStr = event.bilanDate.toYYYYMMDD();

    try {
      GetFilesRequest getFilesRequest = GetFilesRequest(
          numpat: Config.basicUserInfos!.numpat,
          table: Config.tableBiologicAnalysis,
          numSeanceMail: event.numEnreg,
          liste: "details",
          pjName: dateBilanStr);
      String getFilesRequestJson = getFilesRequestToJson(getFilesRequest);
      GetFilesResponse getFilesResponse =
          await _mobilePjRepository.fetchFileContent(getFilesRequestJson);
      emit(StateBilansBioLoading(loading: false));
      await HelperPj.openBase64File(
          getFilesResponse.content!, dateBilanStr + ".pdf");
    } on WebServiceCallException catch (_) {
      emit(StateBilansBioLoading(loading: false));
      emit(StateBilansBioNotif(
          success: false, message: allTranslations.text("cant_charge_file")));
    }
  }

  void _loadCharts(
      EventBilanBioOpenCharts event, Emitter<StateBilansBio> emit) async {
    final String dataName = event.biologicDataContent.typeBilan;
    List repList = getBiologicDataContentToSeriesDoubleDouble(dataName, true);
    emit(StateBilansBioShowCharts(
        iLineDataSetList: repList[0],
        biologicDataContent: event.biologicDataContent,
        limitLineList: repList[1]));
  }

  void _pushFile(
      EventBilansBioAddBilan event, Emitter<StateBilansBio> emit) async {
    // ajout d'un bilan bio en base
    emit(StateBilansBioLoading(loading: true));
    final String path = event.path;
    final String date = event.date.toYYYYMMDD();

    try {
      CreateAnalysisRequest createAnalysisRequest = CreateAnalysisRequest(
          numpat: Config.basicUserInfos!.numpat,
          table: Config.tableBiologicAnalysis,
          numSeanceMail: date);
      String createAnalysisRequestJson =
          createAnalysisRequestToJson(createAnalysisRequest);
      CreateAnalysisResponse createAnalysisResponse = await _mobilePjRepository
          .pushBiologicAnalysisFile(createAnalysisRequestJson);

      File file = File(path);
      Uint8List fichierBrut = file.readAsBytesSync();
      final String chemin = createAnalysisResponse.chemin;
      final String start = 'BILAN_';
      final String end = "_";
      final int startIndex = chemin.indexOf(start);
      final int endIndex = chemin.indexOf(end, startIndex + start.length);
      final String recordID =
          chemin.substring(startIndex + start.length, endIndex);

      try {
        NewFileRequest newFileRequest = NewFileRequest(
            numpat: Config.basicUserInfos!.numpat,
            table: Config.tableBiologicAnalysis,
            numSeanceMail: recordID,
            pj: date + ".pdf");
        var newFileRequestJson = newFileRequestToJson(newFileRequest);
        await _mobilePjRepository.pushFile(newFileRequestJson, fichierBrut);
        emit(StateBilansBioLoading(loading: false));
        emit(StateBilansBioNotif(
            success: true,
            message: allTranslations.text("add_bio_success_message",
                {"date": event.date.toLanguageString()})));
      } on WebServiceCallException catch (e) {
        emit(StateBilansBioLoading(loading: false));
        emit(StateBilansBioNotif(success: false, message: e.toString()));
      }
    } on WebServiceCallException catch (e) {
      emit(StateBilansBioLoading(loading: false));
      emit(StateBilansBioNotif(success: false, message: e.toString()));
    }
  }

  void _deleteFile(
      EventBilansBioDeleteBilan event, Emitter<StateBilansBio> emit) async {
    try {
      DeleteFileRequest deleteFileRequest = DeleteFileRequest(
          numpat: Config.basicUserInfos!.numpat,
          table: Config.tableBiologicAnalysis,
          numSeanceMail: event.biologicAnalysisData.idPdf,
          numPj: "${event.biologicAnalysisData.cDate}.pdf");
      String deleteFileRequestJson = deleteFileRequestToJson(deleteFileRequest);
      await _mobilePjRepository.deleteFile(deleteFileRequestJson);
      emit(StateBilansBioDeleted(
          success: true,
          message: allTranslations.text("bilan_bio_deleted"),
          biologicAnalysisData: event.biologicAnalysisData));
    } on WebServiceCallException catch (e) {
      emit(StateBilansBioDeleted(
          success: false,
          message: e.toString(),
          biologicAnalysisData: event.biologicAnalysisData));
    }
  }

  List getBiologicDataContentToSeriesDoubleDouble(
      String dataName, bool isDetailed) {
    // créé une liste de séries pour la librairie

    // recherche des éléements pour tracer la courbe
    List infoList = _getInfos(dataName);
    List<BiologicDataContent> biologicDataContentList = infoList[0];
    List<DateTime> dateTimeList = infoList[1];
    dynamic normale = infoList[4];
    double? limiteInfDouble = infoList[2];
    double? limiteSupDouble = infoList[3];

    List<FlSpot> spotListAv = [];
    List<FlSpot> spotListAp = [];

    HorizontalLine? normalLimitLine;

    // valeurs limites (limite sup et limite inf)
    HorizontalLine? limitLineInf;
    HorizontalLine? limitLineSup;
    BuildContext context = Config.appKey.currentContext!;
    if (limiteInfDouble != limiteSupDouble) {
      // pour éviter d'afficher deux limites superposées
      if (limiteInfDouble != null) {
        limitLineInf = HorizontalLine(
          y: limiteInfDouble,
          color: AppTheme.chartLimitColor,
          strokeWidth: 1,
          dashArray: [5, 5],
          label: HorizontalLineLabel(
            show: true,
            labelResolver: (line) =>
                allTranslations.text("lower_limit") + " ($limiteInfDouble)",
            style: TextStyle(
              color: AppTheme.chartLimitColor,
              fontWeight: FontWeight.bold,
              fontSize:
                  isDetailed ? FontSizeController.of(context).fontSize : 14,
            ),
            alignment: Alignment.topRight,
          ),
        );
      }
      if (limiteSupDouble != null) {
        limitLineSup = HorizontalLine(
          y: limiteSupDouble,
          color: AppTheme.chartLimitColor,
          strokeWidth: 1,
          dashArray: [5, 5],
          label: HorizontalLineLabel(
            show: true,
            labelResolver: (line) =>
                allTranslations.text("upper_limit") + " ($limiteSupDouble)",
            style: TextStyle(
              color: AppTheme.chartLimitColor,
              fontWeight: FontWeight.bold,
              fontSize:
                  isDetailed ? FontSizeController.of(context).fontSize : 14,
            ),
            alignment: Alignment.topRight,
          ),
        );
      }
    }

    late double ts;
    late int i;

    // itérations pour récupérer les valeurs
    biologicDataContentList.asMap().forEach((index, biologicDataContent) {
      try {
        if ((biologicDataContent.valeurAp != "") &&
            (biologicDataContent.valeurAv != "")) {
          // ajout au courbes avant et après
          // timestamp
          String tsStr = dateTimeList[index]
              .millisecondsSinceEpoch
              .toString()
              .replaceAll(",", ".");
          ts = double.parse(tsStr) * 1000;
          String yAvStr = biologicDataContent.valeurAv.replaceAll(",", ".");
          double yAv = double.parse(yAvStr);
          String yApStr = biologicDataContent.valeurAp.replaceAll(",", ".");
          double yAp = double.parse(yApStr);
          spotListAv.add(FlSpot(ts, yAv));
          spotListAp.add(FlSpot(ts, yAp));
        }
      } catch (e) {
        //print("ERR = $e");
      }
      i = index;
    });

    List<LineChartBarData> dataSets = [];
    Color highlightColor = AppTheme.primaryColorDarker;

    // courbe valeurs avant
    LineChartBarData lineDataSetAvant = LineChartBarData(
      spots: spotListAv,
      isCurved: false,
      color: AppTheme.chartColorList[0],
      barWidth: 2,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          return FlDotCirclePainter(
            radius: 3,
            color: AppTheme.chartColorList[0],
            strokeWidth: 1,
            strokeColor: Colors.white,
          );
        },
      ),
      belowBarData: BarAreaData(show: false),
    );
    dataSets.add(lineDataSetAvant);

    // courbe valeurs après
    LineChartBarData lineDataSetApres = LineChartBarData(
      spots: spotListAp,
      isCurved: false,
      color: AppTheme.chartColorList[1],
      barWidth: 2,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          return FlDotCirclePainter(
            radius: 3,
            color: AppTheme.chartColorList[1],
            strokeWidth: 1,
            strokeColor: Colors.white,
          );
        },
      ),
      belowBarData: BarAreaData(show: false),
    );
    dataSets.add(lineDataSetApres);

    // Création des courbes de valeurs normales *inf* et *sup*
    if (normale != null) {
      // une NORMALE est définie
      if (normale.runtimeType == double) {
        // NORMALE est une ligne
        normalLimitLine = HorizontalLine(
          y: normale,
          color: AppTheme.chartNormalColor,
          strokeWidth: 1,
          dashArray: [5, 5],
          label: HorizontalLineLabel(
            show: true,
            labelResolver: (line) =>
                "${allTranslations.text('normal')}: $normale",
            style: TextStyle(
              color: AppTheme.chartNormalColor,
              fontWeight: FontWeight.bold,
              fontSize:
                  isDetailed ? FontSizeController.of(context).fontSize : 14,
            ),
            alignment: Alignment.topRight,
          ),
        );
      } else if ((normale[1] - normale[0]) > 0) {
        // NORMALE est un intervalle
        List<FlSpot> spotListNormaleInf = [];
        List<FlSpot> spotListNormaleSup = [];
        // première itération de la boucle pour la première date
        spotListNormaleInf.add(FlSpot(
            dateTimeList[0].millisecondsSinceEpoch.toDouble() * 1000,
            normale[0]));
        spotListNormaleSup.add(FlSpot(
            dateTimeList[0].millisecondsSinceEpoch.toDouble() * 1000,
            normale[1]));
        // derniere itération i de la boucle pour la dernière date
        spotListNormaleInf.add(FlSpot(
            dateTimeList[i].millisecondsSinceEpoch.toDouble() * 1000,
            normale[0]));
        spotListNormaleSup.add(FlSpot(
            dateTimeList[i].millisecondsSinceEpoch.toDouble() * 1000,
            normale[1]));

        // Ligne inférieure de la normale
        LineChartBarData lineDataSetNormaleInf = LineChartBarData(
          spots: spotListNormaleInf,
          isCurved: false,
          color: AppTheme.chartNormalColor,
          barWidth: 1,
          isStrokeCapRound: true,
          dotData: FlDotData(show: false),
          belowBarData: BarAreaData(show: false),
        );
        dataSets.add(lineDataSetNormaleInf);

        // Ligne supérieure de la normale avec remplissage
        LineChartBarData lineDataSetNormaleSup = LineChartBarData(
          spots: spotListNormaleSup,
          isCurved: false,
          color: AppTheme.chartNormalColor,
          barWidth: 1,
          isStrokeCapRound: true,
          dotData: FlDotData(show: false),
          belowBarData: BarAreaData(
            show: true,
            color: AppTheme.chartNormalColor.withOpacity(0.2),
          ),
        );
        dataSets.add(lineDataSetNormaleSup);
      }
    }

    return [
      dataSets,
      [limitLineInf, limitLineSup, normalLimitLine]
    ];
  }

  List _getInfos(String dataName) {
    // recupère les valeurs nécessaires pour une donnée

    List<BiologicDataContent> biologicalDataContentListForCharts = [];
    List<DateTime> dateTimeList = [];
    String? normaleStr;
    String? limiteInfString;
    String? limiteSupString;

    _biologicResponse.bio.forEach((key, biologicAnalysisData) {
      if (biologicAnalysisData.donnees != null) {
        if (biologicAnalysisData.donnees![dataName] != null) {
          normaleStr = biologicAnalysisData.donnees![dataName]!.normale;
          limiteInfString = biologicAnalysisData.donnees![dataName]!.limiteInf;
          limiteSupString = biologicAnalysisData.donnees![dataName]!.limiteSup;
          dateTimeList.add(biologicAnalysisData.cDate);
          biologicalDataContentListForCharts
              .add(biologicAnalysisData.donnees![dataName]!);
        }
      }
    });

    double? limiteInfDouble = limiteInfString != null
        ? Helper.replaceComaWithDot(limiteInfString!)
        : null;
    double? limiteSupDouble = limiteSupString != null
        ? Helper.replaceComaWithDot(limiteSupString!)
        : null;

    var normale; // double ou List<double>

    if ((normaleStr ?? "").contains("-")) {
      List<String> tab = normaleStr?.split("-") ?? [];
      if (tab[0] == "") {
        // NORMALE est un nombre négatif contenu dans tab[1]
        normale = Helper.replaceComaWithDot(tab[1]);
      } else {
        if (tab[1] != "") {
          // NORMALE est un intervalle de valeurs
          normale = [
            Helper.replaceComaWithDot(tab[0]),
            Helper.replaceComaWithDot(tab[1])
          ];
        }
      }
    } else if (normaleStr != "") {
      // NORMALE est un nombre positif avec virgule
      try {
        normale = Helper.replaceComaWithDot(normaleStr!);
      } catch (e) {}
    }

    return [
      biologicalDataContentListForCharts,
      dateTimeList,
      limiteInfDouble,
      limiteSupDouble,
      normale
    ];
  }
}
