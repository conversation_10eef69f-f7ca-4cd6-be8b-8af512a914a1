abstract class EventDocuments {
  List<Object> get props => [];
}

// event pour obtenir la liste des documents
class EventDocumentsInitialDocumentList extends EventDocuments {
  EventDocumentsInitialDocumentList();

  @override
  get props => [];
}

class EventDocumentsDocumentList extends EventDocuments {}

// event pour obtenir un document en particulier
class EventDocumentsGetDocument extends EventDocuments {
  final String name;
  final String table;

  EventDocumentsGetDocument({required this.name, required this.table});

  @override
  get props => [name, table];
}
