import 'dart:convert';
import 'dart:typed_data';
import 'package:bloc/bloc.dart';
import 'package:theradom/config.dart';

import 'package:theradom/models/GetFilesRequest.dart';
import 'package:theradom/models/UserID.dart';
import 'package:theradom/models/doc.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_document_repository.dart';
import 'package:theradom/services/repositories/mobile_pj_repository.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:theradom/blocs/documents/EventDocuments.dart';
import 'package:theradom/blocs/documents/StateDocuments.dart';
import 'package:theradom/models/DocumentsResponse.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/GetFilesResponse.dart';
import 'package:theradom/models/ReportsResponse.dart';
import 'package:open_filex/open_filex.dart';

class BlocDocuments extends Bloc<EventDocuments, StateDocuments> {
  final String suivi;
  late final MobilePjRepository _mobilePjRepository;
  late final MobileDocumentsRepository _mobileDocumentsRepository;

  List<Doc> _reportList = [];
  List<Doc> _pdfList = [];

  BlocDocuments(
      {required this.suivi,
      MobilePjRepository? mobilePjRepository,
      MobileDocumentsRepository? mobileDocumentsRepository})
      : super(StateDocumentsUninitialized()) {
    _mobilePjRepository = mobilePjRepository ?? MobilePjRepository();
    _mobileDocumentsRepository =
        mobileDocumentsRepository ?? MobileDocumentsRepository();
    on<EventDocumentsInitialDocumentList>(_getInitialDocuments);
    on<EventDocumentsDocumentList>(_getDocuments);
    on<EventDocumentsGetDocument>(_getDocument);
  }

  void _getInitialDocuments(EventDocumentsInitialDocumentList event,
      Emitter<StateDocuments> emit) async {
    // requête la liste des documents disponibles

    ErrorDetails? reportsErrorDetails;
    ErrorDetails? pdfErrorDetails;

    UserID userID = UserID(numpat: Config.basicUserInfos!.numpat);
    String userIdJson = userIdToJson(userID);

    try {
      ReportsResponse reportsResponse =
          await _mobileDocumentsRepository.fetchReports(userIdJson, docType: 3);
      if (reportsResponse.fichiers[suivi] != null) {
        reportsResponse.fichiers[suivi]?.hebdo.forEach((k, doc) {
          _reportList.add(doc);
        });
      }
    } on WebServiceCallException catch (e) {
      reportsErrorDetails = ErrorDetails(code: "500", message: e.toString());
    }

    try {
      DocumentsResponse documentsResponse =
          await _mobileDocumentsRepository.fetchReports(userIdJson, docType: 2);
      if (documentsResponse.fichiers[suivi] != null) {
        documentsResponse.fichiers[suivi]?.forEach((k, pdf) {
          _pdfList.add(pdf);
        });
      }
    } on WebServiceCallException catch (e) {
      pdfErrorDetails = ErrorDetails(code: "500", message: e.toString());
    }

    emit(StateDocumentsList(
        reportList: _reportList,
        pdfList: _pdfList,
        pdfErrorDetails: pdfErrorDetails,
        reportErrorDetails: reportsErrorDetails));
  }

  void _getDocuments(
      EventDocumentsDocumentList event, Emitter<StateDocuments> emit) async {
    emit(StateDocumentsList(
        reportList: _reportList,
        pdfList: _pdfList,
        pdfErrorDetails: null,
        reportErrorDetails: null));
  }

  void _getDocument(
      EventDocumentsGetDocument event, Emitter<StateDocuments> emit) async {
    // requête un document en particulier

    emit(StateDocumentsLoading(loading: true, fail: false, message: null));
    try {
      final getFilesRequest = GetFilesRequest(
          numpat: Config.basicUserInfos!.numpat,
          table: event.table,
          numSeanceMail: suivi,
          liste: "details",
          pjName: event.name);
      final getFilesRequestJSON = getFilesRequestToJson(getFilesRequest);
      GetFilesResponse getFilesResponse =
          await _mobilePjRepository.fetchFileContent(getFilesRequestJSON);
      emit(StateDocumentsLoading(loading: false, fail: false, message: null));
      Uint8List pjBytes = base64.decode(getFilesResponse.content!);
      String path = await HelperPj.saveFileInTempDirectory(pjBytes, event.name);

      OpenFilex.open(path);
      /*
      bool isGranted = await Permission.manageExternalStorage.request().isGranted;
      if (isGranted) {
        await OpenFile.open(path);
      }
      else {
        emit(StateDocumentsLoading(
            loading: false,
            fail: true,
            message: Permission.manageExternalStorage.
        ));
      }

       */
    } on WebServiceCallException catch (e) {
      emit(StateDocumentsLoading(
          loading: false, fail: true, message: e.toString()));
    }
  }
}
