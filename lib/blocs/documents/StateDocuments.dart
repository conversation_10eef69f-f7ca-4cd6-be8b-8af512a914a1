import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/doc.dart';

abstract class StateDocuments {
  List<Object> get props => [];
}

// état non-init
class StateDocumentsUninitialized extends StateDocuments {}

// état chargement d'un document
class StateDocumentsLoading extends StateDocuments {
  final bool loading;
  final bool fail;
  final String? message;

  StateDocumentsLoading(
      {required this.loading, required this.fail, required this.message});

  @override
  List<Object> get props => [loading, fail, message!];
}

// état liste de documents
class StateDocumentsList extends StateDocuments {
  final List<Doc> reportList;
  final List<Doc> pdfList;
  final ErrorDetails? pdfErrorDetails;
  final ErrorDetails? reportErrorDetails;

  StateDocumentsList(
      {required this.reportList,
      required this.pdfList,
      required this.pdfErrorDetails,
      required this.reportErrorDetails});

  @override
  List<Object> get props =>
      [reportList, pdfList, pdfErrorDetails!, reportErrorDetails!];
}
