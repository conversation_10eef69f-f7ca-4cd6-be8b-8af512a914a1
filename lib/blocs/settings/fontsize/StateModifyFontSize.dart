abstract class StateModifyFontSize {
  List<Object> get props => [];
}

// non init
class StateModifyFontSizeUninitialised extends StateModifyFontSize {}

class StateFontSizeModified extends StateModifyFontSize {
  final bool loading;
  final bool? success;
  final String? message;

  StateFontSizeModified(
      {required this.loading, required this.success, required this.message});

  @override
  List<Object> get props => [loading, success!, message!];
}
