import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/settings/fontsize/EventModifyFontSize.dart';
import 'package:theradom/blocs/settings/fontsize/StateModifyFontSize.dart';
import 'package:theradom/models/NewFontSizeRequest.dart';
import 'package:theradom/config.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_parametres_repository.dart';
import 'package:theradom/utils/Translations.dart';

class BlocModifyFontSize
    extends Bloc<EventModifyFontSize, StateModifyFontSize> {
  late final MobileParametresRepository _mobileParametresRepository;

  BlocModifyFontSize({MobileParametresRepository? mobileParametresRepository})
      : super(StateModifyFontSizeUninitialised()) {
    _mobileParametresRepository =
        mobileParametresRepository ?? MobileParametresRepository();
    on<EventModifyFontSizeValidate>(_modifyFontSize);
  }

  void _modifyFontSize(EventModifyFontSizeValidate event,
      Emitter<StateModifyFontSize> emit) async {
    // modification de la taille de la police

    emit(StateFontSizeModified(loading: true, success: null, message: null));

    final int newFontSize = event.fontSize.round();

    try {
      NewFontSizeRequest newFontSizeRequest = NewFontSizeRequest(
          numpat: Config.basicUserInfos!.numpat, taillePolice: newFontSize);
      final String newFontSizeRequestJson =
          newFontSizeRequestToJson(newFontSizeRequest);
      await _mobileParametresRepository
          .changeUserFontSize(newFontSizeRequestJson);
      Config.basicUserInfos!.taillePolice =
          newFontSize.toDouble(); // màj de la variable globale
      emit(StateFontSizeModified(
          loading: false,
          success: true,
          message: allTranslations.text("font_size_modif_success")));
    } on WebServiceCallException catch (e) {
      emit(StateFontSizeModified(
          loading: false, success: false, message: e.toString()));
    }
  }
}
