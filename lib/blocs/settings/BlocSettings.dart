import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/settings/EventSettings.dart';
import 'package:theradom/blocs/settings/StateSettings.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/TOURequest.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_parametres_repository.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:open_filex/open_filex.dart';
import 'package:url_launcher/url_launcher.dart';

class BlocSettings extends Bloc<EventSettings, StateSettings> {
  late final MobileParametresRepository _mobileParametresRepository;
  BlocSettings({MobileParametresRepository? mobileParametresRepository})
      : super(StateSettingsMainScreen()) {
    _mobileParametresRepository =
        mobileParametresRepository ?? MobileParametresRepository();
    on<EventSettingsMainScreen>(_doNothing);
    on<EventSettingsModifyEmail>(_modifyEmail);
    on<EventSettingsLogout>(_doNothing);
    on<EventSettingsModifyFontSize>(_modifyFontSize);
    on<EventSettingsModifyTOU>(_modifyTermsOfUse);
    on<EventSettingsGetUserGuide>(_showUserGuide);
    on<EventSettingsGetTOU>(_showTOUfile);
    on<EventSettingsGetPrivacyPolicy>(_openNavigator);
  }

  void _doNothing(EventSettings event, Emitter<StateSettings> emit) async {}

  void _modifyEmail(
      EventSettingsModifyEmail event, Emitter<StateSettings> emit) async {
    emit(StateSettingsModifyEmail(
        success: event.success, message: event.message));
  }

  void _modifyFontSize(
      EventSettingsModifyFontSize event, Emitter<StateSettings> emit) async {
    emit(StateSettingsModifyFontSize(
        success: event.success,
        message: event.message,
        fontSize: event.fontSize));
  }

  void _modifyTermsOfUse(
      EventSettingsModifyTOU event, Emitter<StateSettings> emit) async {
    // modif des accords CGU

    try {
      TOURequest touRequest = TOURequest(
          numpat: Config.basicUserInfos!.numpat, accordCGU: event.accordCGU);
      String touRequestJson = touRequestToJson(touRequest);
      await _mobileParametresRepository
          .changeTermsOfUseAgreement(touRequestJson);
      emit(StateSettingsModifyTOU(
          accordCGU: event.accordCGU,
          success: true,
          message: allTranslations.text("tou_modif_success")));
    } on WebServiceCallException catch (e) {
      emit(StateSettingsModifyTOU(
          accordCGU: null, success: false, message: e.toString()));
    }
  }

  void _showUserGuide(
      EventSettingsGetUserGuide event, Emitter<StateSettings> emit) async {
    String path = await HelperPj.copyAssetToTempDirectory(
        "assets/pdf/manuel_theradom.pdf");
    await OpenFilex.open(path);
  }

  void _showTOUfile(
      EventSettingsGetTOU event, Emitter<StateSettings> emit) async {
    String path =
        await HelperPj.copyAssetToTempDirectory("assets/pdf/cgu_v3.pdf");
    await OpenFilex.open(path);
  }

  void _openNavigator(EventSettings event, Emitter<StateSettings> emit) async {
    if (!await launchUrl(Uri.parse(Config.privacyPolicyUrl))) {
      throw Exception('Could not launch ${Config.privacyPolicyUrl}');
    }
  }
}
