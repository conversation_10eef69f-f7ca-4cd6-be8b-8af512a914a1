abstract class EventSettings {
  List<Object> get props => [];
}

// event écran principal
class EventSettingsMainScreen extends EventSettings {}

// en cas de modification de l'email
class EventSettingsModifyEmail extends EventSettings {
  final bool success;
  final String message;

  EventSettingsModifyEmail({required this.success, required this.message});

  List<Object> get props => [success, message];
}

// l'utilisateur se déconnecte
class EventSettingsLogout extends EventSettings {}

// l'utilisateur modifie la taile de la police
class EventSettingsModifyFontSize extends EventSettings {
  final bool success;
  final String message;
  final double fontSize;

  EventSettingsModifyFontSize(
      {required this.success, required this.message, required this.fontSize});

  List<Object> get props => [success, message, fontSize];
}

// l'utilisateur modifie ses accords cgu
class EventSettingsModifyTOU extends EventSettings {
  final bool success;
  final bool accordCGU;

  EventSettingsModifyTOU({required this.success, required this.accordCGU});

  List<Object> get props => [success, accordCGU];
}

class EventSettingsGetUserGuide extends EventSettings {}

class EventSettingsGetTOU extends EventSettings {}

class EventSettingsGetPrivacyPolicy extends EventSettings {}
