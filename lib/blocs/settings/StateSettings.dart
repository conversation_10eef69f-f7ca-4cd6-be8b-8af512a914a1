import 'package:flutter/material.dart';

abstract class StateSettings {
  List<Object> get props => [];
}

// état écran principal
class StateSettingsMainScreen extends StateSettings {}

// état après modification photo utilisateur
class StateSettingsModifyPhoto extends StateSettings {
  final bool confirm;
  final Image newPhoto;

  StateSettingsModifyPhoto({required this.confirm, required this.newPhoto});

  List<Object> get props => [confirm, newPhoto];
}

// apres suppression de la photo
class StateSettingsPhotoDeleted extends StateSettings {
  final Image newPhoto;

  StateSettingsPhotoDeleted({required this.newPhoto});

  List<Object> get props => [newPhoto];
}

// en cas de modification de l'email
class StateSettingsModifyEmail extends StateSettings {
  final bool success;
  final String message;

  StateSettingsModifyEmail({required this.success, required this.message});

  List<Object> get props => [success, message];
}

// l'utilisateur se déconnecte
class StateSettingsLogout extends StateSettings {}

// l'utilisateur modifie la taile de la police
class StateSettingsModifyFontSize extends StateSettings {
  final bool success;
  final String message;
  final double fontSize;

  StateSettingsModifyFontSize(
      {required this.success, required this.message, required this.fontSize});

  List<Object> get props => [success, message, fontSize];
}

// l'utilisateur modifie ses accords cgu
class StateSettingsModifyTOU extends StateSettings {
  final bool? accordCGU;
  final bool success;
  final String message;

  StateSettingsModifyTOU(
      {required this.accordCGU, required this.success, required this.message});

  List<Object> get props => [accordCGU!, success, message];
}

class StateSettingsLoading extends StateSettings {
  final bool loading;

  StateSettingsLoading({required this.loading});

  List<Object> get props => [loading];
}
