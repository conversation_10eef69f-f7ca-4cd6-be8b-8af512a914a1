import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/settings/email/EventModifyEmail.dart';
import 'package:theradom/blocs/settings/email/StateModifyEmail.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/NewAdressRequest.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_parametres_repository.dart';
import 'package:theradom/utils/Translations.dart';

class BlocModifyEmail extends Bloc<EventModifyEmail, StateModifyEmail> {
  final _regExp = RegExp(r"\s+");
  late final MobileParametresRepository _mobileParametresRepository;

  BlocModifyEmail({MobileParametresRepository? mobileParametresRepository})
      : super(StateModifyEmailUninitialised()) {
    _mobileParametresRepository =
        mobileParametresRepository ?? MobileParametresRepository();
    on<EventModifyEmailValidate>(_modifyEmail);
  }

  void _modifyEmail(
      EventModifyEmailValidate event, Emitter<StateModifyEmail> emit) async {
    // validation de la modification de l'email

    emit(StateEmailModified(loading: true, success: null, message: null));

    final String newEmail = event.newEmail.replaceAll(_regExp, "");
    final String newEmailConfirmation =
        event.newEmailConfirmation.replaceAll(_regExp, "");

    if (newEmail == newEmailConfirmation) {
      try {
        NewAdressRequest newAdressRequest = NewAdressRequest(
            newMail: event.newEmail, numpat: Config.basicUserInfos!.numpat);
        final newAdressRequestJson = newAdressRequestToJson(newAdressRequest);
        await _mobileParametresRepository.changeUserEmail(newAdressRequestJson);
        emit(StateEmailModified(
            loading: false,
            success: true,
            message: allTranslations.text("email_modif_success")));
      } on WebServiceCallException catch (e) {
        emit(StateEmailModified(
            loading: false, success: false, message: e.toString()));
      }
    } else {
      emit(StateEmailModified(
          loading: false,
          success: false,
          message: allTranslations.text("emails_dont_match")));
    }
  }
}
