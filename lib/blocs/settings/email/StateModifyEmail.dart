abstract class StateModifyEmail {
  List<Object> get props => [];
}

// non init
class StateModifyEmailUninitialised extends StateModifyEmail {}

class StateEmailModified extends StateModifyEmail {
  final bool loading;
  final bool? success;
  final String? message;

  StateEmailModified(
      {required this.loading, required this.success, required this.message});

  @override
  List<Object> get props => [loading, success!, message!];
}
