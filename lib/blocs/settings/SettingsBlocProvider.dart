import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/settings/BlocSettings.dart';

class SettingsBlocProvider extends StatelessWidget {
  final Widget child;

  SettingsBlocProvider({
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<BlocSettings>(
      create: (context) {
        return BlocSettings();
      },
      child: child,
    );
  }
}
