abstract class EventModifyPassword {
  List<Object> get props => [];
}

// event modification du password
class EventModifyPasswordValidate extends EventModifyPassword {
  final String newPassword;
  final String newPasswordConfirmation;

  EventModifyPasswordValidate(
      {required this.newPassword, required this.newPasswordConfirmation});

  List<Object> get props => [newPassword, newPasswordConfirmation];
}
