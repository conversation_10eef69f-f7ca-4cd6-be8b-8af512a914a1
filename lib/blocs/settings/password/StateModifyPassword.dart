abstract class StateModifyPassword {
  List<Object> get props => [];
}

// état non init
class StateModifyPasswordUninitialised extends StateModifyPassword {}

class StatePasswordModified extends StateModifyPassword {
  final bool loading;
  final bool? success;
  final String? message;

  StatePasswordModified(
      {required this.loading, required this.success, required this.message});

  @override
  List<Object> get props => [loading, success!, message!];
}
