import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/settings/password/EventModifyPassword.dart';
import 'package:theradom/blocs/settings/password/StateModifyPassword.dart';
import 'package:theradom/config.dart';

import 'package:theradom/models/NewPasswordRequest.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_parametres_repository.dart';
import 'package:theradom/utils/EncryptionUtils.dart';
import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/Translations.dart';

class BlocModifyPassword
    extends Bloc<EventModifyPassword, StateModifyPassword> {
  late final MobileParametresRepository _mobileParametresRepository;
  final RegExp _regExp = RegExp(r"\s+");

  BlocModifyPassword({MobileParametresRepository? mobileParametresRepository})
      : super(StateModifyPasswordUninitialised()) {
    _mobileParametresRepository =
        mobileParametresRepository ?? MobileParametresRepository();
    on<EventModifyPasswordValidate>(_modifyPassword);
  }

  void _modifyPassword(EventModifyPasswordValidate event,
      Emitter<StateModifyPassword> emit) async {
    emit(StatePasswordModified(loading: true, success: null, message: null));

    final String newPassword = event.newPassword.replaceAll(_regExp, "");
    final String newPasswordConfirmation =
        event.newPasswordConfirmation.replaceAll(_regExp, "");

    if (newPassword == newPasswordConfirmation) {
      String pwdHash = EncryptionUtils(newPassword).toMd5();

      try {
        NewPasswordRequest newPasswordRequest = NewPasswordRequest(
            numpat: Config.basicUserInfos!.numpat, newPwd: pwdHash);
        final newPasswordRequestJson =
            newPasswordRequestToJson(newPasswordRequest);
        await _mobileParametresRepository
            .changeUserPassword(newPasswordRequestJson);
        await _updateBiometrics(pwdHash);
        emit(StatePasswordModified(
            loading: false,
            success: true,
            message: allTranslations.text("pwd_modif_success")));
      } on WebServiceCallException catch (e) {
        emit(StatePasswordModified(
            loading: false, success: false, message: e.toString()));
      }
    } else {
      emit(StatePasswordModified(
          loading: false,
          success: false,
          message: allTranslations.text("pwds_dont_match")));
    }
  }

  Future<void> _updateBiometrics(String newPwdHash) async {
    // update du pwd à retenir
    if (Config.useBiometricID!) {
      await SharedPreferencesUtil.instance
          .setString(Config.prefPassword, newPwdHash);
      Config.userAccountInfos!.passwordHash = newPwdHash;
    }
  }
}
