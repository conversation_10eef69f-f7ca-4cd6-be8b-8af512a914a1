import 'package:theradom/models/appointment.dart';

abstract class StateCalendar {
  List<Object> get props => [];
}

// état non-initialisé
class StateCalendarUninitialized extends StateCalendar {}

// écran initial
class StateCalendarMainScreenInit extends StateCalendar {
  final Map<DateTime, List<Appointment>> map;

  StateCalendarMainScreenInit({
    required this.map,
  });

  @override
  List<Object> get props => [map];
}

// état rdv confirmé
class StateCalendarAcknowledgeAppointment extends StateCalendar {
  final bool success;
  final DateTime dateTime;
  final String cleFac;

  StateCalendarAcknowledgeAppointment(
      {required this.success, required this.dateTime, required this.cleFac});

  @override
  List<Object> get props => [success, dateTime, cleFac];
}

// état erreur
class StateCalendarError extends StateCalendar {
  final String message;

  StateCalendarError({required this.message});

  List<Object> get props => [message];
}

// état chargement de nouveaux rdv
class StateCalendarLoadingAppointments extends StateCalendar {
  final bool loading;

  StateCalendarLoadingAppointments({required this.loading});

  @override
  List<Object> get props => [loading];
}

// état ajout de nouveaux rdv au calendrier
class StateCalendarNewAppointmentsToAdd extends StateCalendar {
  final Map<DateTime, List<Appointment>> map;

  StateCalendarNewAppointmentsToAdd({required this.map});

  @override
  List<Object> get props => [map];
}
