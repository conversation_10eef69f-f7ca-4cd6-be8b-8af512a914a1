import 'package:theradom/models/ErrorDetails.dart';

abstract class EventCalendar {
  List<Object> get props => [];
}

// event pour obtenir les rdv de l'année courante
class EventCalendarGetAllUserAppointmentsInit extends EventCalendar {}

class EventCalendarGetAllUserAppointments extends EventCalendar {
  final DateTime dateTimeFirst;
  final DateTime dateTimeLast;

  EventCalendarGetAllUserAppointments(
      {required this.dateTimeFirst, required this.dateTimeLast});

  @override
  get props => [dateTimeFirst, dateTimeLast];
}

// event user acknowledge un rdv
class EventCalendarAcknowledgeAppointment extends EventCalendar {
  final DateTime dateTime;
  final String cleFac;

  EventCalendarAcknowledgeAppointment(
      {required this.dateTime, required this.cleFac});

  List<Object> get props => [dateTime, cleFac];
}

class EventCalendarError extends EventCalendar {
  final ErrorDetails errorDetails;

  EventCalendarError({required this.errorDetails});

  @override
  List<Object> get props => [errorDetails];
}
