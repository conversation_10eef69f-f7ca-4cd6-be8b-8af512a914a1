import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/calendar/bloc_calendar.dart';
import 'package:theradom/blocs/calendar/event_calendar.dart';

class CalendarBlocProvider extends StatelessWidget {
  final Widget child;

  CalendarBlocProvider({required this.child});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<BlocCalendar>(
      create: (context) {
        return BlocCalendar()..add(EventCalendarGetAllUserAppointmentsInit());
      },
      child: child,
    );
  }
}
