import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/calendar/event_calendar.dart';
import 'package:theradom/blocs/calendar/state_calendar.dart';
import 'package:theradom/config.dart';

import 'package:theradom/models/AppointmentIdentification.dart';
import 'package:theradom/models/GetAppointmentsRequest.dart';
import 'package:theradom/models/GetAppointmentsResponse.dart';
import 'package:theradom/models/ReminderStatusRequest.dart';
import 'package:theradom/models/appointment.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_rdv_repository.dart';
import 'package:theradom/extensions/datetime_extension.dart';

class BlocCalendar extends Bloc<EventCalendar, StateCalendar> {
  late final MobileRdvRepository _mobileRdvRepository;
  late DateTime minFetchedDate; // date minimale que j'ai requété
  late DateTime maxFetchedDate; // date maximale que j'ai requété

  BlocCalendar({MobileRdvRepository? mobileRdvRepository})
      : super(StateCalendarUninitialized()) {
    _mobileRdvRepository = mobileRdvRepository ?? MobileRdvRepository();
    on<EventCalendarGetAllUserAppointmentsInit>(_getAllAppointmentsInitial);
    on<EventCalendarGetAllUserAppointments>(_getAppointments);
    on<EventCalendarAcknowledgeAppointment>(_onAppointmentAcknowledge);
  }

  void _getAllAppointmentsInitial(EventCalendarGetAllUserAppointmentsInit event,
      Emitter<StateCalendar> emit) async {
    // fetch les rdv de l'année courante, mise en forme et génération du state
    emit(StateCalendarUninitialized());

    DateTime now = DateTime.now(); // date du jour
    DateTime dateTimeFrom = DateTime(now.year, now.month - 1, 1);
    DateTime dateTimeTo = DateTime(now.year, now.month + 2, 0);
    String fromStr = dateTimeFrom.toYYYYMMDD();
    String toStr = dateTimeTo.toYYYYMMDD();

    try {
      GetAppointmentsRequest getAppointmentsRequest = GetAppointmentsRequest(
          numpat: Config.basicUserInfos!.numpat, from: fromStr, to: toStr);
      String getAppointmentsRequestJson =
          getAppointmentsRequestToJson(getAppointmentsRequest);
      GetAppointmentsResponse getAppointmentsResponse =
          await _mobileRdvRepository
              .fetchAppointments(getAppointmentsRequestJson);
      List<Appointment> _listAppointments = [];
      getAppointmentsResponse.rdv.forEach((k, appointment) {
        _listAppointments.add(appointment);
      });

      // je garde la trace de l'intervalle des dates déjà connu
      minFetchedDate = dateTimeFrom;
      maxFetchedDate = dateTimeTo;

      Map<DateTime, List<Appointment>> map =
          _transformAppointmentListToMap(_listAppointments);

      emit(StateCalendarMainScreenInit(map: map));
    } on WebServiceCallException catch (e) {
      emit(StateCalendarError(message: e.toString()));
    }
  }

  void _getAppointments(EventCalendarGetAllUserAppointments event,
      Emitter<StateCalendar> emit) async {
    // fetch les rdv de l'année à laquelle le user s'est déplacé

    DateTime last = event.dateTimeLast;
    DateTime first = event.dateTimeFirst;
    late String from;
    late String to;
    bool fetch = false;
    if (last.isBefore(minFetchedDate)) {
      fetch = true;
      DateTime dateTimeFrom = DateTime(last.year, last.month - 3, 0);
      DateTime dateTimeTo = last;
      minFetchedDate = dateTimeFrom;
      from = dateTimeFrom.toYYYYMMDD();
      to = dateTimeTo.toYYYYMMDD();
    } else if (first.isAfter(maxFetchedDate)) {
      fetch = true;
      DateTime dateTimeFrom = last;
      DateTime dateTimeTo = DateTime(last.year, last.month + 3, 0);
      maxFetchedDate = dateTimeTo;
      from = dateTimeFrom.toYYYYMMDD();
      to = dateTimeTo.toYYYYMMDD();
    }

    if (fetch) {
      // je dois aller chercher les données
      emit(StateCalendarLoadingAppointments(loading: true));

      try {
        GetAppointmentsRequest getAppointmentsRequest = GetAppointmentsRequest(
            numpat: Config.basicUserInfos!.numpat, from: from, to: to);
        String getAppointmentsRequestJson =
            getAppointmentsRequestToJson(getAppointmentsRequest);
        GetAppointmentsResponse getAppointmentsResponse =
            await _mobileRdvRepository
                .fetchAppointments(getAppointmentsRequestJson);
        List<Appointment> _listAppointments = [];
        getAppointmentsResponse.rdv.forEach((k, appointment) {
          _listAppointments.add(appointment);
        });
        Map<DateTime, List<Appointment>> map =
            _transformAppointmentListToMap(_listAppointments);
        //listYear.add(yearInt);
        emit(StateCalendarNewAppointmentsToAdd(map: map));
      } on WebServiceCallException catch (e) {
        emit(StateCalendarError(message: e.toString()));
      }
      emit(StateCalendarLoadingAppointments(loading: false));
    }
  }

  void _onAppointmentAcknowledge(
      EventCalendarAcknowledgeAppointment event, Emitter<StateCalendar> emit) {
    // le user acknowledge un rdv
    final String cleFac = event.cleFac;

    try {
      AppointmentIdentification appointmentIdentification =
          AppointmentIdentification(
              numpat: Config.basicUserInfos!.numpat, cleFac: cleFac);
      String appointmentIdentificationJson =
          appointmentIdentificationToJson(appointmentIdentification);
      _mobileRdvRepository
          .acknowledgeAppointment(appointmentIdentificationJson);
      ReminderStatusRequest reminderStatusRequest = ReminderStatusRequest(
          numpat: Config.basicUserInfos!.numpat, cleFac: cleFac, rappel: true);
      String reminderStatusRequestJson =
          reminderStatusRequestToJson(reminderStatusRequest);
      _mobileRdvRepository.remindAppointment(reminderStatusRequestJson);

      emit(StateCalendarAcknowledgeAppointment(
          success: true, dateTime: event.dateTime, cleFac: cleFac));
    } on WebServiceCallException catch (_) {
      emit(StateCalendarAcknowledgeAppointment(
          success: false, dateTime: event.dateTime, cleFac: cleFac));
    }
  }

  Map<DateTime, List<Appointment>> _transformAppointmentListToMap(
      List<Appointment> appointmentList) {
    // transforme List<Appointments> en un objet Map<DateTime, List<Appointments>> pour le calendrier
    Map<DateTime, List<Appointment>> events = {};
    appointmentList.forEach((appointment) {
      DateTime key = appointment.date;
      if (events[key] == null) {
        events[key] = [];
      }
      events[key]!.add(appointment);
    });
    return events;
  }
}
