import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/qrcode/BlocQrCode.dart';
import 'package:theradom/services/repositories/mobile_status_repository.dart';

class QrCodeBlocProvider extends StatelessWidget {
  final Widget child;
  final int action;
  final MobileStatusRepository _mobileStatusRepository =
      MobileStatusRepository();

  QrCodeBlocProvider({required this.child, required this.action});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<BlocQrCode>(
      create: (context) {
        return BlocQrCode(
            action: action, mobileStatusRepository: _mobileStatusRepository);
      },
      child: child,
    );
  }
}
