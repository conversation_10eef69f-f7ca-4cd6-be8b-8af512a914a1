import 'dart:convert';
import 'package:bloc/bloc.dart';
import 'package:theradom/blocs/qrcode/EventQrCode.dart';
import 'package:theradom/blocs/qrcode/StateQrCode.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/BasicUserInfos.dart';
import 'package:theradom/models/QRcodeMessage.dart';
import 'package:theradom/models/jours_dial.dart';
import 'package:theradom/utils/EncryptionUtils.dart';
import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/models/gs1code128data.dart';
import 'package:theradom/services/repositories/mobile_status_repository.dart';

class BlocQrCode extends Bloc<EventQrCode, StateQrCode> {
  final int action;
  final MobileStatusRepository _mobileStatusRepository;

  BlocQrCode({
    required this.action,
    required MobileStatusRepository mobileStatusRepository,
  })  : _mobileStatusRepository = mobileStatusRepository,
        super(StateQrCodeInitial()) {
    on<EventQrCodeDataDetected>(_onQrCodeDetection);
    on<EventCheckUserStatus>(_onCheckUserStatus);
  }

  /// Gère la détection et le traitement d'un QR code
  /// @param event L'événement contenant les données du QR code scanné
  /// @param emit L'émetteur pour envoyer les nouveaux états
  /// Selon l'action (0 ou 1):
  /// - action 0: Traite un QR code de paramétrage (décryptage, extraction des infos utilisateur)
  /// - action 1: Traite un QR code de numéro de lot (format GS1-128)
  void _onQrCodeDetection(
    EventQrCodeDataDetected event,
    Emitter<StateQrCode> emit,
  ) async {
    switch (action) {
      case 0:
        {
          bool errorOccured = false;
          bool passwordCheckTemp;
          QRcodeMessage? qrCodeMessageObject;

          try {
            final decryptedString =
                EncryptionUtils(event.scanData ?? "").aesDecrypt();
            if (decryptedString != null) {
              qrCodeMessageObject = qrCodeMessagefromJson(decryptedString);

              Config.nomDomaine = qrCodeMessageObject.domainName;
              Config.basicUserInfos = BasicUserInfos(
                valide: "",
                changementMdp: "",
                taillePolice: 0,
                accordCgu: false,
                ins: "",
                mail: "",
                numpat: "",
                numSeance: {},
                serviceToken: "",
                localisation: "",
                enumMotifNonAdmin: {},
                joursDial: JoursDial(),
                seanceNonFinie: {},
                login: "",
                dateSeanceNonFinie: {},
                heureSeanceNonFinie: {},
                heureDebutSeance: {},
                monitorings: {},
                statut: "",
                lastEvaluation: 0,
                firstPage: "",
                monitoringCount: 0,
                JWT: "",
                algoPwd: AlgoPwd(),
              );
              Config.basicUserInfos!.numpat = qrCodeMessageObject.numpat;
              Config.userAccountInfos!.login = qrCodeMessageObject.login;
              Config.userAccountInfos!.INS = qrCodeMessageObject.INS;

              await SharedPreferencesUtil.instance.setString(
                Config.prefDomainName,
                qrCodeMessageObject.domainName,
              );

              // Émettre l'événement EventCheckUserStatus ici
              add(EventCheckUserStatus(
                ins: Config.userAccountInfos!.INS,
                login: Config.userAccountInfos!.login,
                userIdentity:
                    "${qrCodeMessageObject.prenom} ${qrCodeMessageObject.nom}",
              ));
            } else {
              errorOccured = true;
            }
          } catch (error, stacktrace) {
            errorOccured = true;
            print("[ERROR] Échec du décryptage : $error\n$stacktrace");
          }

          passwordCheckTemp = qrCodeMessageObject?.password != "";

          emit(
            StateQrCodeUserDetected(
              errorOccured: errorOccured,
              nom: qrCodeMessageObject?.nom ?? "",
              prenom: qrCodeMessageObject?.prenom ?? "",
              mail: qrCodeMessageObject?.mail ?? "",
              passwordCheck: passwordCheckTemp,
            ),
          );

          break;
        }

      case 1:
        {
          GS1Code128Data data = GS1Code128Data(event.scanData ?? "");
          emit(StateQrCodeGS1Detected(gs1code128data: data));
          break;
        }
    }
  }

  /// Vérifie le statut d'un utilisateur auprès du serveur
  /// @param event L'événement contenant l'INS, le login et l'identité de l'utilisateur
  /// @param emit L'émetteur pour envoyer les nouveaux états
  /// @throws Émet un StateQrCodeError en cas d'erreur
  /// Vérifie notamment si un nouveau mot de passe est nécessaire
  void _onCheckUserStatus(
    EventCheckUserStatus event,
    Emitter<StateQrCode> emit,
  ) async {
    try {
      final jsonResponse =
          await _mobileStatusRepository.checkUserStatus(event.ins, event.login);

      final decodedResponse = jsonDecode(jsonResponse) as Map<String, dynamic>;

      //Pas de conversion implice de boulean en string
      final newPasswordNeeded =
          decodedResponse["new_password_needed"] == "true" ||
              decodedResponse["new_password_needed"] == true;

      emit(StateQrCodeUserStatusChecked(
        newPasswordNeeded: newPasswordNeeded,
        userIdentity: event.userIdentity,
      ));
    } catch (error) {
      emit(StateQrCodeError(error: error.toString()));
    }
  }
}
