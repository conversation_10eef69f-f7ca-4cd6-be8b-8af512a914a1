abstract class EventQrCode {
  const EventQrCode();

  List<Object> get props => [];
}

// event détection d'un qr code
class EventQrCodeDataDetected extends EventQrCode {
  final String? scanData;

  EventQrCodeDataDetected({required this.scanData});

  @override
  List<Object> get props => [scanData!];
}


class EventCheckUserStatus extends EventQrCode {
  final String ins;
  final String login;
  final String userIdentity;
  EventCheckUserStatus({required this.ins, required this.login, required this.userIdentity});
}
