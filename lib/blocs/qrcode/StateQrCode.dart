import 'package:theradom/models/gs1code128data.dart';

abstract class StateQrCode {
  const StateQrCode();

  List<Object> get props => [];
}

// état initial
class StateQrCodeInitial extends StateQrCode {}

// état qr code valide détecté
class StateQrCodeUserDetected extends StateQrCode {
  final bool errorOccured;
  final String nom;
  final String prenom;
  final String mail;
  final bool passwordCheck;

  StateQrCodeUserDetected(
      {required this.errorOccured,
      required this.nom,
      required this.prenom,
      required this.mail,
      required this.passwordCheck});

  @override
  List<Object> get props => [errorOccured, nom, prenom];
}

class StateQrCodeGS1Detected extends StateQrCode {
  final GS1Code128Data gs1code128data;

  StateQrCodeGS1Detected({required this.gs1code128data});

  @override
  List<Object> get props => [gs1code128data];
}

class StateQrCodeUserStatusChecked extends StateQrCode {
  final bool newPasswordNeeded;
  final String userIdentity; // Ajout de cette ligne

  StateQrCodeUserStatusChecked({
    required this.newPasswordNeeded,
    required this.userIdentity, // Ajout de cette ligne
  });
}

class StateQrCodeError extends StateQrCode {
  final String error;
  StateQrCodeError({required this.error});
}
