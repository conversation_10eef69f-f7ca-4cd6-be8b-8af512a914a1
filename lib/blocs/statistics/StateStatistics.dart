import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/stat_field_data_group.dart';
import 'package:theradom/models/syncfusion_chart_data.dart';

abstract class StateStatistics {
  List<Object> get props => [];
}

// état de chargement des données
class StateStatisticsUninitialized extends StateStatistics {}

// état de chargement des données qui vont être affichées
class StateStatisticsLoadingCharts extends StateStatistics {
  final bool loading;
  final bool reloadChart;

  StateStatisticsLoadingCharts(
      {required this.loading, required this.reloadChart});

  get props => [loading, reloadChart];
}

// état de l'éran de choix entre données simple et multiple
class StateStatisticsMainScreen extends StateStatistics {
  final ErrorDetails? errorDetailsPreviousDataResponse;
  final List<StatsFieldDataGroup> simpleDataGroupList;
  final List<StatsFieldDataGroup> multipleDataGroupList;

  StateStatisticsMainScreen(
      {required this.errorDetailsPreviousDataResponse,
      required this.simpleDataGroupList,
      required this.multipleDataGroupList});

  @override
  get props => [
        errorDetailsPreviousDataResponse!,
        simpleDataGroupList,
        multipleDataGroupList
      ];
}

// état pour l'affichage des courbes
class StateStatisticsDisplay extends StateStatistics {
  final List<SyncfusionChartSeries> chartSeriesList;
  final ErrorDetails? errorDetails;
  final bool reloadChart;

  StateStatisticsDisplay(
      {required this.chartSeriesList,
      required this.errorDetails,
      required this.reloadChart});

  get props => [errorDetails!, reloadChart];
}
