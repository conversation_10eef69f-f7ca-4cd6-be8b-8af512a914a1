import 'package:theradom/models/stat_field_data_group.dart';

abstract class EventStatistics {
  List<Object> get props => [];
}

// event pour se rendre dans l'écran de choix des données
class EventStatisticsInitialMainScreen extends EventStatistics {}

class EventStatisticsMainScreen extends EventStatistics {}

// event pour afficher les courbes
class EventStatisticsDisplay extends EventStatistics {
  final List<StatsFieldDataGroup> simpleDataGroupList;
  final List<StatsFieldDataGroup> multipleDataGroupList;
  final DateTime startDate;
  final DateTime endDate;
  final String format;
  final String type;
  final bool reloadChart;

  EventStatisticsDisplay(
      {required this.simpleDataGroupList,
      required this.multipleDataGroupList,
      required this.startDate,
      required this.endDate,
      required this.format,
      required this.type,
      required this.reloadChart});

  List<Object> get props => [startDate, endDate, format, type, reloadChart];
}
