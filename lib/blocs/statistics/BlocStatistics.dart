import 'package:bloc/bloc.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/GetPreviousDataRequest.dart';
import 'package:theradom/models/get_previous_data_response_2.dart';
import 'package:theradom/models/requested_field.dart';
import 'package:theradom/models/stat_field_data_group.dart';
import 'package:theradom/models/stats_field_data.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_seance_repository.dart';
import 'package:theradom/blocs/statistics/EventStatistics.dart';
import 'package:theradom/blocs/statistics/StateStatistics.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/GetPreviousDataResponse.dart';
import 'package:theradom/models/syncfusion_chart_data.dart';
import 'package:theradom/utils/Translations.dart';

class BlocStatistics extends Bloc<EventStatistics, StateStatistics> {
  late final MobileSeanceRepository _mobileSeanceRepository;
  final String suivi;
  List<StatsFieldDataGroup> _simpleGroupList = [];
  List<StatsFieldDataGroup> _multipleGroupList = [];
  ErrorDetails? errorDetails;

  BlocStatistics(
      {required this.suivi, MobileSeanceRepository? mobileSeanceRepository})
      : super(StateStatisticsUninitialized()) {
    _mobileSeanceRepository =
        mobileSeanceRepository ?? MobileSeanceRepository();
    on<EventStatisticsInitialMainScreen>(_fetchAvailableData);
    on<EventStatisticsMainScreen>(_returnAvailableData);
    on<EventStatisticsDisplay>(_fetchDetailedData);
  }

  void _fetchAvailableData(EventStatisticsInitialMainScreen event,
      Emitter<StateStatistics> emit) async {
    // chargement des données dispo pour affichage, création des différents éléments de l'interface
    emit(StateStatisticsUninitialized());

    // requête des données disponibles pour l'affichage
    print("🔍 STATS DEBUG: Début _fetchAvailableData pour suivi: $suivi");
    try {
      RequestedField requestedField =
          RequestedField(listFieldId: null, listFiledName: null);
      GetPreviousDataRequest getPreviousDataRequest = GetPreviousDataRequest(
          numpat: Config.basicUserInfos!.numpat,
          suivi: suivi,
          hasList: false,
          from: null,
          to: null,
          type: "simple",
          format: null,
          champs: requestedField);
      String getPreviousDataRequestJson =
          getPreviousDataRequestToJson(getPreviousDataRequest);
      print(
          "🔍 STATS DEBUG: Requête fetchAvailableFields avec: $getPreviousDataRequestJson");
      GetPreviousDataResponse getPreviousDataResponse =
          await _mobileSeanceRepository
              .fetchAvailableFields(getPreviousDataRequestJson);
      print(
          "🔍 STATS DEBUG: Réponse fetchAvailableFields reçue: ${getPreviousDataResponse.valide}");
      // tag list
      List<String> tagslist = [];
      getPreviousDataResponse.seance!.tags.forEach((key, groupName) {
        tagslist.add(groupName);
      });

      /// Données simples

      // données groupées
      tagslist.forEach((groupName) {
        List<StatsFieldData> statsFieldDataList1 = [];
        if (groupName != "noTag") {
          int index = tagslist.indexOf(groupName);
          getPreviousDataResponse.seance!.unique.received
              .forEach((key, fieldInfos) {
            if (fieldInfos.tag == index.toString()) {
              statsFieldDataList1.add(
                  StatsFieldData(id: fieldInfos.id, name: fieldInfos.name));
            }
          });
          getPreviousDataResponse.seance!.unique.basics
              .forEach((key, fieldInfos) {
            if (fieldInfos.tag == index.toString()) {
              statsFieldDataList1.add(
                  StatsFieldData(id: fieldInfos.id, name: fieldInfos.name));
            }
          });
          if (statsFieldDataList1.length > 0) {
            _simpleGroupList.add(StatsFieldDataGroup(
                name: groupName, statsFieldDataList: statsFieldDataList1));
          }
        }
      });

      // saisies en séance
      List<StatsFieldData> statsFieldDataList2 = [];
      getPreviousDataResponse.seance!.unique.received
          .forEach((key, fieldInfos) {
        if (getPreviousDataResponse.seance!.tags[fieldInfos.tag] == "noTag") {
          // pas de tag, je créé une ligne dans la liste
          statsFieldDataList2
              .add(StatsFieldData(name: fieldInfos.name, id: fieldInfos.id));
        }
      });
      if (statsFieldDataList2.length > 0) {
        _simpleGroupList.add(StatsFieldDataGroup(
            name: allTranslations.text("entered"),
            statsFieldDataList: statsFieldDataList2));
      }

      // calculées
      List<StatsFieldData> statsFieldDataList3 = [];
      getPreviousDataResponse.seance!.unique.basics.forEach((key, fieldInfos) {
        if (getPreviousDataResponse.seance!.tags[fieldInfos.tag] == "noTag") {
          // pas de tag, je créé une ligne dans la liste
          statsFieldDataList3
              .add(StatsFieldData(name: fieldInfos.name, id: fieldInfos.id));
        }
      });
      if (statsFieldDataList3.length > 0) {
        _simpleGroupList.add(StatsFieldDataGroup(
            name: allTranslations.text('calculated'),
            statsFieldDataList: statsFieldDataList3));
      }

      /// Données multiples

      // données groupées
      tagslist.forEach((groupName) {
        List<StatsFieldData> statsFieldDataList4 = [];
        if (groupName != "noTag") {
          int index = tagslist.indexOf(groupName);
          getPreviousDataResponse.seance!.table.forEach((key, fieldInfos) {
            if (fieldInfos.tag == index.toString()) {
              statsFieldDataList4.add(
                  StatsFieldData(id: fieldInfos.id, name: fieldInfos.name));
            }
          });
          if (statsFieldDataList4.length > 0) {
            _multipleGroupList.add(StatsFieldDataGroup(
                name: groupName, statsFieldDataList: statsFieldDataList4));
          }
        }
      });

      // autres données
      List<StatsFieldData> statsFieldDataList5 = [];
      getPreviousDataResponse.seance!.table.forEach((key, fieldInfos) {
        if (getPreviousDataResponse.seance!.tags[fieldInfos.tag] == "noTag") {
          // pas de tag, je créé une ligne dans la liste
          statsFieldDataList5
              .add(StatsFieldData(name: fieldInfos.name, id: fieldInfos.id));
        }
      });
      if (statsFieldDataList5.length > 0) {
        _multipleGroupList.add(StatsFieldDataGroup(
            name: allTranslations.text('other_data'),
            statsFieldDataList: statsFieldDataList5));
      }

      print(
          "🔍 STATS DEBUG: Émission StateStatisticsMainScreen avec ${_simpleGroupList.length} groupes simples et ${_multipleGroupList.length} groupes multiples");
      emit(StateStatisticsMainScreen(
          errorDetailsPreviousDataResponse: null,
          simpleDataGroupList: _simpleGroupList,
          multipleDataGroupList: _multipleGroupList));
    } on WebServiceCallException catch (e) {
      errorDetails = ErrorDetails(code: "", message: e.toString());
      emit(StateStatisticsMainScreen(
          errorDetailsPreviousDataResponse:
              ErrorDetails(code: "", message: e.toString()),
          simpleDataGroupList: [],
          multipleDataGroupList: []));
    }
  }

  void _returnAvailableData(
      EventStatisticsMainScreen event, Emitter<StateStatistics> emit) async {
    emit(StateStatisticsMainScreen(
        errorDetailsPreviousDataResponse: errorDetails,
        simpleDataGroupList: _simpleGroupList,
        multipleDataGroupList: _multipleGroupList));
  }

  void _fetchDetailedData(
      EventStatisticsDisplay event, Emitter<StateStatistics> emit) async {
    print("🔍 BLOC TRACE 1: Début _fetchDetailedData");
    print(
        "🔍 BLOC TRACE 2: startDate: ${event.startDate}, endDate: ${event.endDate}");
    print(
        "🔍 BLOC TRACE 3: type: ${event.type}, reloadChart: ${event.reloadChart}");
    print(
        "🔍 BLOC TRACE 4: simpleDataGroupList length: ${event.simpleDataGroupList.length}");
    print(
        "🔍 BLOC TRACE 5: multipleDataGroupList length: ${event.multipleDataGroupList.length}");

    // mise en forme des données pour l'affichage du graphe
    print("🔍 BLOC TRACE 6: Avant émission StateStatisticsLoadingCharts");
    emit(StateStatisticsLoadingCharts(
        loading: true, reloadChart: event.reloadChart));
    print("🔍 BLOC TRACE 7: Après émission StateStatisticsLoadingCharts");

    ErrorDetails? yErrorDetails;

    String startDateStr = event.startDate.toYYYYMMDD();
    String endDateStr = event.endDate.toYYYYMMDD();

    List<String> fieldNameList = [];
    List<String> fieldIdList = [];
    if (event.type == "simple") {
      event.simpleDataGroupList.forEach((group) {
        group.statsFieldDataList.forEach((field) {
          if (field.selected) {
            fieldIdList.add(field.id);
            fieldNameList.add(field.name);
          }
        });
      });
    } else if (event.type == "table") {
      event.multipleDataGroupList.forEach((group) {
        group.statsFieldDataList.forEach((field) {
          if (field.selected) {
            fieldIdList.add(field.id);
            fieldNameList.add(field.name);
          }
        });
      });
    }

    List<SyncfusionChartSeries> chartSeriesList = [];

    try {
      RequestedField requestedField = RequestedField(
          listFieldId: fieldIdList, listFiledName: fieldNameList);
      GetPreviousDataRequest getPreviousDataRequest = GetPreviousDataRequest(
          numpat: Config.basicUserInfos!.numpat,
          suivi: suivi,
          hasList: true,
          from: startDateStr,
          to: endDateStr,
          type: event.type,
          format: event.format,
          champs: requestedField);
      String getPreviousDataRequestJson =
          getPreviousDataRequestToJson(getPreviousDataRequest);
      print(
          "🔍 STATS DEBUG: Requête fetchFieldValues avec: $getPreviousDataRequestJson");
      GetPreviousDataResponse2 getPreviousDataResponse2 =
          await _mobileSeanceRepository
              .fetchFieldValues(getPreviousDataRequestJson);
      print(
          "🔍 STATS DEBUG: Réponse fetchFieldValues reçue, seance != null: ${getPreviousDataResponse2.seance != null}");
      if (getPreviousDataResponse2.seance != null) {
        List repList = _getPreviousDataResponseToSeriesDoubleDouble(
            getPreviousDataResponse2, event.startDate);
        if (repList[0] != null) {
          yErrorDetails = ErrorDetails(code: "", message: repList[0]);
        }
        chartSeriesList = repList[1];
      } else {
        yErrorDetails = ErrorDetails(
            code: "", message: allTranslations.text("no_data_to_display"));
      }
    } on WebServiceCallException catch (e) {
      yErrorDetails = ErrorDetails(code: "500", message: e.toString());
    }

    print(
        "🔍 STATS DEBUG: Émission StateStatisticsDisplay avec ${chartSeriesList.length} séries de données, erreur: ${yErrorDetails?.message}");

    // Fermer la popup de chargement seulement si on n'est pas en mode reloadChart
    if (!event.reloadChart) {
      emit(StateStatisticsLoadingCharts(loading: false, reloadChart: false));
    }

    // Puis émettre les données
    emit(StateStatisticsDisplay(
        chartSeriesList: chartSeriesList,
        errorDetails: yErrorDetails,
        reloadChart: event.reloadChart));
  }

  List _getPreviousDataResponseToSeriesDoubleDouble(
      GetPreviousDataResponse2 getPreviousDataResponseTable,
      DateTime dateTimeFrom) {
    // créé une liste de séries pour Syncfusion à partir de GetPreviousDataResponse2

    List<SyncfusionChartSeries> dataSets = [];
    String? error;

    // nb de données reçues (pour gérer le cas Tension qui renvoit Tesnsion sys et Tension dia
    List<String> fieldLibellesList = getPreviousDataResponseTable.seance!.values
        .toList()
        .first
        .keys
        .toList();
    int nbLineReceived = fieldLibellesList.length;

    for (int z = 0; z < nbLineReceived; z++) {
      // pour chaque courbe

      List<SyncfusionChartData> chartDataList = [];

      getPreviousDataResponseTable.seance!.forEach((timestamp, listObj) {
        // timestamp - convertir en DateTime
        int timestampInt = int.parse(timestamp);
        DateTime dateTime =
            DateTime.fromMillisecondsSinceEpoch(timestampInt * 1000);

        String valueStr = listObj[fieldLibellesList[z]] != null
            ? listObj[fieldLibellesList[z]]!.replaceAll(",", ".")
            : "";

        if (valueStr != "") {
          if (valueStr.contains(":")) {
            // si la valeur est une heure (format hh:mm:ss)

            List<String> list = valueStr.split(":");
            int h = int.parse(list[0]);
            int m = int.parse(list[1]);
            int s = int.parse(list[2]);
            Duration duration = Duration(hours: h, minutes: m, seconds: s);
            int seconds = duration.inSeconds;
            double yTimeValue =
                double.parse((seconds / 3600).toStringAsFixed(2));

            chartDataList.add(SyncfusionChartData(
              x: dateTime,
              y: yTimeValue,
              label: fieldLibellesList[z],
            ));
          } else {
            double valueDouble;
            try {
              valueDouble = double.parse(valueStr);
              valueDouble = double.parse(valueDouble.toStringAsFixed(1));
              chartDataList.add(SyncfusionChartData(
                x: dateTime,
                y: valueDouble,
                label: fieldLibellesList[z],
              ));
            } catch (e) {
              print("ERROR : $e");
              error = e.toString();
            }
          }
        }
      });

      // création d'une série, si des données ont été trouvées
      if (chartDataList.length > 0) {
        // Trier par date
        chartDataList.sort((a, b) => a.x.compareTo(b.x));

        SyncfusionChartSeries chartSeries = SyncfusionChartSeries(
          name: fieldLibellesList[z],
          data: chartDataList,
          colorIndex: z,
        );
        dataSets.add(chartSeries);
      }
    }

    return [error, dataSets];
  }
}
