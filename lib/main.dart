import 'package:flutter/material.dart';
import 'package:theradom/app.dart';
import 'package:theradom/blocs/authentication/AuthenticationBlocProvider.dart';
import 'package:theradom/blocs/login/LoginBlocProvider.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/SamplingTimerInfos.dart';
import 'package:theradom/models/UserAccountInfos.dart';
import 'package:theradom/utils/DefaultScreenController.dart';
import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/change_orientation.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseNotificationService.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:theradom/utils/Translations.dart';

void main() async {
  // chargement des preferences stockées sur disque avant de démarrer l'application
  WidgetsFlutterBinding.ensureInitialized();
  OrientationSetter.change.toPortrait();

  // initialisation des préférences utilisateur
  await SharedPreferencesUtil.instance.clearSecureStorageOnReinstall();
  Config.durationBeforeLogout =
      await SharedPreferencesUtil.instance.getInt(Config.prefDurBeforeLogOut) ??
          Config.durationBeforeLogout;
  Config.nomDomaine =
      await SharedPreferencesUtil.instance.getString(Config.prefDomainName) ??
          "";
  Config.firstUseState =
      await SharedPreferencesUtil.instance.getInt(Config.prefFirstUseState) ??
          0;
  Config.userAccountInfos = UserAccountInfos();
  Config.userAccountInfos?.login =
      await SharedPreferencesUtil.instance.getString(Config.prefUsername) ?? "";
  Config.userAccountInfos?.passwordHash =
      await SharedPreferencesUtil.instance.getString(Config.prefPassword) ?? "";
  Config.useBiometricID =
      await SharedPreferencesUtil.instance.getBool(Config.prefUseBiometricID);
  Config.doNotShowPrescriptionWarning = await SharedPreferencesUtil.instance
      .getBool(Config.prefDoNotShowPrescriptionWarning);
  Config.showUsabilityQuestionnaireCount = await SharedPreferencesUtil.instance
          .getInt(Config.prefShowUsabilityQuestionnaireCount) ??
      -5; // de base 5 connexion nécessaire
  String? samplingReminder = await SharedPreferencesUtil.instance
      .getString(Config.prefSamplingReminderValue);
  if (samplingReminder != null)
    Config.samplingTimerInfos = samplingTimerInfosFromJson(samplingReminder);

  FirebaseNotificationService
      .instance; // instanciation du service de management des notifications

  // a supprimer en prod
  //Config.firstUseState = 0;
  /*
  await SharedPreferencesUtil.setInt(Config.prefFirstUseState, 2);
  await SharedPreferencesUtil.setString(Config.prefDomainName, Config.nomDomaine);
   */

  //defaultScreen controller
  final _defaultScreenController = DefaultScreenController();
  _defaultScreenController.init();

  // Initialisation du module de traduction
  await allTranslations.init();

  //fontSize controller
  FontSizeController _fontSizeController = FontSizeController();

  _fontSizeController.init();

  await Firebase.initializeApp();

  // démarrage de l'application
  runApp(FontSizeControllerProvider(
    controller: _fontSizeController,
    child: DefaultScreenProvider(
        controller: _defaultScreenController,
        child:
            AuthenticationBlocProvider(child: LoginBlocProvider(child: App()))),
  ));
}
