import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/prescription/BlocPrescription.dart';
import 'package:theradom/blocs/prescription/StatePrescription.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/prescription/ViewPrescription.dart';
import 'package:theradom/widgets/background_header.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/custom_app_bar.dart';

class ViewPrescriptionGeneral extends StatefulWidget {
  final BlocPrescription blocPrescription;
  final String suivi;

  ViewPrescriptionGeneral(
      {required this.blocPrescription, required this.suivi});

  _ViewPrescriptionGeneral createState() => _ViewPrescriptionGeneral();
}

class _ViewPrescriptionGeneral extends State<ViewPrescriptionGeneral> {
  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
  }

  @override
  void dispose() {
    Config.blockBackBtnPop = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(backBtnEnabled: true),
      body: Background(
        child: Column(
          children: <Widget>[
            BackgroundHeader(
              label: allTranslations.text('prescription'),
            ),
            Expanded(
                child: BlocBuilder<BlocPrescription, StatePrescription>(
                    bloc: widget.blocPrescription,
                    builder: (context, state) {
                      Widget screen = Container();

                      if (state is StatePrescriptionUninitialized) {
                        screen = Center(
                          child:
                              PlatformCircularIndicator(darkIndicator: false),
                        );
                      }

                      if (state is StatePrescriptionScreen) {
                        final errorDetails = state.errorDetails;
                        if (errorDetails == null) {
                          if ((state.drugShortList.length == 0) &&
                              (state.prescriptionMap == {})) {
                            // pas de prescription
                            return _buildErrorMessage(
                                allTranslations.text("no_data_available"));
                          } else {
                            return ViewPrescription(
                                suivi: widget.suivi,
                                drugShortList: state.drugShortList,
                                prescriptionMap: state.prescriptionMap);
                          }
                        } else {
                          screen =
                              _buildErrorMessage(state.errorDetails!.message);
                        }
                      }
                      return screen;
                    }))
          ],
        ),
      ),
    );
  }

  Widget _buildErrorMessage(String message) {
    return Center(
      child: Text(
        message,
        style: TextStyle(
            color: AppTheme.backgroundColor,
            fontSize: FontSizeController.of(context).fontSize),
      ),
    );
  }
}
