import 'package:flutter/material.dart';
import 'package:theradom/models/drug_short.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:sticky_headers/sticky_headers.dart';

class ViewPrescription extends StatelessWidget {
  final String suivi;
  final List<DrugShort> drugShortList;
  final Map<String, dynamic> prescriptionMap;

  ViewPrescription({
    required this.suivi,
    required this.drugShortList,
    required this.prescriptionMap,
  });

  @override
  Widget build(BuildContext context) {
    return CustomScrollbar(
      scrollbarColor: AppTheme.primaryColor,
      child: ListView(children: _prescriptionElemsToSingeList(context)),
    );
  }

  List<Widget> _prescriptionElemsToSingeList(BuildContext context) {
    List<Widget> widgetList = [];
    int count = 0;

    // traitements
    if (drugShortList.length > 0)
      widgetList.add(StickyHeader(
        header: Container(
          color: AppTheme.secondaryColor,
          child: ListTile(
            title: Text(
              allTranslations
                  .text(drugShortList.length > 1
                      ? 'treatment_plural'
                      : 'treatment')
                  .toUpperCase(),
              style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600),
            ),
          ),
        ),
        content: Column(
          children: List.generate(drugShortList.length, (index) {
            count++;
            return Material(
              child: ListTile(
                  tileColor: count % 2 == 0
                      ? AppTheme.lightGrey
                      : AppTheme.backgroundColor,
                  title: Text(
                    drugShortList[index].posologie,
                    style: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize,
                        color: AppTheme.primaryColor),
                  ),
                  subtitle: MonitoringUtils.isMonitoringSession(suivi)
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(vertical: 10.0),
                              padding: EdgeInsets.only(
                                  left: 10.0,
                                  right: 10.0,
                                  top: 3.0,
                                  bottom: 3.0),
                              decoration: BoxDecoration(
                                  color: AppTheme.primaryColorDarker,
                                  borderRadius: BorderRadius.circular(20.0)),
                              child: Text(
                                MonitoringUtils.getNumTypeLabel(
                                    drugShortList[index].numType),
                                style: TextStyle(
                                    fontSize:
                                        FontSizeController.of(context).fontSize,
                                    color: AppTheme.backgroundColor,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ],
                        )
                      : null),
            );
          }),
        ),
      ));

    // pour le reste
    if (prescriptionMap.isNotEmpty) {
      // pour chaque zone
      prescriptionMap.forEach((areaName, json) {
        widgetList.add(StickyHeader(
            header: Container(
              color: AppTheme.secondaryColor,
              child: ListTile(
                title: Text(
                  areaName.toUpperCase(),
                  style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w600),
                ),
              ),
            ),
            content: Column(
              children: List.generate(json.length, (index) {
                count++;
                return Material(
                    child: ListTile(
                  tileColor: count % 2 == 0
                      ? AppTheme.backgroundColor
                      : AppTheme.lightGrey,
                  title: Text(
                    "${json.keys.toList()[index]}: ${json.values.toList()[index]}",
                    style: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize,
                        color: AppTheme.primaryColor),
                  ),
                ));
              }),
            )));
      });
    }

    return widgetList;
  }
}
