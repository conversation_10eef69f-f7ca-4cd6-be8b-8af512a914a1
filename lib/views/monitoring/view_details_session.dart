import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/monitoring_details/bloc_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/event_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/state_monitoring_details.dart';
import 'package:theradom/config.dart';

import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/models/session_sampling_structure.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/buttons/fab.dart';
import 'package:theradom/widgets/buttons/session_validation_button.dart';
import 'package:theradom/widgets/custom_scroll_behavior.dart';
import 'package:theradom/widgets/session_details/view_session_ending_time.dart';
import 'package:theradom/extensions/datetime_extension.dart';

class ViewDetailsSession extends StatefulWidget {
  final bool estTerminee;
  final Widget sectionListBody;
  final SessionSamplingStructure sessionSamplingStructureTab;
  final SessionSamplingStructure sessionSamplingStructureBox;
  final void Function() openPrescription;
  final TimeOfDay? heureFin;

  ViewDetailsSession(
      {required this.estTerminee,
      required this.sectionListBody,
      required this.sessionSamplingStructureTab,
      required this.sessionSamplingStructureBox,
      required this.openPrescription,
      required this.heureFin});

  _ViewDetailsSessionState createState() => _ViewDetailsSessionState();
}

GlobalKey viewDetailsSessionKey = GlobalKey();

class _ViewDetailsSessionState extends State<ViewDetailsSession> {
  late final bool _estTermine;
  late final SeancePrecedente _seance;
  Timer? _timer;
  late final bool _isSession;
  final _formKey = GlobalKey<FormState>();
  final ScrollController _scrollController = ScrollController();
  late TimeOfDay? _heureFinSeance;
  late DateTime _dateFinSeance;
  int _durationBeforeSessionSave = Config.durationBeforeSessionSave;

  late final BlocMonitoringDetails _blocMonitoringDetails;

  late final DateTime _dateLimiteFinSeance;

  @override
  void initState() {
    super.initState();
    _estTermine = widget.estTerminee;
    _blocMonitoringDetails = BlocProvider.of<BlocMonitoringDetails>(context);
    _isSession =
        MonitoringUtils.isMonitoringSession(_blocMonitoringDetails.suivi);
    _seance = _blocMonitoringDetails.seance;
    if ((!_seance.verrou) && (!_estTermine))
      _timer = Timer.periodic(Duration(seconds: _durationBeforeSessionSave),
          (timer) {
        _launchEventSaveSession(false, true);
      });
    _heureFinSeance = widget.heureFin;
    DateTime now = DateTime.now();
    _dateLimiteFinSeance = DateTime(now.year, now.month, now.day)
                .difference(_seance.dateSeance)
                .inHours >
            24
        ? DateTime(_seance.dateSeance.year, _seance.dateSeance.month,
            _seance.dateSeance.day + 1)
        : _seance.dateSeance;
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      _showOpeningDialogs();
    });
  }

  @override
  void dispose() {
    super.dispose();
    if (_timer != null) _timer!.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener(
        bloc: BlocProvider.of<BlocMonitoringDetails>(context),
        listener: (context, state) {
          if (state is StateMonitoringDetailsSaveSessionAfterSamplingEditing) {
            _launchEventSaveSession(false, false);
          }
        },
        child: Scaffold(
            key: viewDetailsSessionKey,
            backgroundColor: AppTheme.primaryColor,
            body: GestureDetector(
                onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
                child: Column(
                  children: [
                    Container(
                      color: AppTheme.backgroundColor,
                      padding: EdgeInsets.all(0),
                      width: double.maxFinite,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            allTranslations.text(_isSession
                                    ? 'session_from'
                                    : 'follow_up_from') +
                                " ${_seance.dateSeance.toLanguageString()}",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize:
                                    FontSizeController.of(context).fontSize,
                                color: AppTheme.primaryColor),
                            maxLines: 1,
                          ),
                          Text(
                            _estTermine
                                ? allTranslations.text(
                                    _isSession ? 'finished_f' : 'finished_m')
                                : allTranslations.text('in_progress_session'),
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize:
                                    FontSizeController.of(context).fontSize,
                                fontWeight: FontWeight.bold,
                                color: _estTermine
                                    ? AppTheme.primaryColor.withOpacity(0.3)
                                    : AppTheme.tertiaryColor),
                          )
                        ],
                      ),
                    ),
                    Expanded(
                      child: ScrollConfiguration(
                          behavior: CustomScrollBehavior(),
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            child: Form(
                                key: _formKey, child: widget.sectionListBody),
                          )),
                    )
                  ],
                )),
            floatingActionButton: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // btn prescription
                Align(
                  alignment: Alignment.bottomRight,
                  child: Fab(
                      svgPicture: SvgPicture.asset(
                          "assets/images/icons/icPrescrip.svg"),
                      onPressed: widget.openPrescription),
                ),

                SizedBox(height: 10),

                // btn validation séance
                _seance.verrou
                    ? Container()
                    : SessionValidationButton(
                        isSession: _isSession,
                        scrollController: _scrollController,
                        onSlideAction: () async => await _onValidateBtnSlided(),
                        onPressed: () => _onSaveBtnPressed())
              ],
            )));
  }

  Future<void> _onSaveBtnPressed() async {
    await _onValidateSession(false);
  }

  Future<void> _onValidateBtnSlided() async {
    await _onValidateSession(true);
  }

  Future<void> _onValidateSession(bool finSeance) async {
    // validation de la séance
    if (_formKey.currentState!.validate()) {
      // si le formulaire est valide
      if (finSeance) {
        _launchEventSaveAndCloseSession();
      } else {
        _launchEventSaveSession(finSeance, false);
      }
    } else {
      // si le formulaire n'est pas valide
      HapticFeedback.vibrate();
      bool? saveForm = await AlertBox(
              context: context,
              title: allTranslations.text('session_data_not_specified_title'),
              description:
                  allTranslations.text('session_data_not_specified_msg'),
              confirmBtnLabel: allTranslations.text('continue'))
          .showConfirmation();
      if (saveForm != null) {
        if (saveForm) {
          if (finSeance) {
            _launchEventSaveAndCloseSession();
          } else {
            _launchEventSaveSession(finSeance, false);
          }
        }
      }
    }
  }

  void _launchEventSaveSession(bool finSeance, bool autoSave) {
    // sauvegarde les données de la séance
    _blocMonitoringDetails.add(EventMonitoringDetailsValidateSession(
        autoSave: autoSave,
        finSeance: finSeance,
        heureFinSeance: finSeance ? _heureFinSeance : null,
        dateFinSeance: finSeance ? _dateFinSeance : null));
  }

  Future<void> _launchEventSaveAndCloseSession() async {
    // sélection de la date et de l'heure de fin de séance et appel de _launchEventSaveSession
    List? list = await showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: AppTheme.primaryColor,
        builder: (_) {
          return ViewSessionEndingTime(
              initialEndSessionTime: TimeOfDay(hour: 16, minute: 30),
              minimumDateTime: _seance.dateSeance,
              maximumDateTime: _dateLimiteFinSeance);
        });

    if (list != null) {
      _dateFinSeance = list[0];
      _heureFinSeance = list[1];
      _launchEventSaveSession(true, false);
    }
  }

  void _showOpeningDialogs() async {
    if (!Config.doNotShowPrescriptionWarning) {
      bool? rep = await AlertBox(
              context: context,
              title: allTranslations.text('about_your_prescription'),
              description: allTranslations.text('about_your_prescription_msg'),
              confirmBtnLabel: allTranslations.text('to_hide'))
          .showConfirmation();
      if (rep != null) {
        Config.doNotShowPrescriptionWarning = rep;
        SharedPreferencesUtil.instance
            .setBool(Config.prefDoNotShowPrescriptionWarning, rep);
      }
    }
  }
}
