import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:theradom/models/FieldDescription.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/models/session_sampling_data_element.dart';
import 'package:theradom/models/session_sampling_data_page.dart';
import 'package:theradom/models/session_sampling_data_to_save.dart';
import 'package:theradom/models/session_sampling_structure.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/background_header.dart';
import 'package:theradom/widgets/custom_app_bar.dart';
import 'package:theradom/widgets/custom_scroll_behavior.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/date_and_time_selector.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/widgets/buttons/large_button.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsFieldBuilder.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

/// Vue de la page d'échantillonage des valeurs
class ViewSamplingTable extends StatefulWidget {
  final String title;
  final SeancePrecedente seance;
  final bool locked;
  final SessionSamplingDataToSave? sessionSamplingDataToSave;
  final SessionSamplingStructure structure;
  final bool normalite;

  ViewSamplingTable(
      {required this.title,
      required this.seance,
      required this.locked,
      required this.sessionSamplingDataToSave,
      required this.structure,
      required this.normalite});

  _ViewSamplingTableState createState() => _ViewSamplingTableState();
}

class _ViewSamplingTableState extends State<ViewSamplingTable> {
  late SessionSamplingDataToSave _sessionSamplingDataToSave;
  late int _currentPage;
  late SessionSamplingDataPage? _displayedSessionSamplingDataPage;
  late int _totalPage;
  late final PageController _pageController;
  late final DateTime _maximumDateToPick;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    DateTime now = DateTime.now();
    _sessionSamplingDataToSave = widget.sessionSamplingDataToSave!;
    _totalPage = _sessionSamplingDataToSave.sessionSamplingDataPageList.length;
    _displayedSessionSamplingDataPage = _totalPage == 0
        ? null
        : _sessionSamplingDataToSave.sessionSamplingDataPageList.last;
    _maximumDateToPick =
        widget.seance.dateSeance == DateTime(now.year, now.month, now.day)
            ? widget.seance.dateSeance
            : DateTime(
                widget.seance.dateSeance.year,
                widget.seance.dateSeance.month,
                widget.seance.dateSeance.day + 1);
    _currentPage = _totalPage;
    _pageController = PageController(initialPage: _totalPage);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => widget.locked,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppBar(backBtnEnabled: true),
        body: Background(
          child: Column(
            children: [
              // header
              BackgroundHeader(label: widget.title),

              Padding(
                padding: EdgeInsets.only(left: 20.0, right: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // corbeille
                    widget.locked
                        ? Container()
                        : SvgButton(
                            asset: "assets/images/icons/icTrash.svg",
                            assetColor: AppTheme.tertiaryColor,
                            onTap: () => widget.locked
                                ? null
                                : _openDialogAndRemovePage(
                                    _displayedSessionSamplingDataPage!
                                        .numPage!)),

                    _displayedSessionSamplingDataPage != null
                        ? Container(
                            padding: EdgeInsets.only(
                                top: 10.0,
                                bottom: 10.0,
                                left: 20.0,
                                right: 20.0),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(30.0),
                                color: AppTheme.primaryColorDarker),
                            child: Text(
                              "${_displayedSessionSamplingDataPage!.date!.toLanguageStringShort()} "
                              "  "
                              "${_displayedSessionSamplingDataPage!.time!.toLanguageString()}",
                              style: TextStyle(
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                  color: AppTheme.backgroundColor,
                                  fontWeight: FontWeight.w600),
                            ),
                          )
                        : Container(),

                    widget.locked
                        ? Container()
                        : SvgButton(
                            asset: "assets/images/icons/icAddFile.svg",
                            assetColor: AppTheme.backgroundColor,
                            onTap: () => _selectDateAndTimeAndCreateNewPage())
                  ],
                ),
              ),

              // pages
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                  ),
                  child: CustomScrollbar(
                    scrollbarColor: AppTheme.primaryColor,
                    child: Container(
                        padding: EdgeInsets.all(10.0),
                        decoration: BoxDecoration(
                            color: AppTheme.backgroundColor,
                            borderRadius: BorderRadius.circular(20.0)),
                        child: _displayedSessionSamplingDataPage != null
                            ? ScrollConfiguration(
                                behavior: CustomScrollBehavior(),
                                child: Form(
                                    key: _formKey,
                                    child: PageView(
                                        controller: _pageController,
                                        onPageChanged: (page) {
                                          setState(() {
                                            _currentPage = page + 1;
                                            _displayedSessionSamplingDataPage =
                                                _sessionSamplingDataToSave
                                                        .sessionSamplingDataPageList[
                                                    page];
                                          });
                                        },
                                        children: _buildSamplingPageList())),
                              )
                            : Center(
                                child: Text(
                                  allTranslations.text('no_data'),
                                  style: TextStyle(
                                      fontSize: FontSizeController.of(context)
                                          .fontSize,
                                      color: AppTheme.primaryColor),
                                ),
                              )),
                  ),
                ),
              ),

              SizedBox(
                height: 10,
              ),

              Padding(
                padding: EdgeInsets.only(left: 20.0, right: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    SvgButton(
                        asset: "assets/images/icons/icBack.svg",
                        assetColor: _currentPage > 1
                            ? AppTheme.backgroundColor
                            : AppTheme.lightGrey,
                        onTap: () =>
                            _currentPage != 1 ? _goToPreviousPage() : null),
                    Text(
                      "$_currentPage/$_totalPage",
                      style: TextStyle(
                          fontSize: FontSizeController.of(context).fontSize,
                          color: AppTheme.backgroundColor),
                    ),
                    SvgButton(
                        asset: "assets/images/icons/icNext.svg",
                        assetColor: _currentPage == _totalPage
                            ? AppTheme.lightGrey
                            : AppTheme.backgroundColor,
                        onTap: () =>
                            _currentPage == _totalPage ? null : _goToNextPage())
                  ],
                ),
              ),

              SizedBox(
                height: 20,
              ),

              // boutons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                      child: LargeButton(
                          title: allTranslations.text('cancel'),
                          titleColor: AppTheme.backgroundColor,
                          backgroundColor: AppTheme.primaryColorDarker,
                          onTap: () => Navigator.of(context).pop())),
                  !widget.locked
                      ? Expanded(
                          child: LargeButton(
                              title: allTranslations.text('to_validate'),
                              titleColor: AppTheme.primaryColor,
                              backgroundColor: AppTheme.secondaryColor,
                              onTap: () async {
                                if (!_formKey.currentState!.validate()) {
                                  HapticFeedback.vibrate();
                                  bool? saveForm = await AlertBox(
                                          context: context,
                                          title: allTranslations.text(
                                              'session_data_not_specified_title'),
                                          description: allTranslations.text(
                                              'session_data_not_specified_msg'),
                                          confirmBtnLabel:
                                              allTranslations.text('continue'))
                                      .showConfirmation();
                                  if (saveForm != null && saveForm) {
                                    Navigator.of(context)
                                        .pop(_sessionSamplingDataToSave);
                                  }
                                } else {
                                  Navigator.of(context)
                                      .pop(_sessionSamplingDataToSave);
                                }
                              }),
                        )
                      : Container()
                ],
              ),

              SizedBox(
                height: 20,
              )
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildSamplingPageList() {
    return List.generate(
        _sessionSamplingDataToSave.sessionSamplingDataPageList.length, (index) {
      return _buildPageColumn(
          _sessionSamplingDataToSave.sessionSamplingDataPageList[index]);
    });
  }

  Widget _buildPageColumn(SessionSamplingDataPage samplingDataPage) {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 2.0),
        child: SingleChildScrollView(
          child: Column(
            children: List.generate(
                samplingDataPage.sessionSamplingDataElementList.length,
                (index) {
              return _buildSamplingWidget(
                  samplingDataPage.sessionSamplingDataElementList[index]);
            }),
          ),
        ));
  }

  Widget _buildSamplingWidget(
      SessionSamplingDataElement sessionSamplingDataElement) {
    if (widget.structure.fieldID[sessionSamplingDataElement.id] != null) {
      FieldDescription fieldDescription = FieldDescription.fromJson(
          widget.structure.fieldID[sessionSamplingDataElement.id]);
      return SessionMonitoringDetailsFieldBuilder(
          valeur: sessionSamplingDataElement.value,
          fieldDescription: fieldDescription,
          normalite: widget.normalite,
          locked: widget.locked,
          onValueSelected: _onFieldUpdate);
    } else {
      return Container();
    }
  }

  Future<void> _selectDateAndTimeAndCreateNewPage() async {
    List? dateAndTimeList = await _openDateAndTimeSelectionDialog();
    if (dateAndTimeList != null) {
      _createNewPage(dateAndTimeList[0], dateAndTimeList[1]);
    }
  }

  Future<List?> _openDateAndTimeSelectionDialog() async {
    // ouvre le dialogue de selection de date et heure et retourne les valeurrs choisies
    List? dateAndTimeList = await showDialog(
        context: context,
        barrierColor: AppTheme.primaryColor,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return DateAndTimeSelector(
              title: allTranslations.text('add_sampling_values_msg'),
              initialDate: widget.seance.dateSeance,
              initialTime: _displayedSessionSamplingDataPage == null
                  ? TimeOfDay.now()
                  : _displayedSessionSamplingDataPage!.modifTime!,
              minimumDate: widget.seance.dateSeance,
              maximumDate: _maximumDateToPick);
        });
    return dateAndTimeList;
  }

  void _createNewPage(DateTime date, TimeOfDay time) {
    // créé une nouvelle page
    _sessionSamplingDataToSave.addPage(date, time);
    setState(() {
      _totalPage++;
      _currentPage++;
      _displayedSessionSamplingDataPage =
          _sessionSamplingDataToSave.sessionSamplingDataPageList.last;
    });
    if (_totalPage > 1) _changeSamplingPageTo(_totalPage);
  }

  void _onFieldUpdate(String idBd, String libelle, dynamic value) {
    // sur modification d'un champ
    _sessionSamplingDataToSave.updatePage(
        _displayedSessionSamplingDataPage!.numPage!, idBd, value);
  }

  void _goToPreviousPage() {
    // modifie index quand on clique sur la back arrow
    int index =
        _indexOfSessionSamplingDataPage(_displayedSessionSamplingDataPage!);
    _changeSamplingPageTo(index - 1);
    _updateSelectedSessionSamplingDataPage(index - 1);
  }

  void _goToNextPage() {
    // modifie index quand on clique sur la forward arrow
    int index =
        _indexOfSessionSamplingDataPage(_displayedSessionSamplingDataPage!);
    _changeSamplingPageTo(index + 1);
    _updateSelectedSessionSamplingDataPage(index + 1);
  }

  int _indexOfSessionSamplingDataPage(
      SessionSamplingDataPage sessionSamplingDataPage) {
    int index = _sessionSamplingDataToSave.sessionSamplingDataPageList
        .indexWhere(
            (element) => element.numPage == sessionSamplingDataPage.numPage);
    return index;
  }

  void _changeSamplingPageTo(int index) {
    _pageController.animateToPage(index,
        duration: Duration(seconds: 1), curve: Curves.ease);
  }

  void _updateSelectedSessionSamplingDataPage(int index) {
    setState(() {
      _displayedSessionSamplingDataPage =
          _sessionSamplingDataToSave.sessionSamplingDataPageList[index];
    });
  }

  Future<void> _openDialogAndRemovePage(int numPage) async {
    bool remove = await _openRemovePageDialog();
    if (remove) _removePage(numPage);
  }

  Future<bool> _openRemovePageDialog() async {
    bool? remove = await AlertBox(
            context: context,
            title: allTranslations.text('delete_values'),
            description: allTranslations.text('delete_values_msg', {
              "date": _displayedSessionSamplingDataPage!.date!
                  .toLanguageStringShort(),
              "time":
                  _displayedSessionSamplingDataPage!.time!.toLanguageString()
            }),
            confirmBtnLabel: allTranslations.text('to_delete'))
        .showConfirmation(warning: true);
    return remove == null ? false : remove;
  }

  void _removePage(int numPage) {
    _sessionSamplingDataToSave.removePage(numPage);
    _totalPage--;
    _currentPage--;

    if (_totalPage == 0) {
      setState(() {
        _displayedSessionSamplingDataPage = null;
      });
    } else {
      if (_currentPage == 0) {
        if (_totalPage == 1) {
          setState(() {});
          _pageController.jumpToPage(1);
        } else {
          setState(() {
            _displayedSessionSamplingDataPage = _sessionSamplingDataToSave
                .sessionSamplingDataPageList[_currentPage];
          });
          _pageController.nextPage(
              duration: Duration(seconds: 1), curve: Curves.ease);
        }
      } else {
        _pageController.previousPage(
            duration: Duration(seconds: 1), curve: Curves.ease);
      }
    }
  }
}
