import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/documents/BlocDocuments.dart';
import 'package:theradom/blocs/documents/EventDocuments.dart';
import 'package:theradom/blocs/monitoring/BlocMonitoring1.dart';
import 'package:theradom/blocs/monitoring/StateMonitoring1.dart';
import 'package:theradom/blocs/prescription/EventPrescription.dart';
import 'package:theradom/blocs/statistics/BlocStatistics.dart';
import 'package:theradom/blocs/statistics/EventStatistics.dart';
import 'package:theradom/config.dart';
import 'package:theradom/blocs/monitoring/EventMonitoring1.dart';
import 'package:theradom/blocs/prescription/BlocPrescription.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/monitoring/ViewMonitoring2.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/error_text.dart';
import 'package:theradom/widgets/monitoring/monitoring_dropdown_button.dart';

/// Affichage TabBar des suivis et gestion de la sélection du suivi

class ViewMonitoringGeneral extends StatefulWidget {
  ViewMonitoringGeneral();

  @override
  _ViewMonitoringGeneralState createState() => _ViewMonitoringGeneralState();
}

class _ViewMonitoringGeneralState extends State<ViewMonitoringGeneral>
    with SingleTickerProviderStateMixin {
  late int _currentIndex;
  late String _suivi;
  late BlocMonitoring1 _blocMonitoring1 = BlocMonitoring1();
  late BlocPrescription _blocPrescription;
  late BlocDocuments _blocDocuments;
  late BlocStatistics _blocStatistics;

  @override
  void initState() {
    super.initState();
    if (Config.basicUserInfos!.monitoringCount > 0) {
      _currentIndex = 0;
      _suivi = Config.basicUserInfos!.monitorings.keys.toList()[_currentIndex];
      _launchEventMonitoringMainScreen();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget screen = Container();

    // listener et builder
    Widget builderWidget = BlocListener<BlocMonitoring1, StateMonitoring1>(
      bloc: _blocMonitoring1,
      listener: (context, state) {
        if (state is StateMonitoringLoading) {
          // loading apres validation des données saisie en séance
          if (state.loading) {
            AlertBox(
                    context: context,
                    title: allTranslations.text('please_wait'))
                .showLoading();
          } else {
            Navigator.of(context).pop();
          }
        }

        if (state is StateMonitoringNewSession) {
          if (state.error != null) {
            AlertBox(
                    context: context,
                    title: allTranslations.text('error'),
                    description: state.error)
                .showFlushbar(warning: true);
          }
        }

        if (state is StateMonitoringInitial) {
          _createMonitoringChildBlocs();
        }
      },
      child: BlocBuilder<BlocMonitoring1, StateMonitoring1>(
          bloc: _blocMonitoring1,
          buildWhen: (previousState, state) {
            bool condition = true;

            if (state is StateMonitoringLoading) {
              condition = false;
            }

            if (state is StateMonitoringNewSession) {
              condition = false;
            }

            return condition;
          },
          builder: (context, state) {
            if (state is StateMonitoringUninitialized) {
              return Center(
                  child: PlatformCircularIndicator(darkIndicator: false));
            }

            if (state is StateMonitoringInitial) {
              if (state.error != null) {
                screen = ErrorText(message: state.error!);
              } else {
                screen = MultiBlocProvider(
                  providers: [
                    BlocProvider<BlocPrescription>(
                        create: (context) => _blocPrescription),
                    BlocProvider<BlocDocuments>(
                        create: (context) => _blocDocuments),
                    BlocProvider<BlocStatistics>(
                        create: (context) => _blocStatistics)
                  ],
                  child: ViewMonitoring2(
                      blocMonitoring1: _blocMonitoring1,
                      monitoringType: _suivi,
                      sessionList: state.sessionListResponse),
                );
              }
            }

            return screen;
          }),
    );

    if (Config.basicUserInfos!.monitorings.keys.length > 1) {
      // plusieurs suivis

      screen = Column(
        children: [
          Container(
              color: Colors.transparent,
              padding: EdgeInsets.only(top: 20.0, left: 10.0, right: 10.0),
              height: 50,
              width: double.maxFinite,
              child: MonitoringDropdownButton(
                  monitoring: _suivi, onChanged: _onDropdownValueSelection)),
          Expanded(
            child: BlocProvider(
                create: (context) => _blocMonitoring1, child: builderWidget),
          )
        ],
      );
    } else {
      // un ou zéro suivi
      if (Config.basicUserInfos!.monitorings.keys.length > 0) {
        // il y a un suivi
        screen = BlocProvider(
            create: (context) => _blocMonitoring1, child: builderWidget);
      } else {
        // aucun suivi dispo
        screen = ErrorText(
          message: allTranslations.text("no_monitoring_available"),
        );
      }
    }
    return Background(child: screen);
  }

  void _onDropdownValueSelection(String suivi) {
    // sur sélection d'une tab

    if (_suivi != suivi) {
      _setMonitoring(suivi);
      _blocMonitoring1 = BlocMonitoring1();
      _launchEventMonitoringMainScreen();
    }
  }

  void _setMonitoring(String monitoring) {
    setState(() {
      _suivi = monitoring;
    });
  }

  void _launchEventMonitoringMainScreen() {
    _blocMonitoring1.add(EventMonitorigMainScreen(suivi: _suivi));
  }

  void _launchEventPrescription() {
    _blocPrescription.add(EventPrescriptionScreen());
  }

  void _launchEventGetDocumentsList() {
    _blocDocuments.add(EventDocumentsInitialDocumentList());
  }

  void _launchEventInitStats() {
    _blocStatistics.add(EventStatisticsInitialMainScreen());
  }

  void _createMonitoringChildBlocs() {
    _blocPrescription = BlocPrescription(suivi: _suivi);
    _launchEventPrescription();
    _blocDocuments = BlocDocuments(suivi: _suivi);
    _launchEventGetDocumentsList();
    _blocStatistics = BlocStatistics(suivi: _suivi);
    _launchEventInitStats();
  }
}
