import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/monitoring_details/bloc_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/state_monitoring_details.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/monitoring/view_details_session.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/custom_app_bar.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/error_text.dart';

class ViewMonitoringDetailsGeneral extends StatefulWidget {
  final String suivi;
  final void Function() onPrescriptionButtonTapped;

  ViewMonitoringDetailsGeneral(
      {required this.suivi, required this.onPrescriptionButtonTapped});

  @override
  _ViewMonitoringGeneralState createState() => _ViewMonitoringGeneralState();
}

class _ViewMonitoringGeneralState extends State<ViewMonitoringDetailsGeneral> {
  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = true;
  }

  @override
  void dispose() {
    super.dispose();
    Config.blockBackBtnPop = false;
  }

  @override
  Widget build(BuildContext context) {
    // listener et builder
    return BlocListener<BlocMonitoringDetails, StateMonitoringDetails>(
      bloc: BlocProvider.of<BlocMonitoringDetails>(context),
      listener: (context, state) {
        if (state is StateMonitoringDetailsSessionValidation) {
          // validation de la séance

          final errorDetailsData = state.errorDetailsData;
          final errorDrug = state.errorDetailsDrugStatus;

          if ((errorDetailsData == null) && (errorDrug == null)) {
            if (!state.autoSave) {
              if (!state.rebuild) {
                _showFlusbar(
                    true,
                    allTranslations.text(
                        MonitoringUtils.isMonitoringSession(widget.suivi)
                            ? 'session_saved'
                            : 'follow_up_saved'),
                    allTranslations.text('data_entered_has_been_saved'));
              } else {
                Navigator.of(context).pop();
              }
            }
          } else {
            if (!state.autoSave) {
              if (errorDetailsData != null) {
                _showFlusbar(false, allTranslations.text('error'),
                    errorDetailsData.message);
              }
              if (errorDrug != null) {
                _showFlusbar(
                    false, allTranslations.text('error'), errorDrug.message);
              }
            }
          }
        }

        if (state is StateMonitoringDetailsLoading) {
          // loading
          if (state.loading) {
            AlertBox(
                    context: context,
                    title: allTranslations.text('please_wait'))
                .showLoading();
          } else {
            Navigator.of(context).pop();
          }
        }

        if (state is StateMonitoringDetailsReloadFiles) {
          if (state.error == null) {
            _showFlusbar(
                true,
                "",
                allTranslations.text(state.fileAdded
                    ? 'attachment_saved_succes'
                    : 'attachment_deleted_succes'));
          } else {
            _showFlusbar(false, allTranslations.text('error'), state.error!);
          }
        }
      },
      child: BlocBuilder<BlocMonitoringDetails, StateMonitoringDetails>(
          buildWhen: (previousState, state) {
        bool condition = false;

        if (state is StateMonitoringDetailsSession) {
          condition = true;
        }

        return condition;
      }, builder: (context, state) {
        late final Widget screen;

        if (state is StateMonitoringDetailsUninitialized) {
          screen =
              Center(child: PlatformCircularIndicator(darkIndicator: false));
        }

        if (state is StateMonitoringDetailsSession) {
          final ErrorDetails? erroDetailsSessionData =
              state.errorDetailsSessionData;
          final ErrorDetails? errorDetailsDrugsReponse =
              state.errorDetailsDrugsResponse;

          if ((erroDetailsSessionData == null) &&
              (errorDetailsDrugsReponse == null)) {
            screen = ViewDetailsSession(
                estTerminee: state.estTerminee,
                sessionSamplingStructureTab: state.sessionSamplingStructureTab!,
                sessionSamplingStructureBox: state.sessionSamplingStructureBox!,
                sectionListBody: state.sectionsListBody,
                openPrescription: widget.onPrescriptionButtonTapped,
                heureFin: state.heureFinSeance);
          } else {
            String errorMessage = "";
            if (erroDetailsSessionData != null) {
              errorMessage =
                  errorMessage + erroDetailsSessionData.message + "\n";
            }
            if (errorDetailsDrugsReponse != null) {
              errorMessage =
                  errorMessage + errorDetailsDrugsReponse.message + "\n";
            }
            screen = ErrorText(message: errorMessage);
          }
        }

        return Scaffold(
            appBar: CustomAppBar(
              backBtnEnabled: true,
            ),
            body: Background(child: screen));
      }),
    );
  }

  void _showFlusbar(bool success, String title, String description) {
    AlertBox(context: context, title: title, description: description)
        .showFlushbar(warning: !success);
  }
}
