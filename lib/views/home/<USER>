import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/usability_questionnaire/bloc_usability_questionnaire.dart';
import 'package:theradom/blocs/usability_questionnaire/event_usability_questionnaire.dart';
import 'package:theradom/blocs/usability_questionnaire/state_usability_questionnaire.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/enum_age_range.dart';
import 'package:theradom/models/enum_genre.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/regexp_utils.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/custom_scroll_behavior.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/session_details/formfield_element.dart';

class QuestionnaireUsabilite extends StatefulWidget {
  QuestionnaireUsabilite();

  @override
  _QuestionnaireUsabiliteState createState() => _QuestionnaireUsabiliteState();
}

class _QuestionnaireUsabiliteState extends State<QuestionnaireUsabilite> {
  final _formKey = GlobalKey<FormState>();
  final int _formVersion = Config.evalFormVersion;

  late final BlocUsabilityQuestionnaire _blocUsabilityQuestionnaire;
  bool _saving = false;

  bool _showMessageFormNotCompleted = false;

  AgeRange? _ageRange;
  Genre? _genre;
  TextEditingController _textEditingControllerComputingSkills =
      TextEditingController();
  late final List<TextEditingController> _textEditingControllerQuestNoteList;
  late final List<TextEditingController> _textEditingControllerQuestCommentList;
  TextEditingController _textEditingControllerGeneralComment =
      TextEditingController();

  List<String> _questList = [];
  List<String> _commentList = [];

  @override
  void initState() {
    super.initState();
    _blocUsabilityQuestionnaire =
        BlocProvider.of<BlocUsabilityQuestionnaire>(context);
    for (int i = 1; i <= 9; i++) {
      _questList.add(allTranslations.text('quest-$i'));
    }
    for (int i = 1; i <= 1; i++) {
      _commentList.add(allTranslations.text('comment-$i'));
    }
    _textEditingControllerQuestNoteList =
        List.generate(_questList.length, (index) => TextEditingController());
    _textEditingControllerQuestCommentList =
        List.generate(_questList.length, (index) => TextEditingController());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener(
        bloc: _blocUsabilityQuestionnaire,
        listener: (context, state) {
          if (state is QuestionnaireSaving) {
            setState(() {
              _saving = state.loading;
            });
          }

          if (state is QuestionnaireSent) {
            if (state.success) {
              Navigator.of(context).pop();
            }
            AlertBox(
                    context: context,
                    title: state.success ? "" : allTranslations.text('error'),
                    description: state.message)
                .showFlushbar(warning: !state.success);
          }
        },
        child: GestureDetector(
            onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
            child: Scaffold(
                appBar: AppBar(
                    backgroundColor: AppTheme.backgroundColor,
                    elevation: 0,
                    leading: IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(Icons.close, color: AppTheme.primaryColor))),
                body: ScrollConfiguration(
                    behavior: CustomScrollBehavior(),
                    child: CustomScrollbar(
                        scrollbarColor: AppTheme.primaryColor,
                        child: SingleChildScrollView(
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 30.0,
                                ),

                                // titre
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Center(
                                    child: Text(
                                      allTranslations
                                          .text("usability_questionnaire"),
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          fontSize:
                                              FontSizeController.of(context)
                                                  .fontSize,
                                          color: AppTheme.primaryColor,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),

                                // message
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Text(
                                    allTranslations
                                        .text('usability_questionnaire_msg_1'),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize),
                                  ),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),

                                // INFO AGE
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 8.0, top: 8.0, right: 8.0),
                                  child: Card(
                                    color: AppTheme.secondaryColor
                                        .withOpacity(0.3),
                                    elevation: 0.0,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 12.0,
                                          right: 12.0,
                                          top: 12.0,
                                          bottom: 18.0),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: 10.0,
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 20.0),
                                            child: Text(
                                              allTranslations
                                                      .text('your_age_range') +
                                                  ":",
                                              style: TextStyle(
                                                  fontSize:
                                                      FontSizeController.of(
                                                              context)
                                                          .fontSize,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                          ),
                                          FormFieldElement(
                                            value: _ageRange,
                                            validator: (value) {
                                              if (_ageRange == null) {
                                                return allTranslations
                                                    .text('not_specified');
                                              }
                                              return null;
                                            },
                                            child: Column(
                                              children: List.generate(
                                                  AgeRange.values.length,
                                                  (index) {
                                                return RadioListTile<AgeRange>(
                                                  title: Text(
                                                    allTranslations.text(
                                                        AgeRange.values[index]
                                                            .valueToString()),
                                                    style: TextStyle(
                                                        fontSize:
                                                            FontSizeController
                                                                    .of(context)
                                                                .fontSize),
                                                  ),
                                                  value: AgeRange.values[index],
                                                  groupValue: _ageRange,
                                                  onChanged: (AgeRange? value) {
                                                    if (value != null) {
                                                      setState(() {
                                                        _ageRange = value;
                                                      });
                                                    }
                                                  },
                                                );
                                              }),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 20.0,
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                // INFO GENRE
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 8.0, top: 8.0, right: 8.0),
                                  child: Card(
                                    color: AppTheme.greyColor.withOpacity(0.1),
                                    elevation: 0.0,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 12.0,
                                          right: 12.0,
                                          top: 12.0,
                                          bottom: 18.0),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: 10.0,
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 20.0),
                                            child: Text(
                                              allTranslations.text('you_are') +
                                                  ":",
                                              style: TextStyle(
                                                  fontSize:
                                                      FontSizeController.of(
                                                              context)
                                                          .fontSize,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                          ),
                                          FormFieldElement(
                                            value: _genre,
                                            validator: (value) {
                                              if (_genre == null) {
                                                return allTranslations
                                                    .text('not_specified');
                                              }
                                              return null;
                                            },
                                            child: Column(
                                                children: List.generate(
                                                    Genre.values.length,
                                                    (index) {
                                              return RadioListTile<Genre>(
                                                title: Text(
                                                  allTranslations.text(Genre
                                                      .values[index]
                                                      .valueToString()),
                                                  style: TextStyle(
                                                      fontSize:
                                                          FontSizeController.of(
                                                                  context)
                                                              .fontSize),
                                                ),
                                                value: Genre.values[index],
                                                groupValue: _genre,
                                                onChanged: (Genre? value) {
                                                  if (value != null) {
                                                    setState(() {
                                                      _genre = value;
                                                    });
                                                  }
                                                },
                                              );
                                            })),
                                          ),
                                          SizedBox(
                                            height: 20.0,
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                // INFO A L'AISE AVEC L'INFORMATIQUE
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 8.0, top: 8.0, right: 8.0),
                                  child: Card(
                                    color: AppTheme.secondaryColor
                                        .withOpacity(0.3),
                                    elevation: 0.0,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 12.0,
                                          right: 12.0,
                                          top: 12.0,
                                          bottom: 18.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Flexible(
                                            child: Text(
                                              allTranslations.text(
                                                      'do_you_feel_comfortable_with_IT') +
                                                  "\n"
                                                      "0 - " +
                                                  allTranslations
                                                      .text('not_comfortable') +
                                                  "\n"
                                                      "10 - " +
                                                  allTranslations
                                                      .text('comfortable'),
                                              style: TextStyle(
                                                  fontSize:
                                                      FontSizeController.of(
                                                              context)
                                                          .fontSize,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                          ),
                                          Container(
                                            width: 80,
                                            child: TextFormField(
                                                onFieldSubmitted: (_) =>
                                                    _focusNextTextfield(),
                                                controller:
                                                    _textEditingControllerComputingSkills,
                                                keyboardType:
                                                    TextInputType.number,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .allow(
                                                          RegexpUtils.integer),
                                                ],
                                                decoration: InputDecoration(
                                                    border:
                                                        OutlineInputBorder(),
                                                    hintText: allTranslations
                                                        .text('note'),
                                                    labelText: allTranslations
                                                        .text('note'),
                                                    labelStyle: TextStyle(
                                                        fontSize:
                                                            FontSizeController
                                                                    .of(context)
                                                                .fontSize),
                                                    hintStyle: TextStyle(
                                                        fontSize:
                                                            FontSizeController
                                                                    .of(context)
                                                                .fontSize)),
                                                maxLines: 1,
                                                validator: _noteValidator),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),

                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Text(
                                    allTranslations
                                        .text('usability_questionnaire_msg_2'),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize),
                                  ),
                                ),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Text(
                                    "0 - " + allTranslations.text('not_agree'),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize),
                                  ),
                                ),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Text(
                                    "10 - " +
                                        allTranslations.text('fully_agree'),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize),
                                  ),
                                ),

                                SizedBox(
                                  height: 10.0,
                                ),

                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Text(
                                    allTranslations
                                        .text('usability_questionnaire_msg_3'),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize),
                                  ),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),

                                Column(
                                  children:
                                      List.generate(_questList.length, (index) {
                                    return _createQuestElem(
                                        _questList[index], index);
                                  }),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),

                                // comment
                                Column(
                                  children: List.generate(_commentList.length,
                                      (index) {
                                    return _createCommentElem(
                                        _commentList[index], index);
                                  }),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),

                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Text(
                                    allTranslations
                                        .text('thank_you_for_your_comeback'),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize),
                                  ),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),

                                _showMessageFormNotCompleted
                                    ? Center(
                                        child: Text(
                                        allTranslations
                                            .text('form_not_completed'),
                                        style: TextStyle(
                                            color: AppTheme.tertiaryColor,
                                            fontSize: 14),
                                      ))
                                    : Container(),

                                GestureDetector(
                                  onTap: () {
                                    if ((_formKey.currentState!.validate()) &&
                                        (!_saving)) {
                                      String device = (MediaQuery.of(context)
                                                  .size
                                                  .shortestSide <
                                              600)
                                          ? "mobile"
                                          : "tablette";
                                      String os =
                                          Platform.isIOS ? "iOS" : "Android";
                                      String csv1 = "ID,name,value,comments\n";
                                      String csv2 =
                                          "version,version formulaire,${_formVersion.toString()}\n";
                                      String csvOs = "OS, OS, $os\n";
                                      String csvDevice =
                                          "device, device, $device\n";
                                      String csvInfo1 =
                                          "info-1,${allTranslations.text('your_age_range')},${allTranslations.text(_ageRange!.valueToString())}\n";
                                      String csvInfo2 =
                                          "info-2,${allTranslations.text('you_are')},${allTranslations.text(_genre!.valueToString())}\n";
                                      String csvInfo3 =
                                          "info-3,${allTranslations.text('do_you_feel_comfortable_with_IT')},${_textEditingControllerComputingSkills.text}\n";
                                      String csvQuest = "";
                                      _questList
                                          .asMap()
                                          .forEach((index, quest) {
                                        csvQuest = csvQuest +
                                            "quest-${index + 1},${_questList[index].replaceAll("\n", "")},${_textEditingControllerQuestNoteList[index].text},${_textEditingControllerQuestCommentList[index].text.replaceAll(",", "").replaceAll(";", "")}\n";
                                      });
                                      String csvComment = "";
                                      _commentList
                                          .asMap()
                                          .forEach((index, quest) {
                                        csvQuest = csvQuest +
                                            "comment-${index + 1},${_commentList[index]}, ,${_textEditingControllerGeneralComment.text.replaceAll(",", "").replaceAll(";", "")}\n";
                                      });

                                      String res = csv1 +
                                          csv2 +
                                          csvOs +
                                          csvDevice +
                                          csvInfo1 +
                                          csvInfo2 +
                                          csvInfo3 +
                                          csvQuest +
                                          csvComment;

                                      _blocUsabilityQuestionnaire.add(
                                          SendCompletedQuestionnaire(
                                              csvContent: res,
                                              formVersion: _formVersion));
                                    } else {
                                      HapticFeedback.vibrate();
                                    }
                                  },
                                  child: Container(
                                    height: 64,
                                    margin: EdgeInsets.only(
                                        top: 10.0, left: 20.0, right: 20.0),
                                    decoration: BoxDecoration(
                                      color: _saving
                                          ? AppTheme.greyColor
                                          : AppTheme.secondaryColor,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(16.0)),
                                    ),
                                    child: Center(
                                        child: _saving
                                            ? PlatformCircularIndicator(
                                                darkIndicator: true)
                                            : Text(
                                                allTranslations.text('send'),
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyle(
                                                    color: _saving
                                                        ? AppTheme.greyColor
                                                        : AppTheme.primaryColor,
                                                    fontSize:
                                                        FontSizeController.of(
                                                                context)
                                                            .fontSize,
                                                    fontWeight:
                                                        FontWeight.w600),
                                              )),
                                  ),
                                ),

                                SizedBox(
                                  height: 20.0,
                                ),
                              ],
                            ),
                          ),
                        ))))));
  }

  Widget _createCommentElem(String title, int i) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, top: 8.0, right: 8.0),
      child: Card(
        color: i % 2 == 0
            ? AppTheme.secondaryColor.withOpacity(0.3)
            : AppTheme.greyColor.withOpacity(0.1),
        elevation: 0.0,
        child: Padding(
          padding: const EdgeInsets.only(
              left: 12.0, right: 12.0, top: 12.0, bottom: 18.0),
          child: Column(
            children: [
              SizedBox(
                height: 10.0,
              ),
              Text(
                title,
                style: TextStyle(
                    fontSize: FontSizeController.of(context).fontSize,
                    fontWeight: FontWeight.w600),
              ),
              SizedBox(
                height: 10.0,
              ),
              TextFormField(
                onFieldSubmitted: (_) => _focusNextTextfield(),
                controller: _textEditingControllerGeneralComment,
                keyboardType: TextInputType.multiline,
                decoration: InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: allTranslations.text('comments'),
                    labelText: allTranslations.text('comments'),
                    labelStyle: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize),
                    hintStyle: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize)),
                maxLines: 5,
              ),
              SizedBox(
                height: 20.0,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _createQuestElem(String title, int i) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, top: 8.0, right: 8.0),
      child: Card(
        color: i % 2 == 0
            ? AppTheme.greyColor.withOpacity(0.1)
            : AppTheme.secondaryColor.withOpacity(0.3),
        elevation: 0.0,
        child: Padding(
          padding: const EdgeInsets.only(
              left: 12.0, right: 12.0, top: 12.0, bottom: 18.0),
          child: Column(
            children: [
              SizedBox(
                height: 10.0,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      title,
                      style: TextStyle(
                          fontSize: FontSizeController.of(context).fontSize,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                  Container(
                    width: 80,
                    child: TextFormField(
                        onFieldSubmitted: (_) => _focusNextTextfield(),
                        controller: _textEditingControllerQuestNoteList[i],
                        keyboardType: TextInputType.number,
                        textInputAction: TextInputAction.next,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                              RegexpUtils.integer),
                        ],
                        decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            hintText: allTranslations.text('note'),
                            labelText: allTranslations.text('note'),
                            labelStyle: TextStyle(
                                fontSize:
                                    FontSizeController.of(context).fontSize),
                            hintStyle: TextStyle(
                                fontSize:
                                    FontSizeController.of(context).fontSize)),
                        maxLines: 1,
                        validator: _noteValidator),
                  ),
                ],
              ),
              SizedBox(
                height: 10.0,
              ),
              TextFormField(
                onFieldSubmitted: (_) => _focusNextTextfield(),
                controller: _textEditingControllerQuestCommentList[i],
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.multiline,
                decoration: InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: allTranslations.text('comments'),
                    labelText: allTranslations.text('comments'),
                    labelStyle: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize),
                    hintStyle: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize)),
                maxLines: 1,
              ),
              SizedBox(
                height: 20.0,
              )
            ],
          ),
        ),
      ),
    );
  }

  void _focusNextTextfield() {
    FocusScope.of(context).nextFocus();
  }

  String? _noteValidator(String? value) {
    if (value != null) {
      if (value.isEmpty) {
        _showFormNotCompletedMessage();
        return "";
      } else {
        int n = int.parse(value);
        if ((n < 0) || (n > 10)) {
          _showFormNotCompletedMessage();
          return "";
        }
      }
    }
    return null;
  }

  void _showFormNotCompletedMessage() {
    setState(() {
      _showMessageFormNotCompleted = true;
    });
  }
}
