import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:introduction_screen/introduction_screen.dart';

class OnBoardingHome extends StatelessWidget {
  OnBoardingHome();

  final _introKey = GlobalKey<IntroductionScreenState>();

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.light),
        body: SafeArea(
          child: IntroductionScreen(
            key: _introKey,
            globalBackgroundColor: AppTheme.primaryColor,
            freeze: false,
            pages: [
              // page présentation Théradom
              PageViewModel(
                titleWidget: Container(
                    padding: EdgeInsets.symmetric(vertical: 50.0),
                    child: Image(
                      image: AssetImage(
                          "assets/images/onboarding/theradom_uni.png"),
                    )),
                bodyWidget: Column(
                  children: [
                    Image(
                      image: AssetImage(
                          "assets/images/onboarding/imgOnboarding1.png"),
                    ),
                    SizedBox(height: 50),
                    Text(
                      allTranslations.text('onboarding_1_msg'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: AppTheme.backgroundColor, fontSize: 18.0),
                    )
                  ],
                ),
                decoration: PageDecoration(
                  contentMargin: EdgeInsets.symmetric(horizontal: 40.0),
                ),
              ),

              // page présentation toutes vos données
              PageViewModel(
                  titleWidget: _buildHeader(
                      allTranslations.text('onboarding_2_title'),
                      "assets/images/icons/icSuivi.svg"),
                  bodyWidget:
                      _buildBody(2, allTranslations.text('onboarding_2_msg'))),

              // page présentation calendrier
              PageViewModel(
                  titleWidget: _buildHeader(
                      allTranslations.text('onboarding_3_title'),
                      "assets/images/icons/icCalendar.svg"),
                  bodyWidget:
                      _buildBody(3, allTranslations.text('onboarding_3_msg'))),

              // page présentation messagerie
              PageViewModel(
                  titleWidget: _buildHeader(
                      allTranslations.text('onboarding_4_title'),
                      "assets/images/icons/icMessagerie.svg"),
                  bodyWidget:
                      _buildBody(4, allTranslations.text('onboarding_4_msg'))),

              // page présentation biologie
              PageViewModel(
                  titleWidget: _buildHeader(
                      allTranslations.text('onboarding_5_title'),
                      "assets/images/icons/icBio.svg"),
                  bodyWidget:
                      _buildBody(5, allTranslations.text('onboarding_5_msg'))),
            ],
            onDone: () => _onDone(context),
            showSkipButton: true,
            showNextButton: true,
            skipOrBackFlex: 0,
            nextFlex: 0,
            skipStyle: ButtonStyle(
              backgroundColor: MaterialStateProperty.resolveWith(
                  (_) => Theme.of(context).primaryColor),
            ),
            onSkip: () => _onDone(context),
            skip: Center(
              child: Text(
                allTranslations.text('skip'),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: AppTheme.backgroundColor,
                    fontWeight: FontWeight.w600,
                    fontSize: 18.0),
              ),
            ),
            next: Container(
                height: 50.0,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(16.0)),
                child: TextButton(
                  onPressed: () => _introKey.currentState!.next(),
                  child: Center(
                    child: Text(
                      allTranslations.text('next'),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 18.0),
                    ),
                  ),
                )),
            done: Container(
                height: 50.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.0),
                ),
                child: TextButton(
                  onPressed: () => _onDone(context),
                  child: Center(
                    child: Text(
                      allTranslations.text('next'),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 18.0),
                    ),
                  ),
                )),
            dotsDecorator: DotsDecorator(
              spacing: EdgeInsets.only(left: 5.0, right: 5.0),
              size: Size(5.0, 5.0),
              color: AppTheme.backgroundColor.withOpacity(0.2),
              activeColor: AppTheme.secondaryColor,
              activeSize: Size(5.0, 5.0),
              activeShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(25.0)),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(String title, String iconPath) {
    // build du header
    return Column(
      children: [
        SizedBox(
          height: 50.0,
        ),
        Container(
          padding: EdgeInsets.all(8.0),
          height: 50.0,
          width: 50.0,
          decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(16.0)),
          child: SvgPicture.asset(
            iconPath,
            colorFilter:
                ColorFilter.mode(AppTheme.primaryColor, BlendMode.srcIn),
          ),
        ),
        SizedBox(
          height: 10.0,
        ),
        Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
              color: AppTheme.backgroundColor,
              fontSize: 20,
              fontWeight: FontWeight.bold),
        )
      ],
    );
  }

  Widget _buildBody(int imgNumber, String content) {
    return Column(
      children: [
        Container(
            padding: EdgeInsets.all(50.0),
            child: Image(
              image: AssetImage(
                  "assets/images/onboarding/imgOnboarding$imgNumber.png"),
            )),
        Text(
          content,
          textAlign: TextAlign.center,
          style: TextStyle(color: AppTheme.backgroundColor, fontSize: 18.0),
        )
      ],
    );
  }

  void _onDone(BuildContext context) async {
    await SharedPreferencesUtil.instance.setInt(Config.prefFirstUseState, 2);
    Navigator.of(context).pop();
  }
}
