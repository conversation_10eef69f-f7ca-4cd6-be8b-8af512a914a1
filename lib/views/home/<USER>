import 'package:theradom/utils/expandable_bottom_bar_fixed/expandable_bottom_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/bilans_bio/bilans_bio_bloc_provider.dart';
import 'package:theradom/blocs/calendar/calendar_bloc_provider.dart';
import 'package:theradom/blocs/messaging/messaging_bloc_provider.dart';
import 'package:theradom/blocs/settings/SettingsBlocProvider.dart';
import 'package:theradom/blocs/usability_questionnaire/bloc_usability_questionnaire.dart';
import 'package:theradom/config.dart';
import 'package:theradom/services/snackbar_service.dart';
import 'package:theradom/utils/notifications/NotificationMailObservable.dart';

import 'package:theradom/models/NotificationResponse.dart';
import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/TOU.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/biometric.dart';
import 'package:theradom/utils/notifications/NotificationMailObservable.dart';
import 'package:theradom/utils/sampling_data_timer/sampling_timer_stream.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/calendar/view_calendar_general.dart';
import 'package:theradom/models/BasicUserInfos.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/home/<USER>';
import 'package:theradom/views/home/<USER>';
import 'package:theradom/views/messaging/view_messaging_general.dart';
import 'package:theradom/views/monitoring/ViewMonitoringGeneral.dart';
import 'package:theradom/views/settings/view_settings_main.dart';
import 'package:theradom/widgets/buttons/hidden_menu_button.dart';
import 'package:theradom/widgets/buttons/menu_fab.dart';
import 'package:theradom/widgets/buttons/visible_menu_button.dart';
import 'package:theradom/widgets/custom_app_bar.dart';
import 'package:theradom/views/bilans_bio/view_bilans_bio_general.dart';

/// Home de l'application avec la bottom app bar pour servir à la navigation

class Home extends StatefulWidget {
  static _HomeState? of(BuildContext context) =>
      context.findAncestorStateOfType<_HomeState>(); // nouveau

  final int realIndex;

  Home({required this.realIndex});

  @override
  _HomeState createState() => _HomeState();
}

GlobalKey homeGlobalKey = GlobalKey();

class _HomeState extends State<Home> with TickerProviderStateMixin {
  late final BottomBarController _bottomBarController;
  final BasicUserInfos basicUserInfos = Config.basicUserInfos!;
  late int
      _realIndex; // 0 Suivis, 1 Calendrier, 2 Messagerie, 3 Bio, 4 Parametres
  late List<Widget> _viewList;
  late final NotificationMailObservable _notificationMail;

  @override
  void initState() {
    super.initState();
    _bottomBarController =
        BottomBarController(vsync: this, dragLength: 550, snap: true);
    _realIndex = widget.realIndex;

    //notification new email
    _notificationMail = NotificationMailObservable();
    // Ajouter un listener pour détecter les nouveaux messages
    _notificationMail.addIncrementListener((newValue) {
      SnackBarService.showNotifSnackBar(context,
          message: allTranslations.text('telemonitoring_notif_msg'),
          title: allTranslations.text('telemonitoring_notif_title'));
    });

    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      _showDialogs();
    });

    // Listen specifically to increments of notificationMail
    _notificationMail.addIncrementListener((newValue) {
      print('Value increased to: $newValue');
    });
  }

  void _initViewList() {
    // initialisation des vues
    _viewList = [
      ViewMonitoringGeneral(),
      CalendarBlocProvider(
        child: ViewCalendarGeneral(
          updateNotificationBubble: _updateNbRDV,
        ),
      ),
      MessagingBlocProvider(
        child: ViewMessagingGeneral(
          updateNotificationBubble: _updateNbMessage,
        ),
      ),
      BilansBioBlocProvider(child: ViewBilansBioGeneral()),
      SettingsBlocProvider(child: ViewSettingsGeneral())
    ];
  }

  void _updateNbRDV() {
    // update les fakes notifs pour un rdv vu par le patient
    int nbRDV = Config.notificationResponse!.notifRdVnew;
    setState(() {
      Config.notificationResponse!.notifRdVnew = nbRDV - 1;
    });
  }

  void _updateNbMessage() {
    // update les fake notifs pour un message vu par le patient
    _notificationMail.value = _notificationMail.value - 1;
  }

  void updateFakeNotifications(NotificationResponse newFakeNotif) {
    // update le menu avec les fakes notifs
    setState(() {
      Config.notificationResponse = newFakeNotif;
      _notificationMail.value = newFakeNotif.notifMail;
    });
  }

  @override
  Widget build(BuildContext context) {
    _initViewList();

    return SafeArea(
        top: false,
        child: Scaffold(
          key: homeGlobalKey,
          resizeToAvoidBottomInset: false,
          appBar: CustomAppBar(backBtnEnabled: false, callback: _updateState),
          extendBody: true,
          body: AnimatedSwitcher(
            duration: Duration(milliseconds: 500),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(opacity: animation, child: child);
            },
            child: _viewList.elementAt(_realIndex),
          ),

          // FAB
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: MenuFAB(
              realIndex: _realIndex,
              bottomBarController: _bottomBarController,
              svgPicture: _getFabSvg()),

          // bottom navigation bar
          bottomNavigationBar: Container(
            color: Colors.transparent,
            child: BottomExpandableAppBar(
              controller: _bottomBarController,
              expandedHeight: 140,
              appBarHeight: 64,
              horizontalMargin: 0,
              shape: CircularNotchedRectangle(),
              expandedBackColor: Colors.transparent,
              expandedBody: Container(
                  padding: EdgeInsets.only(bottom: 30.0),
                  decoration: BoxDecoration(
                      color: AppTheme.backgroundColor,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(25))),
                  child: Container(
                    child: Center(
                        child: SingleChildScrollView(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // bouton messagerie
                          ValueListenableBuilder<int>(
                              valueListenable:
                                  _notificationMail.notifMailNotifier,
                              builder: (context, value, child) {
                                return HiddenMenuButton(
                                    title:
                                        allTranslations.text('txt_messaging'),
                                    nbNotif: value,
                                    iconPath:
                                        "assets/images/icons/icMessagerie.svg",
                                    selected: _realIndex == 2 ? true : false,
                                    onTap: () => _selectTab(2));
                              }),

                          // bouton bio
                          HiddenMenuButton(
                              title: allTranslations.text('txt_bio'),
                              nbNotif: 0,
                              iconPath: "assets/images/icons/icBio.svg",
                              selected: _realIndex == 3 ? true : false,
                              onTap: () => _selectTab(3)),
                        ],
                      ),
                    )),
                  )),
              bottomAppBarBody: BottomAppBar(
                elevation: 10,
                shape: CircularNotchedRectangle(),
                child: Container(
                  color: AppTheme.backgroundColor,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: <Widget>[
                      // bouton gauche
                      VisibleMenuButton(
                        title: basicUserInfos.monitoringCount == 1
                            ? basicUserInfos.monitorings.values.first
                            : allTranslations.text('txt_monitorings'),
                        iconPath: "assets/images/icons/icSuivi.svg",
                        selected: _realIndex == 0 ? true : false,
                        onTap: () => _selectTab(0),
                        nbNotif: 0,
                      ),

                      Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          SizedBox(width: 40), // icon
                        ],
                      ),

                      // bouton droite
                      VisibleMenuButton(
                        title: allTranslations.text('txt_calendar'),
                        iconPath: "assets/images/icons/icCalendar.svg",
                        selected: _realIndex == 1 ? true : false,
                        onTap: () => _selectTab(1),
                        nbNotif: Config.notificationResponse!.notifRdVnew,
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        ));
  }

  SvgPicture _getFabSvg() {
    SvgPicture svgPicture;
    switch (_realIndex) {
      case 2:
        {
          svgPicture = SvgPicture.asset("assets/images/icons/icMessagerie.svg",
              colorFilter: ColorFilter.mode(
                  Theme.of(context).canvasColor, BlendMode.srcIn));
        }
        break;
      case 3:
        {
          svgPicture = SvgPicture.asset("assets/images/icons/icBio.svg",
              colorFilter: ColorFilter.mode(
                  Theme.of(context).canvasColor, BlendMode.srcIn));
        }
        break;
      default:
        {
          svgPicture = SvgPicture.asset(
            "assets/images/icons/icMenu.svg",
          );
        }
        break;
    }
    return svgPicture;
  }

  void _selectTab(int realIndex) {
    setState(() {
      _realIndex = realIndex;
    });
    _bottomBarController.close(); // ferme bottom drawer
  }

  void _modifyPasswordDialog() {
    // montre un dialogue d'information
    AlertBox(
            context: context,
            title: allTranslations.text('changePassword'),
            description:
                allTranslations.text('change_password_in_app_parameters'))
        .showInfo();
  }

  Future<void> _showBiometricPopupActivation() async {
    bool? res = await AlertBox(
            context: context,
            title: allTranslations.text('biometrics'),
            description: allTranslations.text('use_biometrics_to_connect_msg'),
            confirmBtnLabel: allTranslations.text('continue'))
        .showConfirmation();

    if (res != null) {
      if (res) {
        bool save = false;
        try {
          save = await Biometric.authentication.start();
        } catch (e) {
          print(e);
        }
        if (save) {
          await SharedPreferencesUtil.instance.setString(
              Config.prefPassword, Config.userAccountInfos!.passwordHash);
          await SharedPreferencesUtil.instance
              .setString(Config.prefUsername, Config.userAccountInfos!.login);
          await SharedPreferencesUtil.instance
              .setBool(Config.prefUseBiometricID, save);
          Config.useBiometricID = true;
        }
      }
    } else {
      await SharedPreferencesUtil.instance
          .setBool(Config.prefUseBiometricID, false);
      Config.useBiometricID = false;
    }
    Config.firstUseState = 2;
    await SharedPreferencesUtil.instance.setInt(Config.prefFirstUseState, 2);
  }

  void _showDialogs() async {
    // montre les dialogues les uns à la suite des autres

    // on boarding
    if (Config.firstUseState! < 2) {
      await Navigator.push(
          context, CupertinoPageRoute(builder: (context) => OnBoardingHome()));
    }

    // modification de password
    if (basicUserInfos.changementMdp == "OK") _modifyPasswordDialog();

    // utilisation de la biométrie pour le connecter
    bool canUseBiometrics = await Biometric.authentication.canCheck();
    if ((canUseBiometrics) && (Config.firstUseState == 1))
      await _showBiometricPopupActivation();

    // accords cgu
    TOU().checkTOU(context,
        Config.firstUseState == 1 ? false : Config.basicUserInfos!.accordCgu);

    // rappel des valeur d'échantillonage à saisir
    if (Config.samplingTimerInfos != null &&
        Config.samplingTimerInfos!.numSeance != null)
      SamplingTimerStream.instance.start(Config.samplingTimerInfos!);

    // questionnaire usabilité
    if (Config.basicUserInfos!.lastEvaluation < Config.evalFormVersion) {
      if (Config.showUsabilityQuestionnaireCount! > 0 &&
          (Config.showUsabilityQuestionnaireCount!) % 3 == 0) {
        // multiple de 3
        await showDialog(
            barrierColor: AppTheme.backgroundColor,
            context: context,
            builder: (BuildContext context) {
              return BlocProvider(
                  create: (context) => BlocUsabilityQuestionnaire(),
                  child: QuestionnaireUsabilite());
            });
      }
      await SharedPreferencesUtil.instance.setInt(
          Config.prefShowUsabilityQuestionnaireCount,
          Config.showUsabilityQuestionnaireCount! + 1);
      Config.showUsabilityQuestionnaireCount =
          Config.showUsabilityQuestionnaireCount! + 1;
    } else {
      await SharedPreferencesUtil.instance
          .setInt(Config.prefShowUsabilityQuestionnaireCount, -5);
    }
  }

  void _updateState() {
    if (_realIndex == 0) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _notificationMail.removeIncrementListener();
    super.dispose();
  }
}
