import 'package:flutter/material.dart';
import 'package:theradom/models/message.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/blocs/messaging/bloc_messaging.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/config.dart';
import 'package:theradom/views/messaging/bring_to_front.dart';
import 'package:theradom/widgets/buttons/custom_close_button.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/widgets/messaging/ListePjMessage.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

/// Vue création d'un message
class ViewMessagingMessageDetails extends StatefulWidget {
  final BlocMessaging blocMessaging;
  final Message message;
  final int boite;
  final void Function(Message) onDelete;
  final void Function(Message) onShare;

  ViewMessagingMessageDetails(
      {required this.blocMessaging,
      required this.message,
      required this.boite,
      required this.onDelete,
      required this.onShare});

  _ViewMessagingMessageDetailsState createState() =>
      _ViewMessagingMessageDetailsState();
}

class _ViewMessagingMessageDetailsState
    extends State<ViewMessagingMessageDetails> {
  late final Message _message;
  late final int _boite;
  TextEditingController _textEditingControllerMotif = TextEditingController();
  TextEditingController _textEditingControllerContenu = TextEditingController();

  @override
  void initState() {
    super.initState();
    _message = widget.message;
    _boite = widget.boite;
    _textEditingControllerMotif.text = _message.motif;
    _textEditingControllerContenu.text = _message.zMessage;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: MediaQuery.of(context).size.height -
            Config.appBarHeight -
            (MediaQuery.of(context).size.shortestSide < 600 ? 25 : 10),
        decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(25.0))),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          resizeToAvoidBottomInset: false,
          body: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onVerticalDragDown: (DragDownDetails dragDownDetails) {
              // dismiss keyboard
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // barre d'options
                Container(
                    padding:
                        EdgeInsets.only(bottom: 15.0, left: 15.0, right: 15.0),
                    height: 63.5,
                    decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                width: 1, color: AppTheme.dividerColor))),
                    child: _buildActions()),

                Expanded(
                  child: CustomScrollbar(
                      scrollbarColor: AppTheme.backgroundColor,
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // pj
                            ListePjMessage(message: _message),

                            // expediteur et date
                            Container(
                                width: double.maxFinite,
                                padding:
                                    EdgeInsets.only(top: 15.0, bottom: 15.0),
                                decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            width: 1,
                                            color: AppTheme.dividerColor))),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: <Widget>[
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 20.0, right: 6.0),
                                      child: Text(
                                        allTranslations.text("from") + " :",
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            color: AppTheme.backgroundColor
                                                .withOpacity(0.5),
                                            fontSize:
                                                FontSizeController.of(context)
                                                    .fontSize),
                                        maxLines: 1,
                                      ),
                                    ),
                                    Flexible(
                                      child: Text(
                                        _message.emetteur,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: AppTheme.backgroundColor,
                                          fontSize:
                                              FontSizeController.of(context)
                                                  .fontSize,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        maxLines: 5,
                                      ),
                                    )
                                  ],
                                )),

                            // objet du message
                            Container(
                              width: double.maxFinite,
                              decoration: BoxDecoration(
                                  border: Border(
                                      bottom: BorderSide(
                                          width: 1,
                                          color: AppTheme.dividerColor))),
                              padding: EdgeInsets.only(
                                  left: 20.0,
                                  right: 20.0,
                                  top: 15.0,
                                  bottom: 15.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(right: 6.0),
                                    child: Text(
                                      allTranslations.text("mail_subject") +
                                          " : ",
                                      style: TextStyle(
                                          color: AppTheme.backgroundColor
                                              .withOpacity(0.5),
                                          fontFamily: "OpenSans",
                                          fontSize:
                                              FontSizeController.of(context)
                                                  .fontSize),
                                    ),
                                  ),
                                  Flexible(
                                    child: Text(
                                      _message.motif,
                                      maxLines: 5,
                                      textAlign: TextAlign.justify,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          fontSize:
                                              FontSizeController.of(context)
                                                  .fontSize,
                                          color: AppTheme.backgroundColor,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  )
                                ],
                              ),
                            ),

                            // contenu du message
                            // HMD renvoie les balises html dans le contenu du message
                            // Surcharges des styes pour les balises html
                            Container(
                                width: double.maxFinite,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20.0, vertical: 15.0),
                                child: Text(
                                  _message.zMessage,
                                  style: TextStyle(
                                      fontSize: FontSizeController.of(context)
                                          .fontSize,
                                      color: AppTheme.backgroundColor),
                                ))
                          ],
                        ),
                      )),
                )
              ],
            ),
          ),
        ));
  }

  Widget _buildActions() {
    //retourne les actions disponibles selon la boîte dans laquelle on est
    // 0 -> boite de reception
    // 1 -> boite d'envoi
    // 2 -> corbeille

    List<Widget> actions = [
      CustomCloseButton(
          hasBackgroundColor: true, onTap: () => Navigator.of(context).pop()),
      Spacer(),
      _boite == 0
          ? Container(
              padding: EdgeInsets.only(top: 5.0),
              height: 25,
              child: SvgButton(
                  asset: "assets/images/icons/icMailReply.svg",
                  onTap: _respondToMessage),
            )
          : Container(),
      SizedBox(width: 25),
      SvgButton(
          asset: "assets/images/icons/icShare.svg",
          assetColor: AppTheme.secondaryColor,
          onTap: () => widget.onShare(_message)),
      SizedBox(width: 30),
      _boite != 2
          ? SvgButton(
              asset: "assets/images/icons/icTrash.svg",
              assetColor: AppTheme.tertiaryColor,
              onTap: () => widget.onDelete(_message))
          : Container(),
      SizedBox(width: 10)
    ];

    return Row(
      children: actions,
    );
  }

  void _respondToMessage() {
    // ferme cette bottom sheet et en ouvre une nouvelle pour l'envoi d'un message
    Navigator.of(context).pop();

    showMaterialModalBottomSheet(
        expand: false,
        enableDrag: false,
        isDismissible: false,
        elevation: 0,
        duration: Duration(milliseconds: 500),
        context: context,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.transparent,
        builder: (context) => BringToFront(
            blocMessaging: widget.blocMessaging,
            emetteur: _message.emetteur,
            destinataire: _message.typeMessage,
            objet: _message.motif,
            contenu: _message.zMessage,
            date: _message.zDate));
  }
}
