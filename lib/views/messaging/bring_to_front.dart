import 'dart:async';
import 'package:flutter/material.dart';
import 'package:theradom/blocs/messaging/bloc_messaging.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/bring_to_front_boxdata.dart';
import 'package:theradom/utils/MessageDataUtils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/messaging/ViewMessagingCreateMessage.dart';
import 'package:theradom/widgets/CustomBottomSheet.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';

class BringToFront extends StatefulWidget {
  final BlocMessaging blocMessaging;
  final String? emetteur;
  final String? destinataire;
  final String? objet;
  final String? contenu;
  final String? date;

  BringToFront(
      {required this.blocMessaging,
      required this.emetteur,
      required this.objet,
      required this.contenu,
      required this.destinataire,
      required this.date})
      : super();

  @override
  State<BringToFront> createState() => _BringToFrontState();
}

class _BringToFrontState extends State<BringToFront> {
  bool _firstInit = true;
  bool _showShortcut = false;
  bool _showingDraft = false;
  List<Widget> _displayedWidgetList = [];
  List<BringToFrontBoxData> _boxDataList = [];

  String _draftId = "draft";
  String _messageId = "message";

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initDraftAndMessageElements(true);
    });
  }

  Future<String> _initDraftAndMessageElements(bool firstInit) async {
    _showShortcut = await MessageDataUtils.dataExists();
    _initBoxDataList(firstInit);
    _initDisplayedWidgetList(firstInit);
    return "OK";
  }

  void _initBoxDataList(bool firstInit) {
    if (firstInit) {
      _boxDataList = [
        BringToFrontBoxData(
            _draftId,
            ViewMessagingCreateMessage(
                isDraft: true,
                hasDraft: _showShortcut,
                blocMessaging: widget.blocMessaging,
                emetteur: null,
                destinataire: null,
                objet: null,
                contenu: null,
                date: null,
                onSaveDraftTap: null)),
        BringToFrontBoxData(
            _messageId,
            ViewMessagingCreateMessage(
                isDraft: false,
                hasDraft: _showShortcut,
                blocMessaging: widget.blocMessaging,
                emetteur: firstInit ? widget.emetteur : null,
                destinataire: firstInit ? widget.destinataire : null,
                objet: firstInit ? widget.objet : null,
                contenu: firstInit ? widget.contenu : null,
                date: firstInit ? widget.date : null,
                onSaveDraftTap: _onDraftSaved))
      ];
    } else {
      int index =
          _boxDataList.indexWhere((element) => element.id == _messageId);
      BringToFrontBoxData? messageBox =
          index != -1 ? _boxDataList[index] : null;

      _boxDataList.clear();

      _boxDataList.add(
        BringToFrontBoxData(
            _draftId,
            ViewMessagingCreateMessage(
                isDraft: true,
                hasDraft: _showShortcut,
                blocMessaging: widget.blocMessaging,
                emetteur: null,
                destinataire: null,
                objet: null,
                contenu: null,
                date: null,
                onSaveDraftTap: null)),
      );

      if (messageBox == null) {
        _boxDataList.add(BringToFrontBoxData(
            _messageId,
            ViewMessagingCreateMessage(
                isDraft: false,
                hasDraft: _showShortcut,
                blocMessaging: widget.blocMessaging,
                emetteur: firstInit ? widget.emetteur : null,
                destinataire: firstInit ? widget.destinataire : null,
                objet: firstInit ? widget.objet : null,
                contenu: firstInit ? widget.contenu : null,
                date: firstInit ? widget.date : null,
                onSaveDraftTap: _onDraftSaved)));
      } else {
        _boxDataList.add(messageBox);
      }
    }
  }

  void _initDisplayedWidgetList(bool firstInit) {
    _displayedWidgetList.clear();
    if (_showShortcut) {
      // un brouillon existe
      _boxDataList.forEach((boxData) {
        Key key = Key(boxData.id);
        _displayedWidgetList.add(Positioned(
          key: key,
          child: Container(
              key: key,
              child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  child: boxData.child,
                  onTap: () => _bringToFront(key))),
        ));
      });
    } else {
      // aucun brouillon
      Key key = Key(_boxDataList[1].id);
      _displayedWidgetList.add(Container(
        color: Colors.transparent,
        key: key,
        child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            child: _boxDataList[1].child,
            onTap: () => _bringToFront(key)),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: _initDraftAndMessageElements(_firstInit),
        builder: (_, AsyncSnapshot snapshot) {
          if (snapshot.hasData) {
            _firstInit = false;
            return Stack(
              children: [
                Container(
                  // cache le menu de sélection de la boîte derrière
                  decoration: BoxDecoration(
                      color: AppTheme.backgroundColor,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(25.0))),
                  height: MediaQuery.of(context).size.height -
                      Config.appBarHeight -
                      (MediaQuery.of(context).size.shortestSide < 600
                          ? 25
                          : 10),
                  child: Column(
                    children: [
                      _displayedWidgetList.length > 1
                          ? GestureDetector(
                              child: Container(
                                  height: 45,
                                  width: double.maxFinite,
                                  margin:
                                      EdgeInsets.only(left: 20.0, right: 20.0),
                                  decoration: BoxDecoration(
                                      color: _showingDraft
                                          ? AppTheme.primaryColor
                                          : AppTheme.primaryColorDarker,
                                      borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(25.0))),
                                  child: Center(
                                    child: Text(
                                      allTranslations
                                          .text(_showingDraft
                                              ? 'new_message'
                                              : 'draft')
                                          .toUpperCase(),
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: AppTheme.backgroundColor,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  )),
                              onTap: _onShortcutTap)
                          : Container(),
                      Expanded(
                        child: Container(
                            height: double.maxFinite,
                            decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(25.0))),
                            child: Container(
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(25.0))),
                              child: Stack(
                                  children: _displayedWidgetList +
                                      [
                                        _showShortcut
                                            ? Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  GestureDetector(
                                                    onTap: _onHeaderTap,
                                                    behavior: HitTestBehavior
                                                        .translucent,
                                                    child: Container(
                                                      margin: EdgeInsets.only(
                                                          top: 15.0),
                                                      height: 30,
                                                      width: 200,
                                                      decoration: BoxDecoration(
                                                          color: _showingDraft
                                                              ? AppTheme
                                                                  .primaryColorDarker
                                                              : _showShortcut
                                                                  ? AppTheme
                                                                      .primaryColor
                                                                  : Colors
                                                                      .transparent,
                                                          borderRadius:
                                                              BorderRadius.all(
                                                                  Radius
                                                                      .circular(
                                                                          50.0)),
                                                          border: Border.all(
                                                              color: _showingDraft
                                                                  ? AppTheme.backgroundColor
                                                                  : _showShortcut
                                                                      ? Colors.transparent
                                                                      : AppTheme.primaryColorDarker,
                                                              width: 1)),
                                                      child: Center(
                                                        child: Text(
                                                          allTranslations
                                                              .text(_showingDraft
                                                                  ? 'delete_draft'
                                                                  : 'new_message')
                                                              .toUpperCase(),
                                                          style: TextStyle(
                                                              color: AppTheme
                                                                  .backgroundColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                              fontSize: 14),
                                                        ),
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              )
                                            : Container()
                                      ]),
                            )),
                      )
                    ],
                  ),
                ),
              ],
            );
          } else {
            return Container(
              color: Theme.of(context).primaryColor,
              child: Center(
                  child: PlatformCircularIndicator(darkIndicator: false)),
            );
          }
        });
  }

  void _onShortcutTap() {
    _bringToFront(Key(_boxDataList[_showingDraft ? 1 : 0].id));
    setState(() {
      _showingDraft = !_showingDraft;
    });
  }

  void _bringToFront(Key key) {
    setState(() {
      for (var i = 0; i < _displayedWidgetList.length; i++) {
        Widget widget = _displayedWidgetList[i];
        if (key == widget.key) {
          _displayedWidgetList.remove(widget);
          _displayedWidgetList.add(widget);
          break;
        }
      }
    });
  }

  Future<void> _showDeleteSheet() async {
    int? res = await CustomBottomSheet(
        context: context,
        labels: [allTranslations.text('delete_draft')]).show();
    if (res != null) {
      _onDraftDeleted();
    }
  }

  Future<void> _onDraftDeleted() async {
    // supprime le draft et rebuild
    await MessageDataUtils.deleteMessageData();
    _displayedWidgetList.removeAt(1);
    _boxDataList.removeWhere((element) => element.id == _messageId);
    setState(() {
      _showShortcut = false;
      _showingDraft = false;
    });
  }

  Future<void> _onDraftSaved(
      String destinataireAffichage, destinataireMessage, objet, contenu) async {
    // sauvegarde le draft et rebuild avec les nouveaux éléments
    await MessageDataUtils.saveMessageData(
        destinataireAffichage, destinataireMessage, objet, contenu);
    _initDraftAndMessageElements(false);
    setState(() {
      _showingDraft = false;
    });
  }

  Future<void> _onHeaderTap() async {
    if (_showShortcut) {
      if (_showingDraft) {
        // delete
        await _showDeleteSheet();
      }
    }
  }
}
