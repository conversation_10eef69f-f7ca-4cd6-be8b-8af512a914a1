import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/messaging/bloc_messaging.dart';
import 'package:theradom/blocs/messaging/event_messaging.dart';
import 'package:theradom/blocs/messaging/state_messaging.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/utils/MessageDataUtils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/messaging/view_messaging_list_messages.dart';
import 'package:theradom/views/messaging/bring_to_front.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/buttons/fab.dart';
import 'package:theradom/widgets/tab_item.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

/// Vue AppBar, selection des onglets, et BlocBuilder et BlocListener pour BlocMessaging

class ViewMessagingGeneral extends StatefulWidget {
  final void Function() updateNotificationBubble; // update la bulle du menu

  ViewMessagingGeneral({required this.updateNotificationBubble});

  @override
  _ViewMessagingGeneralState createState() => _ViewMessagingGeneralState();
}

class _ViewMessagingGeneralState extends State<ViewMessagingGeneral>
    with SingleTickerProviderStateMixin {
  late final void Function() _updateNotificationBubble;
  late final BlocMessaging _blocMessaging;
  final AutoSizeGroup _myGroup = AutoSizeGroup();
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _updateNotificationBubble = widget.updateNotificationBubble;
    _blocMessaging = BlocProvider.of<BlocMessaging>(context);
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Background(
        child: DefaultTabController(
      length: 3,
      initialIndex: 0,
      child: Scaffold(
          appBar: PreferredSize(
            preferredSize: Size.fromHeight(60),
            child: Container(
              padding: EdgeInsets.all(10.0),
              decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(25.0))),
              child: SafeArea(
                child: TabBar(
                    controller: _tabController,
                    indicator: BoxDecoration(
                        borderRadius: BorderRadius.circular(25.0),
                        color: AppTheme.secondaryColor),
                    labelColor: AppTheme.primaryColor,
                    unselectedLabelColor: AppTheme.backgroundColor,
                    indicatorSize: TabBarIndicatorSize.tab,
                    onTap: _selectTab,
                    tabs: [
                      TabItem(
                          title: allTranslations.text("mailbox_received"),
                          group: _myGroup),
                      TabItem(
                          title: allTranslations.text("mailbox_sent"),
                          group: _myGroup),
                      TabItem(
                          title: allTranslations.text("mailbox_deleted"),
                          group: _myGroup),
                    ]),
              ),
            ),
          ),
          body: BlocListener<BlocMessaging, StateMessaging>(
            bloc: _blocMessaging,
            listener: (context, state) async {
              if (state is StateMessagingMessageSentSuccess) {
                if (state.isDraft) await MessageDataUtils.deleteMessageData();
                _onMessageSent(state.errorDetailsMessage);
              }

              if (state is StateMessagingMessageDeleted) {
                _showFlushbar(state.success, "", state.message);
              }

              if (state is StateMessagingSendMessageLoading) {
                if (state.loading) {
                  AlertBox(
                          context: context,
                          title: allTranslations.text('please_wait'))
                      .showLoading();
                } else {
                  Navigator.of(context).pop();
                }
              }
            },
            child: BlocBuilder<BlocMessaging, StateMessaging>(
              buildWhen: (previousState, state) {
                bool condition;

                if ((state is StateMessagingAcknowledgeMessage) ||
                    (state is StateMessagingMessageDeleted) ||
                    (state is StateMessagingMessageSentSuccess) ||
                    (state is StateMessagingSendMessageLoading) ||
                    (state is StateMessagingUpdatedMailbox)) {
                  condition = false;
                } else {
                  condition = true;
                }

                return condition;
              },
              builder: (context, state) {
                if (state is StateMessagingUninitialized) {
                  // non-initialisé, indicateur de chargement
                  return Container(
                      color: AppTheme.primaryColor,
                      child: PlatformCircularIndicator(darkIndicator: false));
                }

                if (state is StateMessagingMailbox) {
                  // affichage de la boite de reception
                  if (state.errorDetails == null) {
                    final messages = state.receivedMessages!.messages;
                    return ViewMessagingListMessages(
                        updateNotificationBubble: _updateNotificationBubble,
                        messages: messages,
                        totalAvailable: state.totalAvailable!,
                        boite: 0);
                  } else {
                    return _buildErrorWidget(state.errorDetails!);
                  }
                }

                if (state is StateMessagingSentMessages) {
                  // affichage des messages envoyés
                  if (state.errorDetails == null) {
                    final sentMessages = state.sentMessages!.messages;
                    return ViewMessagingListMessages(
                        updateNotificationBubble: _updateNotificationBubble,
                        messages: sentMessages,
                        totalAvailable: state.totalAvailable!,
                        boite: 1);
                  } else {
                    return _buildErrorWidget(state.errorDetails!);
                  }
                }

                if (state is StateMessagingTrash) {
                  // affichage de la boite de reception
                  if (state.errorDetails == null) {
                    final deletedMessages = state.deletedMessages!.messages;
                    return ViewMessagingListMessages(
                        updateNotificationBubble: _updateNotificationBubble,
                        messages: deletedMessages,
                        totalAvailable: state.totalAvailable!,
                        boite: 2);
                  } else {
                    return _buildErrorWidget(state.errorDetails!);
                  }
                } else {
                  return Container();
                }
              },
            ),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
          floatingActionButton: Padding(
            padding: EdgeInsets.only(bottom: 80.0),
            child: Fab(
                svgPicture:
                    SvgPicture.asset("assets/images/icons/icNewMail.svg"),
                onPressed: _createNewMessage),
          )),
    ));
  }

  Widget _buildErrorWidget(ErrorDetails errorDetails) {
    return Container(
      color: AppTheme.primaryColor,
      child: Center(
        child: Text(
          errorDetails.message,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
              fontSize: FontSizeController.of(context).fontSize,
              color: AppTheme.backgroundColor),
        ),
      ),
    );
  }

  void _selectTab(int index) {
    // lancement de l'event selon la boîte sélectionnée
    switch (index) {
      case 0:
        {
          // messages reçus
          _blocMessaging.add(EventMessagingMailbox());
        }
        break;
      case 1:
        {
          // messages envoyés
          _blocMessaging.add(EventMessagingSentMessages());
        }
        break;
      case 2:
        {
          // messages supprimés
          _blocMessaging.add(EventMessagingTrash());
        }
        break;
    }
  }

  void _onMessageSent(ErrorDetails? errorDetails) {
    // affichage dans la snackbar du résultat de l'envoi du message
    if (errorDetails == null) {
      // pas d'erreur
      Navigator.of(context).pop();
      _showFlushbar(true, "", allTranslations.text("msg_sent_success"));
      if (_tabController.index == 1) {
        _blocMessaging.add(EventMessagingSentMessages());
      }
    } else {
      _showFlushbar(false, allTranslations.text('error'), errorDetails.message);
    }
  }

  void _showFlushbar(bool isSuccess, String title, String description) {
    AlertBox(
      context: context,
      title: title,
      description: description,
    ).showFlushbar(warning: !isSuccess);
  }

  void _createNewMessage() {
    // Ouvre l'iterface de saisie d'un nouveau message
    showMaterialModalBottomSheet(
        expand: false,
        enableDrag: false,
        isDismissible: false,
        elevation: 0,
        duration: Duration(milliseconds: 500),
        context: context,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.transparent,
        builder: (_) => BringToFront(
            blocMessaging: _blocMessaging,
            emetteur: null,
            destinataire: null,
            objet: null,
            contenu: null,
            date: null));
  }
}
