import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/SavedMessageData.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/MessageDataUtils.dart';
import 'package:theradom/utils/MultiImagePickerUtils.dart';
import 'package:theradom/utils/RecordAudioUtils.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/blocs/messaging/bloc_messaging.dart';
import 'package:theradom/blocs/messaging/event_messaging.dart';
import 'package:theradom/models/BasicUserInfos.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/CustomBottomSheet.dart';
import 'package:theradom/widgets/ThumbnailWidget.dart';

import 'package:theradom/widgets/icons/chev_icon.dart';
import 'package:theradom/widgets/buttons/custom_close_button.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/extensions/string_extension.dart';

/// Vue création d'un message
class ViewMessagingCreateMessage extends StatefulWidget {
  final bool isDraft;
  final bool hasDraft;
  final BlocMessaging blocMessaging;
  final String? emetteur;
  final String? destinataire;
  final String? objet;
  final String? contenu;
  final String? date;
  final Future<void> Function(String destAffichage, String destMessage,
      String objet, String contenu)? onSaveDraftTap;

  ViewMessagingCreateMessage(
      {required this.isDraft,
      required this.hasDraft,
      required this.blocMessaging,
      required this.emetteur,
      required this.destinataire,
      required this.objet,
      required this.contenu,
      required this.date,
      required this.onSaveDraftTap});

  ViewMessagingCreateMessageState createState() =>
      ViewMessagingCreateMessageState();
}

class ViewMessagingCreateMessageState
    extends State<ViewMessagingCreateMessage> {
  BasicUserInfos _basicUserInfos = Config.basicUserInfos!;
  late final String _emetteur;
  late String? _destinataire;
  late String? _objet;
  late String? _contenu;
  late String? _date;

  late final BlocMessaging _blocMessaging;
  late String _destinataireMessage;

  final _objetController = TextEditingController();
  final _contenuController = TextEditingController();

  List<String> _listMonitoringIDs = [];
  List<String> _listMonitoringIDsString = [];
  late String _destinataireParDefaut;

  late bool _hasDraft;

  List<ThumbnailWidget> _listWidgetsPJ = [];

  List<File?> _fileList = []; // pour les images
  File? _audioFile; // pour le fichier son

  int _nbPjMaxToSelect = Config.pjMaxNumber;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _hasDraft = widget.hasDraft;
    _emetteur = widget.emetteur ?? "";
    _destinataire = widget.destinataire;
    _date = widget.date;
    _objet = widget.objet != null ? "RE: ${widget.objet}" : "";
    _objetController.text = _objet ?? "";
    _contenu = widget.contenu != null
        ? "\n\n---\n$_emetteur\n${allTranslations.text("message_date")}: ${_date?.formatYYYYMMDD2Language()}\n\n${widget.contenu}"
        : "";
    _contenuController.text = _contenu ?? "";
    _contenuController.selection =
        TextSelection.fromPosition(TextPosition(offset: 0));
    _blocMessaging = widget.blocMessaging;
    _listMonitoringIDs = _basicUserInfos.monitorings.keys.toList();
    _listMonitoringIDsString = _basicUserInfos.monitorings.values.toList();
    _destinataireParDefaut = _listMonitoringIDsString[0];
    _destinataireMessage = "TLS_" + _listMonitoringIDs[0];
    if (widget.isDraft) _onChargeMessageData().then((value) {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: MediaQuery.of(context).size.height -
            Config.appBarHeight -
            (MediaQuery.of(context).size.shortestSide < 600 ? 25 : 10),
        decoration: BoxDecoration(
            color: widget.isDraft
                ? AppTheme.primaryColorDarker
                : AppTheme.primaryColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(25.0))),
        child: Scaffold(
            backgroundColor: Colors.transparent,
            resizeToAvoidBottomInset: true,
            body: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onVerticalDragDown: (DragDownDetails dragDownDetails) {
                // dismiss keyboard
                FocusScope.of(context).requestFocus(FocusNode());
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // barre d'options
                  Container(
                    padding: EdgeInsets.only(
                        top: 5.0, bottom: 10.0, left: 15.0, right: 15.0),
                    height: 64,
                    decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                width: _listWidgetsPJ.length == 0 ? 2 : 1,
                                color: AppTheme.dividerColor))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomCloseButton(
                            hasBackgroundColor: true,
                            onTap: () => _showCloseSheet()),
                        Spacer(),
                        GestureDetector(
                          onTap: () async {
                            if (!widget.isDraft) {
                              await _saveDraftAction();
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.all(10),
                            padding: EdgeInsets.only(left: 10.0, right: 10.0),
                            decoration: BoxDecoration(
                              color: widget.isDraft
                                  ? Colors.transparent
                                  : _hasDraft
                                      ? Colors.transparent
                                      : AppTheme.primaryColorDarker,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(50.0)),
                            ),
                            child: Center(
                              child: Text(
                                allTranslations.text('draft').toUpperCase(),
                                style: TextStyle(
                                    color: AppTheme.backgroundColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14),
                              ),
                            ),
                          ),
                        ),
                        Spacer(),
                        SvgButton(
                            asset: "assets/images/icons/icPaperclip.svg",
                            assetColor:
                                Theme.of(context).colorScheme.background,
                            onTap: _addAttachments)
                      ],
                    ),
                  ),

                  Expanded(
                      child: ListView(shrinkWrap: true, children: [
                    Container(
                      height: _listWidgetsPJ.length == 0 ? 0 : 90,
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1, color: AppTheme.primaryColor))),
                      padding: EdgeInsets.all(5),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: _listWidgetsPJ),
                      ),
                    ),

                    // dropdown button pour la sélection du destinataire
                    Container(
                        padding: EdgeInsets.only(bottom: 5.0),
                        decoration: BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    width: 1, color: AppTheme.dividerColor))),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Padding(
                              padding: EdgeInsets.only(left: 20.0, right: 10.0),
                              child: Text(
                                allTranslations.text("msg_to"),
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: AppTheme.backgroundColor
                                        .withOpacity(0.5),
                                    fontSize: FontSizeController.of(context)
                                        .fontSize),
                                maxLines: 1,
                              ),
                            ),
                            Expanded(
                              child: IgnorePointer(
                                  ignoring:
                                      _listMonitoringIDsString.length <= 1,
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<String>(
                                        value: _destinataire == null
                                            ? _destinataireParDefaut
                                            : _destinataire =
                                                _destinataireParDefaut,
                                        icon: Padding(
                                            padding: EdgeInsets.only(
                                              right: 1.0,
                                            ),
                                            child: ChevIcon(
                                              assetPath:
                                                  "assets/images/icons/btChevDw.svg",
                                            )),
                                        dropdownColor: widget.isDraft
                                            ? AppTheme.primaryColorDarker
                                            : AppTheme.primaryColor,
                                        iconDisabledColor:
                                            AppTheme.disabledColor,
                                        iconEnabledColor: AppTheme.primaryColor,
                                        onChanged: (String? newValue) =>
                                            _changeDestinataire(newValue),
                                        items: _date == null
                                            ? _listMonitoringIDsString
                                                .map<DropdownMenuItem<String>>(
                                                    (String value) {
                                                return DropdownMenuItem<String>(
                                                  value: value,
                                                  child: Text(value,
                                                      style: TextStyle(
                                                          color: AppTheme
                                                              .backgroundColor,
                                                          fontSize:
                                                              FontSizeController
                                                                      .of(context)
                                                                  .fontSize),
                                                      maxLines: 1),
                                                );
                                              }).toList()
                                            : [
                                                DropdownMenuItem(
                                                  value: _destinataireParDefaut,
                                                  child: Text(
                                                      _destinataireParDefaut,
                                                      style: TextStyle(
                                                          fontSize:
                                                              FontSizeController
                                                                      .of(
                                                                          context)
                                                                  .fontSize,
                                                          color: AppTheme
                                                              .backgroundColor),
                                                      maxLines: 1),
                                                )
                                              ]),
                                  )),
                            )
                          ],
                        )),

                    // objet du message
                    Container(
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1, color: AppTheme.dividerColor))),
                      padding: EdgeInsets.only(
                          left: 20.0, right: 20.0, top: 5.0, bottom: 5.0),
                      child: Row(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 0.0),
                            child: Text(
                              allTranslations.text("mail_subject") + " : ",
                              style: TextStyle(
                                  color:
                                      AppTheme.backgroundColor.withOpacity(0.5),
                                  fontSize:
                                      FontSizeController.of(context).fontSize),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(left: 8.0),
                              child: TextField(
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp('[^{}]'))
                                ],
                                cursorColor: AppTheme.backgroundColor,
                                style: TextStyle(
                                  color: AppTheme.backgroundColor,
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                ),
                                keyboardType: TextInputType.text,
                                controller: _objetController,
                                obscureText: false,
                                autocorrect: true,
                                enabled: _date == null ? true : false,
                                maxLines: 1,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),

                    // contenu du message
                    Container(
                        child: CustomScrollbar(
                      scrollbarColor: AppTheme.backgroundColor,
                      child: Padding(
                        padding:
                            EdgeInsets.only(top: 5.0, left: 20.0, right: 20.0),
                        child: TextField(
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp('[^{}]'))
                          ],
                          cursorColor: AppTheme.backgroundColor,
                          style: TextStyle(
                              fontSize: FontSizeController.of(context).fontSize,
                              color: AppTheme.backgroundColor,
                              fontFamily: "OpenSans"),
                          keyboardType: TextInputType.multiline,
                          minLines: 1,
                          maxLength: 2000,
                          controller: _contenuController,
                          autofocus:
                              _contenuController.text != "" ? true : false,
                          autocorrect: true,
                          obscureText: false,
                          maxLines: double.maxFinite.floor(),
                          textAlignVertical: TextAlignVertical.top,
                          decoration: InputDecoration(
                            counterStyle: TextStyle(color: Colors.transparent),
                            border: InputBorder.none,
                            hintText: allTranslations.text("mail_msg"),
                            hintStyle: TextStyle(
                                color:
                                    AppTheme.backgroundColor.withOpacity(0.5),
                                fontSize:
                                    FontSizeController.of(context).fontSize),
                          ),
                        ),
                      ),
                    ))
                  ]))
                ],
              ),
            ),
            floatingActionButton: SizedBox(
              height: 60.0,
              width: 60.0,
              child: FloatingActionButton(
                  heroTag: 0,
                  child:
                      SvgPicture.asset("assets/images/icons/icSendMessage.svg"),
                  backgroundColor: AppTheme.backgroundColor,
                  onPressed: _sendMessage),
            )));
  }

  Future<void> _saveDraftAction() async {
    await widget.onSaveDraftTap!(_destinataireParDefaut, _destinataireMessage,
        _objetController.text, _contenuController.text);
  }

  void _changeDestinataire(String? newValue) {
    // change le destinataire du message
    if (newValue != null) {
      setState(() {
        _destinataireParDefaut = newValue;
        int indexDestinataire =
            _listMonitoringIDsString.indexOf(_destinataireParDefaut);
        _destinataireMessage = "TLS_" + _listMonitoringIDs[indexDestinataire];
      });
    }
  }

  void _sendMessage() {
    // valide le contenu des texfields et lance l'event pour l'envoi d'un message
    String objet = _objetController.text;
    String contenu = _contenuController.text;
    if ((objet == "") || (contenu == "")) {
      // pas d'objet ou de contenu
      _showFlushbar(false, allTranslations.text('error'),
          allTranslations.text("msg_empty"));
    } else {
      _blocMessaging.add(EventMessagingSendNewMessage(
          destinataire: _destinataireMessage,
          objet: objet,
          contenu: contenu,
          fileList: _fileList,
          context: context,
          audioFile: _audioFile,
          isDraft: widget.isDraft));
    }
  }

  Future<void> _addAttachments() async {
    if (_nbPjMaxToSelect > 0) {
      int? choice = await CustomBottomSheet(
        context: context,
        labels: [allTranslations.text('photos'), allTranslations.text('audio')],
      ).show();
      if (choice != null) {
        await _onAttachmentChoice(choice);
      }
    } else {
      _showFlushbar(
          false,
          "",
          allTranslations
              .text('msg_max_attachments', {"nb": "${Config.pjMaxNumber}"}));
    }
  }

  Future<void> _onAttachmentChoice(int i) async {
    switch (i) {
      case 0:
        {
          await _pickImageInGallery();
        }
        break;
      case 1:
        {
          _pickAudioFile(context, _audioFile, _handlePickedAudioFile);
        }
    }
  }

  Future<void> _pickImageInGallery() async {
    List list = await MultiImagePickerUtils.pickImagesFromGallery(
        context, _nbPjMaxToSelect);
    await _handlePickedImageFile(list);
  }

  void _pickAudioFile(BuildContext context, File? existingAudioFile,
      Function(bool, File) callback) async {
    bool continuer = await RecordAudioUtils.recordAnotherAudioFile(
        context, existingAudioFile);
    if (continuer) {
      if (_audioFile != null) {
        _removeFile(File("dummy.aac"));
      }
      List audioResponseList =
          await RecordAudioUtils.showAlertAudioRecorder(context);
      bool isRecorded = audioResponseList[0];
      File audioFile = audioResponseList[1];
      callback(isRecorded, audioFile);
    }
  }

  void _showCloseSheet() async {
    // montre bottom sheet pour effacer le message
    int? res = await CustomBottomSheet(
        context: context,
        labels: [allTranslations.text('delete_message')]).show();
    if (res != null) Navigator.of(context).pop();
  }

  void _showFlushbar(bool success, String title, String description) {
    AlertBox(
      context: context,
      title: title,
      description: description,
    ).showFlushbar(warning: !success);
  }

  /// Gestion des pj
  Future<void> _handlePickedImageFile(List pickedFileList) async {
    // ajoute une image sélectionnée à la liste
    List<Image> thumbnailList = pickedFileList[0];
    List<File> fileList = pickedFileList[1];

    if (fileList.length > 0) {
      _fileList = _fileList + fileList;
      thumbnailList.asMap().forEach((index, value) async {
        _listWidgetsPJ.add(ThumbnailWidget(
            image: thumbnailList[index],
            file: fileList[index],
            isAudio: false,
            onRemoval: _removeFile));
      });
      setState(() {
        _nbPjMaxToSelect = _nbPjMaxToSelect - fileList.length;
      });
    }
  }

  Future<void> _handlePickedAudioFile(
      bool isRecorded, File selectedAudioFile) async {
    // ajoute un enregistrement audio à la liste
    if (isRecorded) {
      _audioFile = selectedAudioFile;
      _listWidgetsPJ.add(ThumbnailWidget(
          image: null,
          file: _audioFile!,
          isAudio: true,
          onRemoval: _removeFile));
      _fileList.add(null);
      setState(() {
        _nbPjMaxToSelect = _nbPjMaxToSelect - 1;
      });
    }
  }

  void _removeFile(File fileToRemove) {
    try {
      if (fileToRemove.path.contains(".m4a")) {
        // supprimer audioFile
        _audioFile = null;
        _listWidgetsPJ.forEach((thumbnailWidget) {});
        _listWidgetsPJ
            .removeWhere((thumbnailWidget) => thumbnailWidget.isAudio == true);
        _fileList.removeWhere((thumbnailWidget) => thumbnailWidget == null);
      } else {
        int index = _fileList.indexOf(fileToRemove);
        _listWidgetsPJ.removeAt(index);
        _fileList.removeAt(index);
      }
      setState(() {
        _nbPjMaxToSelect = _nbPjMaxToSelect + 1;
      });
    } catch (e) {
      print(e);
    }
  }

  /// Gestion du brouillon
  Future<void> _onChargeMessageData() async {
    // charge les données sauvées

    SavedMessageData? savedMessageData =
        await MessageDataUtils.getMessageData();

    if (savedMessageData != null) {
      _objetController.text = savedMessageData.objet;
      _contenuController.text = savedMessageData.contenu;

      setState(() {
        _destinataireParDefaut = savedMessageData.destinataireAffichage;
        _destinataireMessage = savedMessageData.destinataireMessage;
      });
    }
  }
}
