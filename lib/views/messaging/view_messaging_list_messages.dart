import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:theradom/models/ErrorDetails.dart';
import 'package:theradom/models/message.dart';
import 'package:theradom/utils/custom_icon_data_icons.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/blocs/messaging/bloc_messaging.dart';
import 'package:theradom/blocs/messaging/event_messaging.dart';
import 'package:theradom/blocs/messaging/state_messaging.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/messaging/view_messaging_message_details.dart';
import 'package:theradom/widgets/error_text.dart';
import 'package:theradom/widgets/messaging/message_tile.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:share_plus/share_plus.dart';
import 'package:theradom/extensions/string_extension.dart';

/// Vue pour affichage des messages en liste d'objets Slidable.
/// BlocListener pour acknowledge et update des messages.

class ViewMessagingListMessages extends StatefulWidget {
  final void Function() updateNotificationBubble;
  final Map<String, Message> messages;
  final int totalAvailable;
  final int boite;

  ViewMessagingListMessages(
      {required this.updateNotificationBubble,
      required this.messages,
      required this.totalAvailable,
      required this.boite});

  @override
  _ViewMessagingListMessagesState createState() =>
      _ViewMessagingListMessagesState();
}

class _ViewMessagingListMessagesState extends State<ViewMessagingListMessages> {
  late final void Function() _callbackHomeUpdateMessageNb;
  late Map<String, Message> _messages;
  late final int _totalAvailable;
  List<Message> _messageList = <Message>[];
  ScrollController _listViewController = ScrollController();
  double _height = 100.0;
  late final BlocMessaging _blocMessaging;

  @override
  void initState() {
    super.initState();
    _callbackHomeUpdateMessageNb = widget.updateNotificationBubble;
    _messages = widget.messages;
    _totalAvailable = widget.totalAvailable;
    _addMessagesToList();
    _listViewController.addListener(_scrollListener);
    _blocMessaging = BlocProvider.of<BlocMessaging>(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<BlocMessaging, StateMessaging>(
        listener: (context, state) {
          if (state is StateMessagingUpdatedMailbox) {
            _updateMailbox(state.errorDetails, state.newMessageList);
          }

          if (state is StateMessagingAcknowledgeMessage) {
            _callbackHomeUpdateMessageNb();
            _setMessageRead(state.message);
          }
        },
        child: Container(
          color: AppTheme.primaryColor,
          child: RefreshIndicator(
              onRefresh: _refresh,
              notificationPredicate: (scrollNotification) {
                return true;
              },
              child: _messageList.length > 0
                  ? CustomScrollbar(
                      scrollbarColor: AppTheme.backgroundColor,
                      child: ListView.builder(
                        physics: ClampingScrollPhysics(),
                        controller: _listViewController,
                        itemBuilder: (context, index) {
                          Message message = _messageList[index];
                          if (message.numEnregMessagerie == "***") {
                            return _canFetchMoreMessages(index);
                          } else {
                            return _buildSlidable(context, index, message);
                          }
                        },
                        itemCount: _messageList.length,
                      ),
                    )
                  : ErrorText(
                      message: allTranslations.text('no_message'),
                    )),
        ));
  }

  Widget _buildSlidable(BuildContext context, int index, Message message) {
    return Container(
        decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(width: 1, color: AppTheme.dividerColor)),
        ),
        child: Slidable(
            key: Key(message.numEnregMessagerie),
            closeOnScroll: true,
            child: MessageTile(message: message, onTap: _openMessage),
            startActionPane: widget.boite < 2
                ? ActionPane(
                    motion: const ScrollMotion(),
                    dismissible: DismissiblePane(
                        closeOnCancel: false,
                        confirmDismiss: () async =>
                            await _showRemoveMessageDialog(message),
                        onDismissed: () {
                          print("ON DISMISSED");
                          setState(() {
                            _messageList.removeAt(index);
                          });
                          _launchEventDeleteMessage(message, true);
                        }),
                    children: [
                      SlidableAction(
                        autoClose: false,
                        onPressed: (context) async {
                          bool res = await _showRemoveMessageDialog(message);
                          if (res) {
                            await Slidable.of(context)?.dismiss(
                              ResizeRequest(const Duration(milliseconds: 300),
                                  () {
                                setState(() {
                                  _messageList.removeAt(index);
                                });
                                _launchEventDeleteMessage(message, true);
                              }),
                              duration: const Duration(milliseconds: 300),
                            );
                          }
                        },
                        backgroundColor: AppTheme.tertiaryColor,
                        icon: Icons.delete,
                        label: allTranslations.text('to_hide'),
                      )
                    ],
                  )
                : null,
            endActionPane: ActionPane(
              extentRatio: 0.25,
              motion: ScrollMotion(),
              dragDismissible: false,
              children: [
                SlidableAction(
                  onPressed: (_) => _shareMessage(message),
                  backgroundColor: AppTheme.secondaryColor,
                  icon: CustomIconData.icshare,
                  label: allTranslations.text('toShare'),
                )
              ],
            )));
  }

  Future<bool> _showRemoveMessageDialog(Message message) async {
    bool? res = await AlertBox(
            context: context,
            title: allTranslations
                .text('hide_message_txt', {"sender": message.emetteur}),
            description: "${message.motif}\n\n${message.zMessage}",
            confirmBtnLabel: allTranslations.text('to_hide'))
        .showConfirmation(warning: true);
    return res == null ? false : res;
  }

  Widget _canFetchMoreMessages(int index) {
    // ajoute un message composé d'un indicateur de chargement quand on arrive en fin de liste et que d'autres messages sont disponibles
    if ((_messageList.length < _totalAvailable) &&
        (_messageList[index - 1].numEnregMessagerie != "***")) {
      return Container(
        height: _height,
        child: PlatformCircularIndicator(darkIndicator: false),
      );
    } else {
      return Container();
    }
  }

  void _shareMessage(Message message) {
    // partage d'un message
    Share.share(message.emetteur +
        "\n\n" +
        message.motif +
        "\n\n" +
        message.zMessage +
        "\n\n" +
        message.zDate.formatYYYYMMDD2Language() +
        " ${allTranslations.text('at')} " +
        message.zheure);
  }

  void _launchEventDeleteMessage(Message message, bool slideRemoval) {
    // lancement de l'event pour supprimer un message
    _blocMessaging.add(EventMessagingDeleteMessage(
        messageToDelete: message, slideRemoval: slideRemoval));
  }

  void _openMessage(Message message) {
    // Ouvre l'interface de visualisation d'un message
    _launchEventAcknowledgeMessage(message);
    showMaterialModalBottomSheet(
        expand: false,
        enableDrag: false,
        isDismissible: false,
        elevation: 0,
        duration: Duration(milliseconds: 500),
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => ViewMessagingMessageDetails(
              blocMessaging: _blocMessaging,
              message: message,
              boite: widget.boite,
              onDelete: _removeMessage,
              onShare: _shareMessage,
            ));
  }

  _removeMessage(Message messageToDelete) async {
    bool? res = await AlertBox(
            context: context,
            title: allTranslations
                .text('hide_message_txt', {"sender": messageToDelete.emetteur}),
            description: messageToDelete.motif,
            confirmBtnLabel: allTranslations.text('to_hide'))
        .showConfirmation(warning: true);
    if (res != null) {
      if (res) {
        Navigator.of(context).pop();
        _launchEventDeleteMessage(messageToDelete, false);
        int index = _messageList.indexOf(messageToDelete);
        setState(() {
          _messageList.removeAt(index);
        });
      }
    }
  }

  void _scrollListener() {
    if (_listViewController.offset >=
            _listViewController.position.maxScrollExtent &&
        !_listViewController.position.outOfRange) {
      // quand j'atteint le bas de la liste de message
      if (_messageList.length < _totalAvailable) {
        // si je peux encore demander des messages, j'ajoute un message bidon qui apparaitra comme loading dans la liste des message
        // puis je lance la fonction pour avoir les autres messages
        setState(() {
          _messageList.add(Message(
              motif: "",
              destinataire: "",
              zMessage: "",
              dejaLu: false,
              numEnregMessagerie: "***",
              zDate: "",
              zheure: "",
              emetteur: "",
              typeMessage: ""));
        });
        _getMoreMessages();
      }
    }
  }

  void _getMoreMessages() {
    // lance un event pour demander 10 messages de plus
    if (_messageList.length - 1 < _totalAvailable) {
      _listViewController.animateTo(_height * _messageList.length,
          duration: Duration(seconds: 1), curve: Curves.easeInOutExpo);
      int numberOfMessagesFetch = _messageList.length - 1;
      _blocMessaging.add(EventMessagingUpdateMailBox(
          option: widget.boite.toString(),
          from: numberOfMessagesFetch + 1,
          to: numberOfMessagesFetch + 10));
    }
  }

  void _launchEventAcknowledgeMessage(Message message) async {
    // lance l'event pour acknowledger la lecture du message
    if (message.dejaLu == false) {
      _blocMessaging.add(EventMessagingAcknowledgeMessaging(message: message));
    }
  }

  Future<void> _refresh() async {
    // refresh de la boîte
    switch (widget.boite) {
      case 0:
        {
          _blocMessaging.add(EventMessagingMailbox());
        }
        break;
      case 1:
        {
          _blocMessaging.add(EventMessagingSentMessages());
        }
        break;
      case 2:
        {
          _blocMessaging.add(EventMessagingTrash());
        }
        break;
    }
  }

  void _setMessageRead(Message message) {
    int messageIndex = _messageList.indexOf(message);
    setState(() {
      _messageList[messageIndex].dejaLu = true;
    });
  }

  void _addMessagesToList() {
    // ajoute les messages à la liste
    _messages.forEach((key, message) {
      _messageList.add(message);
    });
  }

  void _updateMailbox(
      ErrorDetails? errorDetails, List<Message> newMessageList) {
    // update la liste des messages quand de nouveaux sont reçus
    setState(() {
      _messageList.remove(_messageList.last); // retirer le widget de chargement
    });
    if (errorDetails == null) {
      setState(() {
        _messageList = _messageList + newMessageList;
      });
    } else {
      AlertBox(
              context: context,
              title: allTranslations.text('error'),
              description: allTranslations.text("cant_load_messages"))
          .showFlushbar(warning: true);
    }
  }
}
