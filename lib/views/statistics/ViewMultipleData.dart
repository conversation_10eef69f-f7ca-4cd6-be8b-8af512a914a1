import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/enum_stats_format.dart';
import 'package:theradom/models/stat_field_data_group.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/stats/stats_data_group_tile.dart';
import 'package:theradom/widgets/buttons/radio_button.dart';

class ViewMultipleData extends StatefulWidget {
  final List<StatsFieldDataGroup> dataFieldGroupList;
  final void Function(StatsFormat) onFormatChanged;
  final void Function(List<StatsFieldDataGroup>) onChange;

  ViewMultipleData(
      {required this.dataFieldGroupList,
      required this.onFormatChanged,
      required this.onChange});

  _ViewMultipleDataState createState() => _ViewMultipleDataState();
}

class _ViewMultipleDataState extends State<ViewMultipleData> {
  late List<StatsFieldDataGroup> _dataGroupList;
  StatsFormat _format = StatsFormat.details;
  int _selectedFieldCount = 0;
  int _selectedFieldCountMax = Config.statsMaxLines;

  @override
  void initState() {
    super.initState();
    widget.onFormatChanged(_format);
    _dataGroupList = widget.dataFieldGroupList;
    _updateSelectedFieldCount();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        // text
        Container(
            padding: EdgeInsets.only(left: 21.0, top: 5.0, bottom: 10.0),
            child: Text(
              "${allTranslations.text("select_data_mode")}",
              style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  color: AppTheme.backgroundColor),
            )),

        // affichage moyenne ou details
        Padding(
          padding: EdgeInsets.only(left: 21.0, top: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              _buildRadioButton(
                  allTranslations.text("details"), StatsFormat.details),
              Spacer(),
              _buildRadioButton(
                  allTranslations.text("average"), StatsFormat.average),
              Spacer()
            ],
          ),
        ),

        // text 2
        Flexible(
          child: Container(
              padding: EdgeInsets.only(left: 21.0, top: 25.0, bottom: 10.0),
              child: AutoSizeText(
                "${allTranslations.text("select_data_msg")}: $_selectedFieldCount/$_selectedFieldCountMax",
                maxFontSize: FontSizeController.of(context).fontSize,
                style: TextStyle(
                    fontSize: FontSizeController.of(context).fontSize,
                    color: _selectedFieldCount > _selectedFieldCountMax
                        ? AppTheme.tertiaryColor
                        : AppTheme.backgroundColor),
              )),
        ),

        // list
        widget.dataFieldGroupList.length > 0
            ? Expanded(
                child: ListView(
                    shrinkWrap: true,
                    children: ListTile.divideTiles(
                      context: context,
                      color: AppTheme.dividerColor,
                      tiles: List.generate(
                          widget.dataFieldGroupList.length,
                          (index) => StatsDataGroupTile(
                              onGroupModification: _onGroupModification,
                              dataGroup: widget.dataFieldGroupList[index])),
                    ).toList()),
              )
            : Padding(
                padding: EdgeInsets.only(left: 21.0),
                child: Text(
                  allTranslations.text('no_data'),
                  style: TextStyle(
                      color: AppTheme.backgroundColor,
                      fontSize: FontSizeController.of(context).fontSize),
                ),
              )
      ],
    );
  }

  Widget _buildRadioButton(String label, StatsFormat format) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _format = format;
        });
        widget.onFormatChanged(format);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          RadioButton(
            picked: format == _format ? true : false,
          ),
          SizedBox(
            width: 10.0,
          ),
          Text(
            label,
            style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: FontSizeController.of(context).fontSize,
                color: AppTheme.backgroundColor),
          )
        ],
      ),
    );
  }

  void _onGroupModification(StatsFieldDataGroup updatedStatsFieldDataGroup) {
    _updateFieldDataGroup(updatedStatsFieldDataGroup);
    _updateSelectedFieldCount();
    widget.onChange(_dataGroupList);
  }

  void _updateFieldDataGroup(StatsFieldDataGroup updatedStatsFieldDataGroup) {
    int index = _dataGroupList.indexWhere(
        (element) => element.name == updatedStatsFieldDataGroup.name);
    _dataGroupList[index] = updatedStatsFieldDataGroup;
  }

  void _updateSelectedFieldCount() {
    int count = 0;
    _dataGroupList.forEach((group) {
      count = count + group.selectedFieldCount();
    });
    setState(() {
      _selectedFieldCount = count;
    });
  }
}
