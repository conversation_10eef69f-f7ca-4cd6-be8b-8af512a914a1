import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/statistics/BlocStatistics.dart';
import 'package:theradom/blocs/statistics/StateStatistics.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/change_orientation.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/syncfusion_charts_widget.dart';
import 'package:theradom/widgets/stats/stats_shortcut_button_row.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/config.dart';
import 'package:theradom/extensions/datetime_extension.dart';

/// Vue d'un graphique, affiche aussi bien les données depuis "Statistique"

class ViewChartsStats extends StatefulWidget {
  final DateTime dateFrom;
  final DateTime dateTo;
  final List<dynamic> iLinedataSetList;
  final BlocStatistics blocStatistics;
  final List<double> normaliteList;
  final void Function(DateTime, DateTime) onDataReloadFrom;

  ViewChartsStats({
    required this.dateFrom,
    required this.dateTo,
    required this.iLinedataSetList,
    required this.blocStatistics,
    required this.normaliteList,
    required this.onDataReloadFrom,
  });

  @override
  _ViewChartsStatsState createState() => _ViewChartsStatsState();
}

class _ViewChartsStatsState extends State<ViewChartsStats> {
  late DateTime _dateFrom;
  late DateTime _dateTo;
  double _fontSize = 18.0;
  bool _isLandscape = true;

  @override
  void initState() {
    super.initState();
    _dateFrom = widget.dateFrom;
    _dateTo = widget.dateTo;
    Config.blockBackBtnPop = false;
    // Forcer le mode paysage immédiatement
    _setLandscapeMode();
  }

  @override
  void dispose() {
    // Remettre en mode portrait
    OrientationSetter.change.toPortrait();
    Config.blockBackBtnPop = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Remettre en mode portrait
        await OrientationSetter.change.toPortrait();
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          toolbarHeight: 0,
          backgroundColor: AppTheme.primaryColor,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        body: Container(
          color: AppTheme.primaryColor,
          padding: EdgeInsets.all(10.0),
          child: Column(
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildDateRangeWidget(),
                  SizedBox(width: 30.0),
                  StatsShortcutButtonsRow(
                    onDatesSelected: _setDatesAndReloadFromDate,
                  ),
                  SizedBox(width: 30.0),
                  SvgButton(
                    asset: _isLandscape
                        ? "assets/images/icons/icReduceScreen.svg"
                        : "assets/images/icons/icFullScreen.svg",
                    assetColor: AppTheme.backgroundColor,
                    onTap: _toggleOrientation,
                  ),
                  SizedBox(width: 15.0),
                  SvgButton(
                    asset: "assets/images/icons/icCross.svg",
                    assetColor: AppTheme.backgroundColor,
                    onTap: () async {
                      // Remettre en mode portrait
                      await OrientationSetter.change.toPortrait();
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
              SizedBox(height: 10.0),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: BlocBuilder<BlocStatistics, StateStatistics>(
                    bloc: widget.blocStatistics,
                    buildWhen: (previousState, state) {
                      // Ne jamais reconstruire pour StateStatisticsMainScreen
                      // Cela empêche le retour à l'écran de sélection
                      if (state is StateStatisticsMainScreen) {
                        return false;
                      }
                      // Reconstruire pour tous les autres états
                      return true;
                    },
                    builder: (context, state) {
                      Widget screen = Container();

                      if (state is StateStatisticsLoadingCharts) {
                        if (state.reloadChart) {
                          screen =
                              PlatformCircularIndicator(darkIndicator: true);
                        }
                      }

                      if (state is StateStatisticsDisplay) {
                        print(
                            "🔍 STATS UI DEBUG: StateStatisticsDisplay reçu - ${state.chartSeriesList.length} séries, erreur: ${state.errorDetails?.message}");
                        if (state.errorDetails == null &&
                            state.chartSeriesList.isNotEmpty) {
                          // Graphique Syncfusion professionnel avec points
                          screen = SyncfusionChartsWidget(
                            chartSeriesList: state.chartSeriesList,
                          );
                        } else {
                          screen = Center(
                            child: Text(
                              state.errorDetails?.message ??
                                  allTranslations.text("no_data"),
                              style: TextStyle(
                                fontSize:
                                    FontSizeController.of(context).fontSize,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          );
                        }
                      }

                      return screen;
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeWidget() {
    EdgeInsets padding = EdgeInsets.only(
      top: 4.0,
      bottom: 3.0,
      left: 15.0,
      right: 15.0,
    );

    BoxDecoration boxDecoration = BoxDecoration(
      color: AppTheme.backgroundColor,
      borderRadius: BorderRadius.all(Radius.circular(18.0)),
    );

    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Date début
          Container(
            padding: padding,
            decoration: boxDecoration,
            child: Text(
              _dateFrom.toLanguageStringShort(),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
                fontSize: _fontSize,
              ),
            ),
          ),
          SizedBox(width: 10.0),
          SvgPicture.asset("assets/images/icons/icToTime.svg"),
          SizedBox(width: 10.0),
          // Date fin
          Container(
            padding: padding,
            decoration: boxDecoration,
            child: Text(
              _dateTo.toLanguageStringShort(),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
                fontSize: _fontSize,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _setDatesAndReloadFromDate(DateTime from, DateTime to) {
    print("🔍 PARENT TRACE 1: Début _setDatesAndReloadFromDate");
    print("🔍 PARENT TRACE 2: from: $from, to: $to");

    try {
      print("🔍 PARENT TRACE 3: Avant appel _setDates");
      _setDates(from, to);
      print("🔍 PARENT TRACE 4: Après appel _setDates - SUCCÈS");

      print("🔍 PARENT TRACE 5: Avant appel widget.onDataReloadFrom");
      widget.onDataReloadFrom(from, to);
      print("🔍 PARENT TRACE 6: Après appel widget.onDataReloadFrom - SUCCÈS");
    } catch (e) {
      print(
          "🔍 PARENT TRACE ERROR: Erreur dans _setDatesAndReloadFromDate: $e");
      print("🔍 PARENT TRACE ERROR: Stack trace: ${StackTrace.current}");
    }

    print("🔍 PARENT TRACE 7: Fin _setDatesAndReloadFromDate");

    // IMPORTANT: Ne pas fermer l'écran après le rechargement
    print("🔍 PARENT TRACE 8: Maintien de l'écran des graphiques");
  }

  void _setDates(DateTime from, DateTime to) {
    print("🔍 PARENT DEBUG: _setDates appelé - from: $from, to: $to");
    setState(() {
      _dateFrom = from;
      _dateTo = to;
    });
    print(
        "🔍 PARENT DEBUG: setState terminé - _dateFrom: $_dateFrom, _dateTo: $_dateTo");
  }

  void _setLandscapeMode() async {
    print("🔍 ORIENTATION DEBUG: Passage en mode paysage");
    await OrientationSetter.change.toLandscape();
    if (mounted) {
      setState(() {
        _isLandscape = true;
      });
    }
  }

  void _setPortraitMode() async {
    print("🔍 ORIENTATION DEBUG: Passage en mode portrait");
    await OrientationSetter.change.toPortrait();
    if (mounted) {
      setState(() {
        _isLandscape = false;
      });
    }
  }

  void _toggleOrientation() {
    print("🔍 ORIENTATION DEBUG: _toggleOrientation() called!");
    print(
        "🔍 ORIENTATION DEBUG: Toggle orientation - Current: ${_isLandscape ? 'Landscape' : 'Portrait'}");
    if (_isLandscape) {
      print("🔍 ORIENTATION DEBUG: Switching to Portrait");
      _setPortraitMode();
    } else {
      print("🔍 ORIENTATION DEBUG: Switching to Landscape");
      _setLandscapeMode();
    }
  }
}
