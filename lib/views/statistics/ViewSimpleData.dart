import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/stat_field_data_group.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/stats/stats_data_group_tile.dart';

class ViewSimpleData extends StatefulWidget {
  final List<StatsFieldDataGroup> dataFieldGroupList;
  final void Function(List<StatsFieldDataGroup>) onChange;

  ViewSimpleData({required this.dataFieldGroupList, required this.onChange});

  _ViewSimpleDataState createState() => _ViewSimpleDataState();
}

class _ViewSimpleDataState extends State<ViewSimpleData> {
  late List<StatsFieldDataGroup> _dataGroupList;
  int _selectedFieldCount = 0;
  int _selectedFieldMax = Config.statsMaxLines;

  @override
  void initState() {
    super.initState();
    _dataGroupList = widget.dataFieldGroupList;
    _updateSelectedFieldCount();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // text sélection des données
        Padding(
          padding: EdgeInsets.only(left: 21.0, top: 5.0, bottom: 10.0),
          child: AutoSizeText(
            allTranslations.text("select_data_msg") +
                ": $_selectedFieldCount/$_selectedFieldMax",
            maxFontSize: FontSizeController.of(context).fontSize,
            style: TextStyle(
                fontSize: FontSizeController.of(context).fontSize,
                color: _selectedFieldCount > _selectedFieldMax
                    ? AppTheme.tertiaryColor
                    : AppTheme.backgroundColor),
          ),
        ),

        // liste groupes de données
        widget.dataFieldGroupList.length > 0
            ? Expanded(
                child: ListView(
                  shrinkWrap: true,
                  children: ListTile.divideTiles(
                    context: context,
                    color: AppTheme.dividerColor,
                    tiles: List.generate(
                        _dataGroupList.length,
                        (index) => StatsDataGroupTile(
                            dataGroup: _dataGroupList[index],
                            onGroupModification: _onGroupModification)),
                  ).toList(),
                ),
              )
            : Padding(
                padding: EdgeInsets.only(left: 21.0),
                child: Text(
                  allTranslations.text('no_data'),
                  style: TextStyle(
                      color: AppTheme.backgroundColor,
                      fontSize: FontSizeController.of(context).fontSize),
                ),
              )
      ],
    );
  }

  void _onGroupModification(StatsFieldDataGroup updatedStatsFieldDataGroup) {
    _updateFieldDataGroup(updatedStatsFieldDataGroup);
    _updateSelectedFieldCount();
    widget.onChange(_dataGroupList);
  }

  void _updateFieldDataGroup(StatsFieldDataGroup updatedStatsFieldDataGroup) {
    int index = _dataGroupList.indexWhere(
        (element) => element.name == updatedStatsFieldDataGroup.name);
    _dataGroupList[index] = updatedStatsFieldDataGroup;
  }

  void _updateSelectedFieldCount() {
    int count = 0;
    _dataGroupList.forEach((group) {
      count = count + group.selectedFieldCount();
    });
    setState(() {
      _selectedFieldCount = count;
    });
  }
}
