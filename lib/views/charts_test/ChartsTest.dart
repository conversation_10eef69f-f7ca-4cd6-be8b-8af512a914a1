import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class ViewChartsTestPage extends StatefulWidget {
  @override
  _ViewChartsTestPageState createState() => _ViewChartsTestPageState();
}

class _ViewChartsTestPageState extends State<ViewChartsTestPage> {
  List<double> data = [5, 10, 15, 20, 25]; // Made-up data

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('FlChart Example'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: LineChart(
          LineChartData(
            lineBarsData: [
              LineChartBarData(
                spots: data.asMap().entries.map((entry) {
                  return FlSpot(entry.key.toDouble(), entry.value);
                }).toList(),
                isCurved: true,
                color: Colors.blue, // Fix: Replace 'colors' with 'color'
                barWidth: 4,
                dotData: FlDotData(show: false),
              ),
            ],
            minX: 0,
            maxX: data.length.toDouble() - 1,
            minY: 0,
            maxY: data
                .reduce((value, element) => value > element ? value : element),
            titlesData: FlTitlesData(
              bottomTitles: AxisTitles(
                // Fix: Replace 'SideTitles' with 'AxisTitles'
                sideTitles: SideTitles(showTitles: true),
              ),
              leftTitles: AxisTitles(
                // Fix: Replace 'SideTitles' with 'AxisTitles'
                sideTitles: SideTitles(showTitles: true),
              ),
            ),
            gridData: FlGridData(show: true),
          ),
        ),
      ),
    );
  }
}
