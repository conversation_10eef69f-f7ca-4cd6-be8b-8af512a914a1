import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/qrcode/BlocQrCode.dart';
import 'package:theradom/blocs/qrcode/EventQrCode.dart';
import 'package:theradom/blocs/qrcode/StateQrCode.dart';
import 'package:theradom/config.dart';
import 'package:theradom/services/snackbar_service.dart';
import 'package:theradom/utils/change_orientation.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:theradom/utils/EncryptionUtils.dart';

/// Vue écran lecture qr code
class ViewQRcode extends StatefulWidget {
  @override
  _ViewQRcodeState createState() => _ViewQRcodeState();
}

class _ViewQRcodeState extends State<ViewQRcode> {
  final GlobalKey _qrKey = GlobalKey(debugLabel: 'QR');
  late final QRViewController _controller;
  late final BlocQrCode _blocQrCode;
  bool _isScanning = true;

  /// Initialise l'état du widget
  /// - Désactive le blocage du bouton retour
  /// - Force l'orientation en mode portrait
  /// - Initialise le bloc QR code
  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
    OrientationSetter.change.toPortrait();
    _blocQrCode = BlocProvider.of<BlocQrCode>(context);
  }

  /// Nettoie les ressources lors de la destruction du widget
  /// - Réactive le blocage du bouton retour
  /// - Libère le contrôleur de QR code
  @override
  void dispose() {
    Config.blockBackBtnPop = true;
    _controller.dispose();
    super.dispose();
  }

  /// Construit l'interface de scan de QR code
  /// - Affiche un overlay de scan avec bordures
  /// - Affiche un message d'instruction
  /// - Gère les différents états du scan (détection utilisateur, détection GS1)
  /// @param context Le contexte de build Flutter
  /// @return Le widget construit
  @override
  Widget build(BuildContext context) {
    return BlocListener<BlocQrCode, StateQrCode>(
      bloc: _blocQrCode,
      listener: (previousState, state) async {
        if (state is StateQrCodeUserDetected) {
          if (state.errorOccured) {
            HapticFeedback.vibrate();
            _isScanning = true;
            SnackBarService.showErrorSnackBar(
              context,
              message: 'Erreur lors de la lecture du QR code',
            );
          }
        }

        if (state is StateQrCodeGS1Detected) {
          Navigator.of(context).pop(state.gs1code128data);
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
          backgroundColor: Colors.transparent,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        body: Stack(
          children: <Widget>[
            QRView(
              key: _qrKey,
              overlay: QrScannerOverlayShape(
                borderColor: AppTheme.backgroundColor,
                borderRadius: 1.0,
                borderWidth: 10,
                borderLength: 30,
                overlayColor: AppTheme.primaryColor.withOpacity(0.8),
                cutOutSize: MediaQuery.of(context).size.width * 2 / 3,
              ),
              onQRViewCreated: _onQRcodeDetected,
            ),
            Center(
              child: Column(
                children: <Widget>[
                  Spacer(),
                  Padding(
                    padding:
                        EdgeInsets.only(bottom: 50.0, left: 30.0, right: 30.0),
                    child: Text(
                      allTranslations.text('aim_at_qr_code_msg'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: AppTheme.backgroundColor,
                        fontSize: 18.0,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 50.0,
              left: 2.0,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: ChevIcon(
                  assetPath: "assets/images/icons/btChevBlancL.svg",
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Gère la détection d'un QR code
  /// - Initialise le contrôleur de QR code
  /// - Configure l'écoute du flux de données scannées
  /// - Déclenche l'événement de détection une seule fois par scan
  /// @param controller Le contrôleur de QR code à initialiser
  void _onQRcodeDetected(QRViewController controller) async {
    this._controller = controller;
    controller.scannedDataStream.listen((streamScanData) {
      if (_isScanning) {
        _isScanning = false;
        print(EncryptionUtils(streamScanData.code ?? "").aesDecrypt());
        _blocQrCode.add(EventQrCodeDataDetected(scanData: streamScanData.code));
        print(streamScanData.code);
      }
    });
  }
}
