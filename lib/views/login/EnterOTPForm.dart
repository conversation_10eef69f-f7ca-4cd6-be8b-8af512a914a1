import 'package:flutter/material.dart';

class EnterOTPForm extends StatelessWidget {
  // Controllers pour les champs de texte
  final TextEditingController securityKeyController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      // Use Dialog instead of AlertDialog for more customization
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0), // Increased border radius
      ),
      child: Container(
        // Add a Container for padding and background
        padding: EdgeInsets.all(30.0), // Generous padding
        decoration: BoxDecoration(
          color: Colors.white, // White background
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 0.0,
              blurRadius: 10.0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Row(
              // Row for title and close button
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Entrer la clé de sécurité',
                  style: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue, // Primary color for title
                  ),
                ),
                IconButton(
                  // Use IconButton for close button
                  iconSize: 30, // Increased icon size
                  icon: Icon(Icons.close, color: Colors.grey),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            Text(
              'Veuillez entrer la clé de sécurité pour activer votre compte.',
              style: TextStyle(
                fontSize: 12.0,
                color: Colors.grey, // Secondary color for message
              ),
            ),
            SizedBox(height: 15),
            TextField(
              controller: newPasswordController,
              decoration: InputDecoration(
                labelText: 'Clé de sécurité',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                contentPadding: EdgeInsets.all(20.0),
              ),
              obscureText: true,
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 15),
            Row(
              // Row for buttons
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  // Confirm button
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue, // Primary color background
                    padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                    textStyle: TextStyle(fontSize: 18), // Larger font size
                  ),
                  child:
                      Text('Confirmer', style: TextStyle(color: Colors.white)),
                  onPressed: () {
                    // ... (Password modification logic)
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
