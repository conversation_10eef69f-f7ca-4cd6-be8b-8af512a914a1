import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/login/BlocLogin.dart';
import 'package:theradom/config.dart';
import 'package:theradom/blocs/login/EventLogin.dart';
import 'package:theradom/blocs/login/StateLogin.dart';
import 'package:theradom/blocs/qrcode/QRcodeBlocProvider.dart';
import 'package:theradom/utils/helper_message.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/biometric.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/regexp_utils.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/qrcode/ViewQRcode.dart';
import 'package:theradom/widgets/AppInfosElement.dart';
import 'package:theradom/widgets/buttons/dialog_button.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/widgets/dialog_background.dart';
import 'package:theradom/blocs/login/ModifyPasswordForm.dart';
import 'package:theradom/services/repositories/mobile_password_reset_repository.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/qrcode/BlocQrCode.dart';
import 'package:theradom/blocs/qrcode/StateQrCode.dart';
import 'package:theradom/services/repositories/mobile_status_repository.dart';

class ViewLoginForm extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return QrCodeBlocProvider(
      action: 0,
      child: _ViewLoginFormContent(),
    );
  }
}

class _ViewLoginFormContent extends StatefulWidget {
  @override
  _ViewLoginFormContentState createState() => _ViewLoginFormContentState();
}

class _ViewLoginFormContentState extends State<_ViewLoginFormContent> {
  final TextEditingController _loginEmailController = TextEditingController();
  final TextEditingController _loginPasswordController =
      TextEditingController();
  final AutoSizeGroup _autoSizeGroup = AutoSizeGroup();
  bool _obscureTextLogin = true;
  int loginAttempts = 0;
  bool isUserBlocked = false;
  DateTime? blockEndTime;
  double _fontSize = 16; // Added fontSize variable

  @override
  void initState() {
    super.initState();
    _fontSize = 16;
    if (Config.firstUseState == 1 || Config.useBiometricID!) {
      _loginEmailController.text = Config.userAccountInfos!.login;
    }
  }

  @override
  void dispose() {
    _loginEmailController.dispose();
    _loginPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<BlocLogin, StateLogin>(
          listener: (context, state) {
            if (state is StateLoginFailure) {
              setState(() {
                loginAttempts++;
              });
              _showFlushbar(false, 'Erreur',
                  'Login ou mot de passe incorrect. ${4 - loginAttempts} essais restants');
            } else if (state is StateLoginLoading) {
              if (state.loading) {
                AlertBox(
                        context: context,
                        title: allTranslations.text('please_wait'))
                    .showLoading();
              } else {
                Navigator.of(context).pop();
              }
            } else if (state is StateOTPRequired) {
              _launchEventNewDevice(Config.userAccountInfos!.INS);
            } else if (state is StateLoginEmailSent) {
              print("Password reset fully OK");
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return ModifyPasswordForm(
                      passwordRepository: MobileResetPasswordRepository());
                },
              );
            } else if (state is StateLoginModifyPassword) {
              bool success = state.success;
              if (success) Navigator.of(context).pop();
              String message = state.message;
              _showFlushbar(success, state.title, message);
            } else if (state is StateLoginAccountBlocked) {
              _showFlushbar(false, state.titleError,
                  HelperMessage.getErrorMessage(state.messageError));
            } else if (state is StateLoginEmailUnknown) {
              _showFlushbar(false, state.titleError,
                  HelperMessage.getErrorMessage(state.messageError));
            }
          },
        ),
        BlocListener<BlocQrCode, StateQrCode>(
          listener: (context, state) {
            if (state is StateQrCodeUserDetected) {
              Navigator.of(context).pop();

              setState(() {
                _loginEmailController.text = Config.userAccountInfos!.login;
              });
            }
            if (state is StateQrCodeUserStatusChecked) {
              print("StateQrCodeUserDetected");
            }
            print("StateQrCodeUserDetected");
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: AppTheme.primaryColor,
        body: SingleChildScrollView(
          child: Container(
            padding:
                EdgeInsets.only(top: 80, left: 20.0, right: 20.0, bottom: 30.0),
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width * 8 / 10,
                  child: Image.asset("assets/images/logo/theradom.png",
                      color: AppTheme.backgroundColor),
                ),
                SizedBox(height: 50.0),
                _buildTextField(_loginEmailController, "Login ou INS", false),
                SizedBox(height: 25.0),
                _buildTextField(_loginPasswordController,
                    allTranslations.text('password'), true),
                SizedBox(height: 10),
                if (Config.useBiometricID!)
                  Container(
                    padding: EdgeInsets.all(10.0),
                    height: 50.0,
                    width: 50.0,
                    child: IconButton(
                      icon: Icon(Icons.fingerprint),
                      onPressed: _authenticateWithBiometrics,
                      color: Colors.white,
                    ),
                  ),
                SizedBox(height: 20.0),
                _buildLoginButton(),
                SizedBox(height: 20.0),
                TextButton(
                  onPressed: () => _openDialogToModifyPassword(1),
                  child: Text(
                    "Réinitialiser mon mot de passe",
                    style: TextStyle(color: AppTheme.backgroundColor),
                  ),
                ),
                SizedBox(height: 20.0),
                _buildBottomButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(
      TextEditingController controller, String hint, bool isPassword) {
    return Container(
      height: 64,
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
              blurRadius: 20.0, color: AppTheme.primaryColor.withOpacity(0.5))
        ],
      ),
      child: TextField(
        controller: controller,
        obscureText: isPassword ? _obscureTextLogin : false,
        decoration: InputDecoration(
          hintText: hint,
          contentPadding:
              EdgeInsets.symmetric(horizontal: 20.0, vertical: 15.0),
          border: InputBorder.none,
          suffixIcon: isPassword
              ? IconButton(
                  icon: Icon(_obscureTextLogin
                      ? Icons.visibility
                      : Icons.visibility_off),
                  onPressed: _togglePasswordVisibility,
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildLoginButton() {
    return Container(
      height: 64,
      child: TextButton(
        onPressed: isUserBlocked ? null : () => _onLoginButtonPressed(false),
        child: Center(
          child: Text(
            allTranslations.text('connect'),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w600,
              fontSize: _fontSize,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForgotPasswordButton() {
    return TextButton(
      onPressed: () => _openDialogToModifyPassword(1),
      child: Text(
        "Réinitialiser mon mot de passe",
        style: TextStyle(color: AppTheme.backgroundColor),
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        SvgButton(
          onTap: () => _showInfos(),
          asset: "assets/images/icons/icInfoApp.svg",
        ),
        SvgButton(
          onTap: () => _showQRcode(),
          asset: "assets/images/icons/icQRcode.svg",
        ),
      ],
    );
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscureTextLogin = !_obscureTextLogin;
    });
  }

  Future<void> _showQRcode() async {
    final blocQrCode =
        BlocProvider.of<BlocQrCode>(context); // Récupère le Bloc existant

    final result = await Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => BlocProvider.value(
          value: blocQrCode, // Réutilise le même Bloc
          child: ViewQRcode(),
        ),
      ),
    );

    // if (result is String) {
    //   setState(() {
    //     _loginEmailController.text = result;
    //   });
    // }
  }

  void _onLoginButtonPressed(bool biometricAuth) {
    if (isUserBlocked) return;

    if (loginAttempts >= 3) {
      setState(() {
        isUserBlocked = true;
        blockEndTime = DateTime.now().add(Duration(minutes: 30));
      });
      _startBlockTimer();
      return;
    }

    if (Config.nomDomaine.isEmpty) {
      _showFlushbar(false, allTranslations.text('error'),
          allTranslations.text("txt_appNotConfig"));
    } else {
      BlocProvider.of<BlocLogin>(context).add(EventLogginBtnPressed(
        biometricAuth: biometricAuth,
        user: _loginEmailController.text,
        password: biometricAuth
            ? Config.userAccountInfos!.passwordHash
            : _loginPasswordController.text,
      ));
    }
  }

  void _showFlushbar(bool success, String title, String description) {
    AlertBox(context: context, title: title, description: description)
        .showFlushbar(warning: !success);
  }

  Future<void> _authenticateWithBiometrics() async {
    if (Config.firstUseState == 2 && Config.useBiometricID!) {
      try {
        final authSuccess = await Biometric.authentication.start();
        if (authSuccess) _onLoginButtonPressed(true);
      } catch (e) {
        print("Biometric authentication error: $e");
      }
    }
  }

  void _startBlockTimer() {
    Future.delayed(Duration(minutes: 30), () {
      setState(() {
        isUserBlocked = false;
        loginAttempts = 0;
      });
    });
  }

  void _launchEventResetPassword(ins, login, email) {
    // lance l'event pour envoyer l'email de reset du password
    BlocProvider.of<BlocLogin>(context)
        .add(EventLoginForgotPassword(email: email));
  }

  void _launchEventNewDevice(String ins) {
    BlocProvider.of<BlocLogin>(context).add(EventLoginNewDevice(ins: ins));
  }

  void _showInfos() {
    AlertBox(
      context: context,
      title: "${Config.appName} v.${Config.appVersion}",
      content: Text("Application information goes here."),
    ).showInfo();
  }

  void _openDialogToModifyPassword(int mode) async {
    // dialogue de modification du mot de passe
    // mode 1 : mot de passe oublié
    // mode 2 : appareil compromis

    TextEditingController emailController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    switch (mode) {
      case 1:
        await showDialog<String>(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return DialogBackground(
                  child: Form(
                key: formKey,
                child: Center(
                  child: Column(
                    children: [
                      Text(
                        allTranslations.text('enter_email'),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 18.0,
                            fontWeight: FontWeight.w600),
                      ),
                      SizedBox(
                        height: 30.0,
                      ),
                      Container(
                        padding: EdgeInsets.only(bottom: 10.0),
                        decoration: BoxDecoration(
                            color: AppTheme.backgroundColor,
                            borderRadius: BorderRadius.circular(20.0),
                            boxShadow: [
                              BoxShadow(
                                  spreadRadius: 0.0,
                                  blurRadius: 10.0,
                                  color: AppTheme.primaryBlurColor
                                      .withOpacity(0.3))
                            ]),
                        child: TextFormField(
                          validator: (value) {
                            if (value != null) {
                              if (value.isEmpty) {
                                return allTranslations.text('not_specified');
                              } else {
                                if (!RegexpUtils.emailAdress.hasMatch(value)) {
                                  return allTranslations
                                      .text('format_not_valid');
                                } else {
                                  return null;
                                }
                              }
                            }
                            return null;
                          },
                          style: TextStyle(fontSize: 18.0),
                          autocorrect: false,
                          cursorColor: AppTheme.primaryColor,
                          autofocus: true,
                          enableInteractiveSelection: false,
                          keyboardType: TextInputType.emailAddress,
                          enabled: true,
                          controller: emailController,
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.all(20.0),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20.0),
                                borderSide: BorderSide.none),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 30.0,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          DialogButton(
                              label: allTranslations.text('back'),
                              labelColor: AppTheme.primaryColor,
                              backgroundColor: AppTheme.backgroundColor,
                              onPressed: () => Navigator.of(context).pop(),
                              group: _autoSizeGroup),
                          SizedBox(
                            width: 30.0,
                          ),
                          DialogButton(
                              label: allTranslations.text('to_validate'),
                              labelColor: AppTheme.primaryColor,
                              backgroundColor: AppTheme.secondaryColor,
                              group: _autoSizeGroup,
                              onPressed: () async {
                                if (formKey.currentState!.validate()) {
                                  if (Config.userAccountInfos!.INS == "" ||
                                      Config.userAccountInfos == "") {
                                    HapticFeedback.vibrate();
                                    _showFlushbar(false, 'Erreur',
                                        'Veuillez scanner un QR code avant de continuer');
                                  } else {
                                    _launchEventResetPassword(
                                        Config.userAccountInfos!.INS,
                                        Config.userAccountInfos!.login,
                                        emailController.text);
                                  }
                                } else {
                                  HapticFeedback.vibrate();
                                }
                              })
                        ],
                      )
                    ],
                  ),
                ),
              ));
            });
        break;

      case 2:
        break;
    }
  }
}
