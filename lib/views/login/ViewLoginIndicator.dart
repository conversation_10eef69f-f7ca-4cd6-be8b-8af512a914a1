import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/widgets/spinning_child.dart';

class ViewLoginIndicator extends StatelessWidget {
  final String message;

  ViewLoginIndicator({
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          elevation: 0,
          toolbarHeight: 0,
          backgroundColor: AppTheme.primaryColor,
          systemOverlayStyle: SystemUiOverlayStyle.light),
      backgroundColor: AppTheme.primaryColor,
      body: Container(
        padding: EdgeInsets.all(50.0),
        child: Center(
          child: Column(
            children: <Widget>[
              Spacer(),

              // image
              Container(
                  padding: EdgeInsets.only(bottom: 20.0),
                  child: SpinningChild(
                      height: MediaQuery.of(context).size.height / 6,
                      width: MediaQuery.of(context).size.height / 6,
                      child:
                          Image.asset("assets/images/logo/pastille_400.png"))),

              // message
              Text(
                message,
                style: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.backgroundColor),
              ),

              Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
