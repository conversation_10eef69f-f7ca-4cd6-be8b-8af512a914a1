import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/config.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/authentication/StateAuthentication.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseNotificationService.dart';
import 'package:theradom/models/StatusResponse.dart';

import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_send_new_password_repository.dart';

import 'package:theradom/widgets/alert_box.dart';

bool otp = false;

class ViewUserConfirmation extends StatefulWidget {
  final bool otpRequired;
  final bool otpForced;
  ViewUserConfirmation(this.otpRequired, this.otpForced) : super();

  @override
  _ViewUserConfirmationState createState() => _ViewUserConfirmationState();
}

class _ViewUserConfirmationState extends State<ViewUserConfirmation> {
  Widget build(BuildContext context) {
    return BlocListener<BlocAuthentication, StateAuthentication>(
        listener: (context, state) {},
        child: Scaffold(
          appBar: AppBar(
              elevation: 0,
              toolbarHeight: 0,
              backgroundColor: AppTheme.primaryColor,
              systemOverlayStyle: SystemUiOverlayStyle.light),
          backgroundColor: AppTheme.primaryColor,
          body: Container(
            padding: EdgeInsets.all(50.0),
            child: Center(
              child: Column(
                children: <Widget>[
                  Text(allTranslations.text('hello'),
                      style: TextStyle(
                          color: AppTheme.backgroundColor,
                          fontSize: 30.0,
                          fontWeight: FontWeight.bold)),

                  Spacer(),

                  // image
                  Container(
                      padding: EdgeInsets.only(bottom: 20.0),
                      child: CircleAvatar(
                          radius: 70,
                          backgroundColor: AppTheme.backgroundColor,
                          backgroundImage:
                              Config.userAccountInfos!.photo == null
                                  ? null
                                  : Config.userAccountInfos!.photo!.image,
                          child: Config.userAccountInfos!.photo == null
                              ? SvgPicture.asset(
                                  "assets/images/icons/icProfile.svg",
                                  height: 130)
                              : null)),

                  // message
                  Text(
                    "${Config.userAccountInfos!.genre} ${Config.userAccountInfos!.prenom} ${Config.userAccountInfos!.nom}",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.backgroundColor),
                  ),

                  Spacer(),

                  Container(
                      height: 64,
                      child: TextButton(
                        onPressed: () => _launchContinueEvent(
                            context), //_showConfirmationDialog(context),
                        child: Center(
                          child: Text(
                            allTranslations.text('it_s_me'),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w600,
                                fontSize:
                                    FontSizeController.of(context).fontSize),
                          ),
                        ),
                      )),

                  SizedBox(
                    height: 30.0,
                  ),

                  Container(
                      height: 64,
                      child: TextButton(
                        onPressed: () => _launchLogOutEvent(context),
                        style: ButtonStyle(
                            backgroundColor: MaterialStateProperty.resolveWith(
                                (states) =>
                                    Theme.of(context).primaryColorDark)),
                        child: Center(
                          child: Text(
                            allTranslations.text('it_s_not_me'),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                color: AppTheme.tertiaryColor,
                                fontWeight: FontWeight.w600,
                                fontSize:
                                    FontSizeController.of(context).fontSize),
                          ),
                        ),
                      ))
                ],
              ),
            ),
          ),
        ));
  }

  void _launchLogOutEvent(BuildContext context) {
    BlocProvider.of<BlocAuthentication>(context).add(EventLoggedOut());
  }

  void _launchContinueEvent(BuildContext context) {
    // print("widget.otpRequired : ${widget.otpRequired}");
    // print("widget.otpForced : ${widget.otpForced}");
    otp = widget.otpRequired || widget.otpForced;
    // print("otp : $otp");
    if (widget.otpRequired && otp) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          final TextEditingController _securityKeyController =
              TextEditingController();
          final TextEditingController _deviceNameController =
              TextEditingController();
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.0),
            ),
            title: Text(
              'Sécurité',
              style: TextStyle(
                fontSize: 26.0,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            content: Container(
              padding: EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0),
                    spreadRadius: 0.0,
                    blurRadius: 10.0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    'Vous essayez de vous connecter depuis un appareil inconnu. Veuillez confirmer votre identité en entrant la clé de sécurité reçue par email.',
                    style: TextStyle(
                      fontSize: 16.0,
                      color: Colors.black54,
                    ),
                  ),
                  SizedBox(height: 15),
                  TextFormField(
                    controller: _securityKeyController,
                    decoration: InputDecoration(
                      labelText: 'Clé de sécurité',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      contentPadding: EdgeInsets.all(20.0),
                    ),
                    obscureText: false,
                    style: TextStyle(fontSize: 18),
                  ),
                  SizedBox(height: 15),
                  TextFormField(
                    controller: _deviceNameController,
                    decoration: InputDecoration(
                      labelText: 'Nom de cet appareil',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),
                      contentPadding: EdgeInsets.all(20.0),
                    ),
                    style: TextStyle(fontSize: 18),
                  ),
                  SizedBox(height: 15),
                  TextButton(
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      textStyle: TextStyle(fontSize: 16),
                    ),
                    onPressed: () {
                      // Add the logout event - Go back to the login page
                      Navigator.of(context).pop();
                      BlocProvider.of<BlocAuthentication>(context)
                          .add(EventLoggedOut());
                    },
                    child: Text(
                      allTranslations.text('cancel'),
                      style: TextStyle(
                        color: Colors.white,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: <Widget>[
              TextButton(
                style: TextButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  textStyle: TextStyle(fontSize: 16),
                ),
                child: Text('Confirmer'),
                onPressed: () {
                  _confirmIDApp(
                    Config.userAccountInfos!.INS,
                    Config.userAccountInfos!.login,
                    _securityKeyController.text,
                    Config.basicUserInfos!.mail,
                    false,
                    Config.userAccountInfos!.passwordHash,
                    _deviceNameController.text,
                    context, // Pass the device name
                  );
                  // Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    } else if (widget.otpForced && otp) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          final TextEditingController _securityKeyController =
              TextEditingController();
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.0),
            ),
            titlePadding: EdgeInsets.only(top: 20, bottom: 10),
            title: Center(
              child: Text(
                'Sécurité',
                style: TextStyle(
                  fontSize: 24.0,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ),
            content: Container(
              padding: EdgeInsets.all(10.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1.0,
                    blurRadius: 10.0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    "L'authentification double facteur a été activée. Veuillez entrer la clé de sécurité envoyée sur votre boîte mail.",
                    style: TextStyle(
                      fontSize: 16.0,
                      color: Colors.black54,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 20),
                  TextFormField(
                    controller: _securityKeyController,
                    decoration: InputDecoration(
                      labelText: 'Clé de sécurité',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(15.0),
                      ),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                      labelStyle: TextStyle(color: Colors.blue),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue),
                        borderRadius: BorderRadius.circular(15.0),
                      ),
                    ),
                    obscureText: true,
                    style: TextStyle(fontSize: 16),
                  ),
                  SizedBox(height: 10),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        textStyle: TextStyle(fontSize: 14),
                      ),
                      onPressed: () {
                        print("Vous n'avez pas reçu de mail? link clicked");
                      },
                      child: Text(
                        "Renvoyer le mail",
                        style: TextStyle(
                          color: Colors.white,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actionsPadding: EdgeInsets.only(bottom: 10, right: 10, left: 10),
            actions: <Widget>[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15.0),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    _confirmOTP(
                      Config.userAccountInfos!.INS,
                      _securityKeyController.text,
                      Config.userAccountInfos!.login,
                      context,
                    );
                    // Navigator.of(context).pop();
                  },
                  child: Text(
                    'Confirmer',
                    style:
                        TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          );
        },
      );
    } else {
      BlocProvider.of<BlocAuthentication>(context)
          .add(EventAuthenticationIdentityConfirmed());
    }
  }
}

void _confirmIDApp(
    String ins,
    String login,
    String OTP,
    String email,
    bool save_pwd,
    String? password,
    String deviceName,
    BuildContext context) async {
  // print("Fonction _confirmIDApp appelée"); // Ajouté pour le débogage
  MobileSendNewPasswordRepository repository =
      MobileSendNewPasswordRepository();
  // print("ins : $ins");
  // print("login : $login");
  // print("OTP : $OTP");
  // print("email : $email");
  // print("save_pwd : $save_pwd");
  // print("password : $password");
  //print("RESULT : $result");
  String AppToken = await FirebaseNotificationService.instance
      .getDeviceToken(); //ligne à modifier pour changer appToken

  // Montrer un dialogue d'attente en retour du ws.
  AlertBox(context: context, title: allTranslations.text('please_wait'))
      .showLoading();
  try {
    String reponse = await repository.sendNewPassword(
        AppToken, ins, login, email, OTP, save_pwd, password!, deviceName);
    // print(reponse);
    if (reponse.toString() == "OK") {
      // Exit loading
      Navigator.of(context).pop();

      otp = false;
      BlocProvider.of<BlocAuthentication>(context)
          .add(EventAuthenticationIdentityConfirmed());

      // Navigate to next page
      Navigator.of(context).pop();
    }
  } on WebServiceCallException catch (_) {
    // Exit loading
    Navigator.of(context).pop();
    AlertBox(
            context: context,
            title: allTranslations.text('error'),
            description: _.toString())
        .showFlushbar(warning: true);
  }
  return null;
}

void _confirmOTP(
    String ins, String OTP, String login, BuildContext context) async {
  // print("Fonction _confirmIDApp appelée"); // Ajouté pour le débogage
  MobileSendNewPasswordRepository repository =
      MobileSendNewPasswordRepository();
  // print("ins : $ins");
  // print("OTP : $OTP");
  //print("RESULT : $result");
  String AppToken = await FirebaseNotificationService.instance
      .getDeviceToken(); //ligne à modifier pour changer appToken

  StatusResponse reponse = await repository.sendOTP(ins, OTP, AppToken, login);
  Map<String, dynamic> reponseJson = reponse.toJson();
  // print(reponseJson);
  if (reponseJson['valide'] == "OK") {
    otp = false;
    BlocProvider.of<BlocAuthentication>(context)
        .add(EventAuthenticationIdentityConfirmed());
  }
  return null;
}

/* void _showConfirmationDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: Container(
          padding: EdgeInsets.all(30.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 0.0,
                blurRadius: 10.0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                'Modifier mot de passe',
                style: TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
              Text(
                  "Pour des raisons de sécurité, votre administrateur souhaite que vous changiez votre mot de passe périodiquement.",
                  style: TextStyle(
                      color: Colors.black54,
                      fontSize: 14.0,
                      fontWeight: FontWeight.w600)),
              SizedBox(height: 15),
              TextFormField(
                decoration: InputDecoration(
                  labelText: 'Mot de passe ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: true,
                style: TextStyle(fontSize: 18),
              ),
              SizedBox(height: 15),
              TextFormField(
                decoration: InputDecoration(
                  labelText: 'Confirmer mot de passe',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  contentPadding: EdgeInsets.all(20.0),
                ),
                obscureText: true,
                style: TextStyle(fontSize: 18),
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      textStyle: TextStyle(fontSize: 16),
                    ),
                    child: Text('Confirmer'),
                    onPressed: () async {
                      Future.delayed(Duration(seconds: 2), () {
                        Navigator.of(context).pop();
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
} */

/* void _sendOTP(String email, String ins, String login) async {
  print("ins + $ins, email : $email, login : $login");
  MobileResetPasswordRepository passwordResetRepo =
      MobileResetPasswordRepository();
  var result = await passwordResetRepo.sendResetPassword(ins, login, email);
  print(result);
} */
