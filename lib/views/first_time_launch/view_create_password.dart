import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/settings/password/BlocModifyPassword.dart';
import 'package:theradom/blocs/settings/password/EventModifyPassword.dart';
import 'package:theradom/blocs/settings/password/StateModifyPassword.dart';
import 'package:theradom/config.dart';

import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/buttons/large_button.dart';

class ViewCreatePassword extends StatefulWidget {
  final String userIdentity;

  ViewCreatePassword({required this.userIdentity});

  _ViewCreatePasswordState createState() => _ViewCreatePasswordState();
}

class _ViewCreatePasswordState extends State<ViewCreatePassword> {
  final BlocModifyPassword _blocModifyPassword = BlocModifyPassword();
  final _formKey = GlobalKey<FormState>();
  final _pwdController = TextEditingController();
  final _pwdConfirmController = TextEditingController();
  final FocusNode _secondFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener(
      bloc: _blocModifyPassword,
      listener: (context, state) async {
        if (state is StatePasswordModified)
          await _onPasswordModifiedNewState(state);
      },
      child: GestureDetector(
        onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
        child: Scaffold(
            resizeToAvoidBottomInset: false,
            backgroundColor: AppTheme.primaryColor,
            appBar: AppBar(
                elevation: 0,
                toolbarHeight: 0,
                backgroundColor: AppTheme.primaryColor,
                systemOverlayStyle: SystemUiOverlayStyle.light),
            body: LayoutBuilder(builder: (context, constraints) {
              return SingleChildScrollView(
                  child: ConstrainedBox(
                      constraints: BoxConstraints(
                          minWidth: constraints.maxWidth,
                          minHeight: constraints.maxHeight - 30),
                      child: IntrinsicHeight(
                        child:
                            Column(mainAxisSize: MainAxisSize.max, children: [
                          Padding(
                            padding: EdgeInsets.only(top: 50.0),
                            child: Text(
                              allTranslations.text('welcome'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: AppTheme.backgroundColor,
                                  fontSize: 30.0,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 0.0),
                            child: Text(
                              widget.userIdentity,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: AppTheme.backgroundColor,
                                fontSize: 20.0,
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(20.0),
                            margin: EdgeInsets.only(top: 20.0, bottom: 35.0),
                            height: 80.0,
                            width: 80.0,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppTheme.backgroundColor),
                            child: SvgPicture.asset(
                                "assets/images/icons/icSeanceLocked.svg"),
                          ),
                          Padding(
                            padding: EdgeInsets.all(20.0),
                            child: Text(
                              allTranslations.text('create_pwd_msg'),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: AppTheme.backgroundColor,
                                  fontSize: 18.0),
                            ),
                          ),
                          Expanded(
                            child: Form(
                              key: _formKey,
                              child: Container(
                                padding:
                                    EdgeInsets.only(left: 20.0, right: 20.0),
                                color: AppTheme.primaryColor,
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(bottom: 1.0),
                                      height: 64.0,
                                      decoration: BoxDecoration(
                                          color: AppTheme.backgroundColor,
                                          borderRadius:
                                              BorderRadius.circular(16.0),
                                          boxShadow: [
                                            BoxShadow(
                                                blurRadius: 20.0,
                                                spreadRadius: 0.0,
                                                color: AppTheme.primaryBlurColor
                                                    .withOpacity(0.5))
                                          ]),
                                      child: Center(
                                        child: TextFormField(
                                            controller: _pwdController,
                                            enableInteractiveSelection: false,
                                            obscureText: true,
                                            autocorrect: false,
                                            maxLines: 1,
                                            keyboardType:
                                                TextInputType.visiblePassword,
                                            textInputAction:
                                                TextInputAction.next,
                                            cursorColor: AppTheme.primaryColor,
                                            style: TextStyle(
                                              // style du texte renseigné
                                              color: AppTheme.primaryColor,
                                              fontSize: 18.0,
                                            ),
                                            decoration: InputDecoration(
                                              hintText: allTranslations
                                                  .text('new_pwd'),
                                              hintMaxLines: 1,
                                              hintStyle: TextStyle(
                                                  color: AppTheme.primaryColor,
                                                  fontSize: 16.0,
                                                  fontWeight: FontWeight.w600),
                                              contentPadding:
                                                  EdgeInsets.only(left: 20.0),
                                              border: InputBorder.none,
                                              errorStyle: TextStyle(
                                                  color: AppTheme.tertiaryColor,
                                                  fontSize: 14.0,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                            validator: _textfieldValidation,
                                            onFieldSubmitted: (String value) =>
                                                FocusScope.of(context)
                                                    .requestFocus(
                                                        _secondFocusNode)),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 20.0,
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(bottom: 1.0),
                                      height: 64.0,
                                      decoration: BoxDecoration(
                                          color: AppTheme.backgroundColor,
                                          borderRadius:
                                              BorderRadius.circular(16.0),
                                          boxShadow: [
                                            BoxShadow(
                                                blurRadius: 20.0,
                                                spreadRadius: 0.0,
                                                color: AppTheme.primaryBlurColor
                                                    .withOpacity(0.5))
                                          ]),
                                      child: Center(
                                        child: TextFormField(
                                            controller: _pwdConfirmController,
                                            focusNode: _secondFocusNode,
                                            enableInteractiveSelection: false,
                                            obscureText: true,
                                            autocorrect: false,
                                            maxLines: 1,
                                            keyboardType:
                                                TextInputType.visiblePassword,
                                            textInputAction:
                                                TextInputAction.done,
                                            cursorColor: AppTheme.primaryColor,
                                            style: TextStyle(
                                              // style du texte renseigné
                                              color: AppTheme.primaryColor,
                                              fontSize: 18.0,
                                            ),
                                            decoration: InputDecoration(
                                              hintText: allTranslations
                                                  .text('confirm_new_pwd'),
                                              hintMaxLines: 1,
                                              hintStyle: TextStyle(
                                                  color: AppTheme.primaryColor,
                                                  fontSize: 16.0,
                                                  fontWeight: FontWeight.w600),
                                              contentPadding:
                                                  EdgeInsets.only(left: 20.0),
                                              border: InputBorder.none,
                                              errorStyle: TextStyle(
                                                  color: AppTheme.tertiaryColor,
                                                  fontSize: 14.0,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                            validator: _textfieldValidation,
                                            onFieldSubmitted: (String value) =>
                                                _validateFields()),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 30,
                          ),
                          LargeButton(
                              title: allTranslations.text('to_validate'),
                              titleColor: AppTheme.primaryColor,
                              backgroundColor: AppTheme.secondaryColor,
                              showShadow: false,
                              onTap: _validateFields),
                          SizedBox(height: 15.0)
                        ]),
                      )));
            })),
      ),
    );
  }

  void _validateFields() {
    // valide les zones du formulaire
    if (_formKey.currentState!.validate()) {
      _launchChangePasswordEvent();
    } else {
      HapticFeedback.vibrate();
    }
  }

  void _launchChangePasswordEvent() {
    // lance l'event de validation du mot de passe
    _blocModifyPassword.add(EventModifyPasswordValidate(
        newPassword: _pwdController.text,
        newPasswordConfirmation: _pwdConfirmController.text));
  }

  String? _textfieldValidation(String? value) {
    // validator des textfields
    if (value != null) {
      if (value.isEmpty) {
        // si non rensigné
        return allTranslations.text("not_specified");
      } else if (value.replaceAll(RegExp(r"\s+"), "") == "") {
        // si c'est vide
        return allTranslations.text("format_not_valid");
      }
    }
    return null;
  }

  Future<void> _onPasswordModifiedNewState(StatePasswordModified state) async {
    if (state.loading) {
      AlertBox(context: context, title: allTranslations.text('please_wait'))
          .showLoading();
    } else {
      Navigator.of(context).pop();
      if (state.success!) {
        Config.firstUseState = 1;
        await SharedPreferencesUtil.instance
            .setInt(Config.prefFirstUseState, 1);
        BlocProvider.of<BlocAuthentication>(context).add(EventAppStarted());
      } else {
        AlertBox(context: context, title: "", description: state.message)
            .showFlushbar(warning: !state.success!);
      }
    }
  }
}
