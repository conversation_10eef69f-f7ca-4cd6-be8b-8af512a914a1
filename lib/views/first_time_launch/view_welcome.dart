import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/login/NewPasswordDialog.dart';
import 'package:theradom/blocs/qrcode/BlocQrCode.dart';
import 'package:theradom/blocs/qrcode/EventQrCode.dart';
import 'package:theradom/blocs/qrcode/QRcodeBlocProvider.dart';
import 'package:theradom/blocs/qrcode/StateQrCode.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/qrcode/ViewQRcode.dart';
import 'package:theradom/widgets/buttons/large_button.dart';
import 'package:theradom/services/repositories/mobile_status_repository.dart';
import 'package:theradom/config.dart';
import 'dart:convert';
import 'package:theradom/utils/SharedPreferences.dart';

// // class ViewWelcome extends StatelessWidget {
// //   final MobileStatusRepository _mobileStatusRepository = MobileStatusRepository();

// //   ViewWelcome({Key? key}) : super(key: key);

// //   // @override
// //   // Widget build(BuildContext context) {
// //   //   return Scaffold(
// //   //     appBar: AppBar(backgroundColor: AppTheme.primaryColor, toolbarHeight: 0, elevation: 0, systemOverlayStyle: SystemUiOverlayStyle.light),
// //   //     body: Container(
// //   //       padding: EdgeInsets.only(top: 30.0, left: 10.0, right: 10.0, bottom: 10.0),
// //   //       color: AppTheme.primaryColor,
// //   //       child: Column(
// //   //         children: [
// //   //           Text(
// //   //             allTranslations.text('hello'),
// //   //             textAlign: TextAlign.center,
// //   //             style: TextStyle(color: AppTheme.backgroundColor, fontSize: 30.0, fontWeight: FontWeight.bold),
// //   //           ),
// //   //           Expanded(
// //   //             child: SingleChildScrollView(
// //   //               padding: EdgeInsets.only(top: 30.0, bottom: 30.0, left: 10.0, right: 10.0),
// //   //               child: Column(
// //   //                 children: [
// //   //                   Image(
// //   //                     image: AssetImage("assets/images/first_launch/imgScan.png"),
// //   //                   ),
// //   //                   Padding(
// //   //                     padding: EdgeInsets.all(10.0),
// //   //                     child: Text(
// //   //                       Config.firstUseState! >= 1 ? allTranslations.text('welcome_msg2') : allTranslations.text('welcome_msg'),
// //   //                       textAlign: TextAlign.center,
// //   //                       style: TextStyle(color: AppTheme.backgroundColor, fontSize: 18.0),
// //   //                     ),
// //   //                   ),
// //   //                 ],
// //   //               ),
// //   //             ),
// //   //           ),
// //   //           LargeButton(
// //   //               title: Config.firstUseState! >= 1 ? allTranslations.text('continue') : allTranslations.text('scan'),
// //   //               titleColor: AppTheme.primaryColor,
// //   //               backgroundColor: AppTheme.secondaryColor,
// //   //               showShadow: false,
// //   //               onTap: () async => await _pushToViewQrCodeAndLaunchChangePasswordEventOnReturn(context))
// //   //         ],
// //   //       ),
// //   //     ),
// //   //   );
// //   // }

// //   Future<void> _pushToViewQrCodeAndLaunchChangePasswordEventOnReturn(
// //     BuildContext context,
// //   ) async {
// //     final parentContext = context;

// //     String? userIdentity = await Navigator.push(
// //       context,
// //       CupertinoPageRoute(
// //         builder: (context) => QrCodeBlocProvider(
// //           action: 0,
// //           child: ViewQRcode(),
// //         ),
// //       ),
// //     );

// //     String ins = Config.userAccountInfos!.INS;
// //     String login = Config.userAccountInfos!.login;

// //     showDialog(
// //       context: context,
// //       barrierDismissible: false,
// //       builder: (_) => const Center(
// //         child: CircularProgressIndicator(),
// //       ),
// //     );

// //     // late String jsonResponse;
// //     // try {
// //     //   // jsonResponse = await _mobileStatusRepository.checkUserStatus(ins, login);
// //     //   jsonResponse = "";
// //     // } catch (error) {
// //     //   Navigator.of(context).pop();
// //     //   return;
// //     // }
// //     // Navigator.of(context).pop();

// //     // final decodedResponse = jsonDecode(jsonResponse) as Map<String, dynamic>;
// //     // if (decodedResponse["new_password_needed"] == true) {
// //     //   if (userIdentity != null) {
// //     //     _launchEventFirstPasswordChange(parentContext, userIdentity);
// //     //   }
// //     // } else {
// //     //   Config.firstUseState = 1;
// //     //   await SharedPreferencesUtil.instance.setInt(Config.prefFirstUseState, 1);
// //     //   BlocProvider.of<BlocAuthentication>(parentContext).add(EventAppStarted());
// //     // }
// //   }

// //   void _launchEventFirstPasswordChange(BuildContext context, String userIdentity) {
// //     BlocProvider.of<BlocAuthentication>(context).add(EventAuthenticationFirstPasswordChange(userIdentity: userIdentity));
// //   }
// // }

@override
class ViewWelcome extends StatelessWidget {
  ViewWelcome({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => BlocQrCode(
        action: 0,
        mobileStatusRepository: MobileStatusRepository(),
      ),
      child: _ViewWelcomeContent(),
    );
  }
}

class _ViewWelcomeContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BlocQrCode, StateQrCode>(
      listener: (context, state) {
        if (state is StateQrCodeUserStatusChecked) {
          Navigator.of(context).pop(); // Close the loading dialog

          if (state.newPasswordNeeded) {
            // Show the new password dialog and wait for it to be dismissed
            _showNewPasswordDialog(context, state.userIdentity).then((_) {
              // if (state.userIdentity.isEmpty) {
              //   _launchEventFirstPasswordChange(context, state.userIdentity);
              // }

              _eventAppStard(context);
              // Config.firstUseState = 1;
              // SharedPreferencesUtil.instance.setInt(Config.prefFirstUseState, 1);
              // BlocProvider.of<BlocAuthentication>(context).add(EventAppStarted());
            });
          } else {
            _eventAppStard(context);
            // Config.firstUseState = 1;
            // SharedPreferencesUtil.instance.setInt(Config.prefFirstUseState, 1);
            // BlocProvider.of<BlocAuthentication>(context).add(EventAppStarted());
          }

          // // Show the new password dialog and wait for it to be dismissed
          // _showNewPasswordDialog(context, state.userIdentity).then((_) {
          //   if (state.newPasswordNeeded) {
          //     if (state.userIdentity.isEmpty) {
          //       _launchEventFirstPasswordChange(context, state.userIdentity);
          //     }
          //   } else {
          //     Config.firstUseState = 1;
          //     SharedPreferencesUtil.instance.setInt(Config.prefFirstUseState, 1);
          //     BlocProvider.of<BlocAuthentication>(context).add(EventAppStarted());
          //   }
          // });
        } else if (state is StateQrCodeError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('An error occurred: ${state.error}')),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: AppTheme.primaryColor,
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.light,
          ),
          body: Container(
            padding: EdgeInsets.only(
                top: 30.0, left: 10.0, right: 10.0, bottom: 10.0),
            color: AppTheme.primaryColor,
            child: Column(
              children: [
                Text(
                  allTranslations.text('hello'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: AppTheme.backgroundColor,
                      fontSize: 30.0,
                      fontWeight: FontWeight.bold),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                        top: 30.0, bottom: 30.0, left: 10.0, right: 10.0),
                    child: Column(
                      children: [
                        Image(
                          image: AssetImage(
                              "assets/images/first_launch/imgScan.png"),
                        ),
                        Padding(
                          padding: EdgeInsets.all(10.0),
                          child: Text(
                            Config.firstUseState! >= 1
                                ? allTranslations.text('welcome_msg2')
                                : allTranslations.text('welcome_msg'),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: AppTheme.backgroundColor,
                                fontSize: 18.0),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                LargeButton(
                  title: Config.firstUseState! >= 1
                      ? allTranslations.text('continue')
                      : allTranslations.text('scan'),
                  titleColor: AppTheme.primaryColor,
                  backgroundColor: AppTheme.secondaryColor,
                  showShadow: false,
                  onTap: () =>
                      _pushToViewQrCodeAndLaunchChangePasswordEventOnReturn(
                          context),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  /// Navigates to QR code scanning screen and handles the returned user identity
  /// Shows loading dialog while checking user status
  /// @param context BuildContext to access the bloc and navigation
  Future<void> _pushToViewQrCodeAndLaunchChangePasswordEventOnReturn(
      BuildContext context) async {
    final blocQrCode = BlocProvider.of<BlocQrCode>(context);

    final userIdentity = await Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => BlocProvider.value(
          value: blocQrCode,
          child: ViewQRcode(),
        ),
      ),
    );

    if (userIdentity != null) {
      String ins = Config.userAccountInfos!.INS;
      String login = Config.userAccountInfos!.login;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
  }

  /// Cette méthode affiche la boîte de dialogue de nouveau mot de passe et attend que l'utilisateur la ferme.
  /// Displays modal dialog for new password input
  /// @param context BuildContext for showing the dialog
  /// @param userIdentity String containing the user's identity/email
  /// @return Future that completes when dialog is closed
  Future<void> _showNewPasswordDialog(
      BuildContext context, String userIdentity) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return NewPasswordDialog(emailParam: userIdentity);
      },
    );
  }

  /// Updates first use state and triggers app started event
  /// Sets firstUseState to 1 in both Config and SharedPreferences
  /// @param context BuildContext to access the authentication bloc
  void _eventAppStard(BuildContext context) {
    Config.firstUseState = 1;
    SharedPreferencesUtil.instance.setInt(Config.prefFirstUseState, 1);
    BlocProvider.of<BlocAuthentication>(context).add(EventAppStarted());
  }
}
