import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/calendar/bloc_calendar.dart';
import 'package:theradom/blocs/calendar/state_calendar.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/calendar/view_calendar.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/background.dart';

/// BlocListener et BlocBuilder pour BlocCalendar

class ViewCalendarGeneral extends StatefulWidget {
  final void Function() updateNotificationBubble;

  ViewCalendarGeneral({required this.updateNotificationBubble});

  _ViewCalendarGeneralState createState() => _ViewCalendarGeneralState();
}

class _ViewCalendarGeneralState extends State<ViewCalendarGeneral> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<BlocCalendar, StateCalendar>(
      listener: (context, state) {
        if (state is StateCalendarAcknowledgeAppointment) {
          // met à jour la bulle calendrier dans le menu
          if (state.success) {
            widget.updateNotificationBubble();
          }
        }

        if (state is StateCalendarLoadingAppointments) {
          // affiche un dialogue de chargement et le cache
          if (state.loading) {
            AlertBox(
                    context: context,
                    title: allTranslations.text('please_wait'))
                .showLoading();
          } else {
            Navigator.of(context).pop();
            setState(() {});
          }
        }
      },
      child: BlocBuilder<BlocCalendar, StateCalendar>(
        buildWhen: (previousState, state) {
          bool rebuild;
          if ((state is StateCalendarAcknowledgeAppointment) ||
              (state is StateCalendarLoadingAppointments) ||
              (state is StateCalendarNewAppointmentsToAdd)) {
            rebuild = false;
          } else {
            rebuild = true;
          }
          return rebuild;
        },
        builder: (context, state) {
          Widget _screen = Container();

          if (state is StateCalendarUninitialized) {
            _screen = PlatformCircularIndicator(darkIndicator: false);
          }

          if (state is StateCalendarMainScreenInit) {
            _screen = ViewCalendar(
                mapAppointment: state.map,
                initShortestSide: MediaQuery.of(context).size.shortestSide);
          }

          if (state is StateCalendarError) {
            _screen = Center(
              child: Text(
                state.message,
                style: TextStyle(
                    fontSize: FontSizeController.of(context).fontSize,
                    color: AppTheme.backgroundColor),
              ),
            );
          }

          return Background(child: _screen);
        },
      ),
    );
  }
}
