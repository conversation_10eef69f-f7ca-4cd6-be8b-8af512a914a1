import 'package:add_2_calendar/add_2_calendar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/blocs/calendar/event_calendar.dart';
import 'package:theradom/blocs/calendar/state_calendar.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/appointment.dart';
import 'package:theradom/utils/UrlLauncherUtils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/blocs/calendar/bloc_calendar.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'package:theradom/extensions/datetime_extension.dart';

/// Vue du calendrier et des évenements associés
class ViewCalendar extends StatefulWidget {
  final Map<DateTime, List<Appointment>> mapAppointment;
  final double initShortestSide;

  ViewCalendar({required this.mapAppointment, required this.initShortestSide});

  _ViewCalendarState createState() => _ViewCalendarState();
}

class _ViewCalendarState extends State<ViewCalendar>
    with SingleTickerProviderStateMixin {
  late Map<DateTime, List<Appointment>> _mapAppointments;
  late final BlocCalendar _blocCalendar;
  late DateTime _selectedDay;
  late List<dynamic> _selectedEvents;
  late AnimationController _animationController;
  late Map<CalendarFormat, String> _availableCalendarFormat;
  DateTime _todayDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _blocCalendar = BlocProvider.of<BlocCalendar>(context);
    DateTime now = DateTime.now();
    _selectedDay = DateTime(now.year, now.month, now.day);
    _mapAppointments = widget.mapAppointment;
    _selectedEvents = _mapAppointments[_selectedDay] ?? [];
    _availableCalendarFormat = const {CalendarFormat.month: ''};
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onVisibleDaysChanged(DateTime firstDay) async {
    // appelé sur un changement de mois

    DateTime lastDay = DateTime(firstDay.year, firstDay.month, 0);

    _launchEventGetAllUserAppointments(firstDay, lastDay);

    // comme la fonction est appelée quand je change l'orientation de l'écran
    DateTime date;
    if ((_selectedDay.isBefore(lastDay)) && _selectedDay.isAfter(firstDay)) {
      // si le jour déjà sélectionné est dans le mois affché
      date = _selectedDay;
    } else {
      // si il n'y a pas de jour séléectionné pour la nouvelle vue
      date = DateTime(firstDay.year, firstDay.month, firstDay.day);
    }
    _selectDateAndEvents(date);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<BlocCalendar, StateCalendar>(
        bloc: BlocProvider.of<BlocCalendar>(context),
        listener: (previousState, state) {
          if (state is StateCalendarNewAppointmentsToAdd) {
            // ajout des nouveaux rdv à la map des evenements du patient
            _mapAppointments.addAll(state.map);
            _selectDateAndEvents(_selectedDay);
          }
        },
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: SingleChildScrollView(
            child: Column(
              children: [
                _buildTableCalendarWithBuilders(),
                _buildEventList(),
                SizedBox(
                  height: 20.0,
                )
              ],
            ),
          ),
        ));
  }

  Widget _buildTableCalendarWithBuilders() {
    // build du widget calendrier

    return TableCalendar<Appointment>(
        rowHeight: 50, // 55
        locale: allTranslations.currentLanguage,
        firstDay: DateTime(1900, 1, 1),
        lastDay: DateTime(3000, 1, 1),
        focusedDay: _selectedDay,
        calendarFormat: CalendarFormat.month,
        formatAnimationCurve: Curves.easeInCubic,
        formatAnimationDuration: Duration(seconds: 1),
        startingDayOfWeek: StartingDayOfWeek.monday,
        availableGestures: AvailableGestures.horizontalSwipe,
        availableCalendarFormats: _availableCalendarFormat,
        calendarStyle: CalendarStyle(
          outsideDaysVisible: false,
          canMarkersOverflow: false,
        ),
        daysOfWeekHeight: 30,
        daysOfWeekStyle: DaysOfWeekStyle(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(8.0),
            ),
            color: AppTheme.primaryColorDarker,
          ),
          dowTextFormatter: (date, locale) {
            String dayStr = DateFormat.E(locale).format(date);
            return dayStr[0].toUpperCase() + dayStr.substring(1, 3);
          },
          weekendStyle: TextStyle(
              fontSize: FontSizeController.of(context).fontSize,
              color: AppTheme.backgroundColor),
          weekdayStyle: TextStyle(
              fontSize: FontSizeController.of(context).fontSize,
              color: AppTheme.backgroundColor),
        ),
        headerStyle: HeaderStyle(
            titleTextStyle: TextStyle(
                fontSize: FontSizeController.of(context).fontSize,
                color: AppTheme.backgroundColor,
                fontFamily: "openSans-SemiBold",
                fontWeight: FontWeight.w500),
            titleTextFormatter: (date, locale) {
              String str = DateFormat.yMMMM(locale).format(date);
              return str[0].toUpperCase() + str.substring(1);
            },
            leftChevronIcon: Icon(FontAwesomeIcons.circleChevronLeft,
                color: AppTheme.backgroundColor, size: 22),
            rightChevronIcon: Icon(FontAwesomeIcons.circleChevronRight,
                color: AppTheme.backgroundColor, size: 22),
            headerPadding: EdgeInsets.only(top: 15.0, bottom: 15.0),
            titleCentered: true,
            formatButtonVisible: false),
        calendarBuilders: CalendarBuilders(selectedBuilder: (context, date, _) {
          // build jour sélectionné par le user
          return FadeTransition(
            opacity: Tween(begin: 0.0, end: 1.0).animate(_animationController),
            child: Container(
              height: 55,
              margin: EdgeInsets.only(top: 5.0, bottom: 8.0),
              decoration: BoxDecoration(
                  border: Border.all(color: AppTheme.primaryColor),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.secondaryBlurColor.withOpacity(1),
                      spreadRadius: 2,
                      blurRadius: 15,
                      offset: Offset(0, 0), // changes position of shadow
                    )
                  ]),
              child: Container(
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(colors: [
                      AppTheme.secondaryColorDarker,
                      AppTheme.secondaryColor
                    ])),
                child: Center(
                  child: Text('${date.day}',
                      style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: FontSizeController.of(context).fontSize,
                          fontWeight: FontWeight.w600,
                          fontFamily: "OpenSans-SemiBold")),
                ),
              ),
            ),
          );
        }, todayBuilder: (context, day, focusedDay) {
          return Container(
            margin: EdgeInsets.only(top: 5.0, bottom: 8.0),
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                    color: Theme.of(context).colorScheme.onSecondary)),
            child: Center(
              child: Text(
                '${day.day}',
                style: TextStyle(
                    fontSize: FontSizeController.of(context).fontSize,
                    color: Theme.of(context).scaffoldBackgroundColor,
                    fontFamily: "OpenSans-SemiBold",
                    fontWeight: FontWeight.w600),
              ),
            ),
          );
        }, defaultBuilder: (context, date, _) {
          // build un jour dans le calendrier
          DateTime currentDateTime = DateTime(date.year, date.month, date.day);
          List currentEvents = _mapAppointments[currentDateTime] ?? [];
          return Container(
            margin: EdgeInsets.only(top: 5.0, bottom: 8.0),
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                    color: currentEvents.length > 0
                        ? AppTheme.primaryColorDarker
                        : Colors.transparent)),
            child: Center(
              child: Text(
                '${date.day}',
                style: TextStyle(
                    fontSize: FontSizeController.of(context).fontSize,
                    color: AppTheme.backgroundColor,
                    fontFamily: "OpenSans-SemiBold",
                    fontWeight: FontWeight.w500),
              ),
            ),
          );
        }, markerBuilder: (context, date, _) {
          // build des marker dans les cellules

          // check pour savoir si les évènements ont tous été vus
          DateTime currentDateTime = DateTime(date.year, date.month, date.day);
          List<Appointment> currentEvents =
              _mapAppointments[currentDateTime] ?? [];
          bool showBubble = false;
          for (Appointment appointment in currentEvents) {
            if (appointment.messDelivrer == false) {
              showBubble = true;
              break;
            }
          }

          final children = <Widget>[];
          if (currentEvents.isNotEmpty) {
            if (showBubble) {
              children.add(Container(
                  padding: EdgeInsets.only(right: 10.0, top: 5.0),
                  child: Align(
                      alignment: Alignment.topRight,
                      child: Container(
                          width: 8.0,
                          height: 8.0,
                          child: Container(
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: AppTheme.primaryColorDarker),
                                color: AppTheme.secondaryColor),
                          )))));
            } else {
              children.add(Container(width: 8.0));
            }

            List<Widget> rowWidget = [];
            currentEvents.forEach((event) {
              rowWidget.add(_buildAppointmentMarker(date, currentEvents));
              if (event.teleconsultation) {
                rowWidget.add(_buildCameraMarker(date));
              } else {
                rowWidget.add(Container(width: 19));
              }
            });
            children.add(Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: rowWidget));

            return Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: children);
          }

          return null;
        }),
        selectedDayPredicate: (day) => day.isSameDate(_selectedDay),
        onDaySelected: (selectedDay, _) {
          _selectDateAndEvents(selectedDay);
          _animationController.forward(from: 0.0);
        },
        onPageChanged: _onVisibleDaysChanged
        //onVisibleDaysChanged: _onVisibleDaysChanged
        );
  }

  Widget _buildAppointmentMarker(DateTime date, List events) {
    // build le marker indiquant le nb de rdv à une date
    return SizedBox(
      height: 19.0,
      width: 19.0,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppTheme.primaryColorDarker,
        ),
        child: Center(
          child: Text(
            '${events.length}',
            style: TextStyle(
                color: AppTheme.backgroundColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
                fontFamily: "OpenSans-SemiBold"),
          ),
        ),
      ),
    );
  }

  Widget _buildCameraMarker(DateTime date) {
    // build le marker indiquant si une teleconsultation a lieu à une date
    return SizedBox(
      height: 19.0,
      width: 19.0,
      child: Container(
        decoration: BoxDecoration(
            shape: BoxShape.circle, color: AppTheme.primaryColorDarker),
        child: Center(
          child: Icon(FontAwesomeIcons.video,
              size: 9, color: AppTheme.backgroundColor),
        ),
      ),
    );
  }

  Widget _buildEventList() {
    // créé la liste pour les évènements de la date séléectionnée
    return Container(
        padding: EdgeInsets.only(top: 15),
        child: CustomScrollbar(
          scrollbarColor: AppTheme.backgroundColor,
          child: ListView(
              primary: false,
              physics: ClampingScrollPhysics(),
              shrinkWrap: true,
              children: List.generate(_selectedEvents.length,
                  (index) => _buildAppointmentTile(_selectedEvents[index]))),
        ));
  }

  Widget _buildAppointmentTile(Appointment appointment) {
    // build une tile qui représente un rdv

    return BlocListener<BlocCalendar, StateCalendar>(
      listener: (context, state) {
        if (state is StateCalendarAcknowledgeAppointment) {
          // met à jour la bulle calendrier dans le menu et affiche le message de confirmation/erreur
          if (state.success) {
            _mapAppointments[state.dateTime]!.forEach((appointment) {
              if (appointment.cleFac == state.cleFac) {
                setState(() {
                  appointment.messDelivrer = true;
                });
              }
            });
          }
        }
      },
      child: Container(
        height: 100,
        padding:
            EdgeInsets.only(top: 12.0, bottom: 14.0, left: 22.0, right: 22.0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25.0),
            color: AppTheme.backgroundColor,
            border: Border.all(
                color: appointment.messDelivrer
                    ? AppTheme.backgroundColor
                    : AppTheme.secondaryColor,
                width: 3)),
        margin: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
        child: ListTile(
          contentPadding: EdgeInsets.only(bottom: 10.0),

          // title
          title: Text("${appointment.titre}",
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontSize: FontSizeController.of(context).fontSize,
                  fontFamily: "OpenSans-SemiBold",
                  fontWeight: FontWeight.w500),
              maxLines: 2),
          onTap: () => _openAppointment(appointment),

          // trailing
          trailing: appointment.teleconsultation
              ? SizedBox(
                  height: 40.0,
                  width: 40.0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryBlurColor.withOpacity(0.6),
                          spreadRadius: 0,
                          blurRadius: 15,
                          offset: Offset(0, 0), // changes position of shadow
                        )
                      ],
                    ),
                    child: Icon(FontAwesomeIcons.video,
                        color: AppTheme.primaryColor, size: 16),
                  ),
                )
              : null,
        ),
      ),
    );
  }

  void _addToCalendar(Appointment appointment) {
    // Ajoute ce RDV au calendrier de l'OS
    DateTime dateTime = appointment.date;
    DateTime startDateTime = DateTime(dateTime.year, dateTime.month,
        dateTime.day, appointment.time.hour, appointment.time.minute);
    DateTime endDateTime = DateTime(dateTime.year, dateTime.month,
        dateTime.day + 1, appointment.time.hour, appointment.time.minute);
    final Event event = Event(
        title: appointment.titre,
        description: appointment.commentaire,
        location: '',
        startDate: startDateTime,
        endDate: endDateTime);
    Add2Calendar.addEvent2Cal(event);
  }

  void _launchEventAcknowledgeAppointment(Appointment appointment) {
    // lance l'event pour ackowledger le rdv
    if (appointment.messDelivrer == false) {
      _blocCalendar.add(EventCalendarAcknowledgeAppointment(
          dateTime: _selectedDay, cleFac: appointment.cleFac));
    }
  }

  void _launchEventGetAllUserAppointments(DateTime firstDay, DateTime lastDay) {
    // lance l'event pour tous requeter tous les rdv
    _blocCalendar.add(EventCalendarGetAllUserAppointments(
        dateTimeLast: lastDay, dateTimeFirst: firstDay));
  }

  Future<void> _openAppointment(Appointment appointment) async {
    // ouvre un dialogue qui affiche le détail du rdv
    _launchEventAcknowledgeAppointment(appointment);
    bool canJoin = ((Config.notificationResponse!.teleconsultation != "") &&
            (appointment.date.isSameDate(_todayDate)))
        ? true
        : false;

    dynamic rep = await AlertBox(
            context: context,
            title: appointment.titre,
            description: appointment.toString())
        .showAppointment(appointment.teleconsultation, canJoin);

    switch (rep) {
      case 1:
        {
          // add to calendar button pressed
          _addToCalendar(appointment);
          _openAppointment(appointment);
        }
        break;
      case 2:
        {
          // launch teleconsult button pressed
          UrlLauncherUtils.launchURL(
              Config.notificationResponse!.teleconsultation);
        }
        break;
      default:
        {}
        break;
    }
  }

  void _selectDateAndEvents(DateTime selectedDay) {
    // sélectionne la date et les événements pour dateTime
    setState(() {
      _selectedDay = selectedDay;
      _selectedEvents = _mapAppointments[
              DateTime(selectedDay.year, selectedDay.month, selectedDay.day)] ??
          [];
    });
  }
}
