import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/documents/BlocDocuments.dart';
import 'package:theradom/blocs/documents/EventDocuments.dart';
import 'package:theradom/blocs/documents/StateDocuments.dart';

import 'package:theradom/config.dart';
import 'package:theradom/models/doc.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/documents/view_documents_tabview.dart';
import 'package:theradom/widgets/background_header.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/custom_app_bar.dart';
import 'package:theradom/widgets/tab_item.dart';

/// View générale des documents avec le blob builder et la tab bar

class ViewDocumentsGeneral extends StatefulWidget {
  final BlocDocuments blocDocuments;

  ViewDocumentsGeneral({required this.blocDocuments});

  @override
  _ViewDocumentsGeneralState createState() => _ViewDocumentsGeneralState();
}

class _ViewDocumentsGeneralState extends State<ViewDocumentsGeneral>
    with SingleTickerProviderStateMixin {
  late final BlocDocuments _blocDocuments;
  late final TabController _tabController;
  AutoSizeGroup _myGroup = AutoSizeGroup();
  String _tableReports = Config.tableFakeRapports;
  String _tablePdf = Config.tableFakeDocs;

  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
    _blocDocuments = widget.blocDocuments;
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();
    Config.blockBackBtnPop = true;
    _blocDocuments.add(EventDocumentsDocumentList());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        backBtnEnabled: true,
        callback: null,
      ),
      body: DefaultTabController(
        length: 2,
        initialIndex: 0,
        child: Background(
          child: Scaffold(
              backgroundColor: Colors.transparent,
              appBar: PreferredSize(
                preferredSize: Size.fromHeight(140),
                child: Column(
                  children: [
                    BackgroundHeader(
                      label: allTranslations.text('docs'),
                    ),
                    Container(
                        color: Colors.transparent,
                        margin: EdgeInsets.symmetric(horizontal: 10.0),
                        padding: EdgeInsets.all(10.0),
                        child: TabBar(
                            controller: _tabController,
                            indicator: BoxDecoration(
                                borderRadius: BorderRadius.circular(25.0),
                                color: AppTheme.secondaryColor),
                            labelColor: AppTheme.primaryColor,
                            unselectedLabelColor: AppTheme.backgroundColor,
                            indicatorSize: TabBarIndicatorSize.tab,
                            tabs: [
                              TabItem(
                                  title: allTranslations.text("report_plural"),
                                  group: _myGroup),
                              TabItem(title: "PDF", group: _myGroup)
                            ]))
                  ],
                ),
              ),
              body: BlocListener(
                bloc: _blocDocuments,
                listener: (context, state) {
                  if (state is StateDocumentsLoading) {
                    if (state.loading) {
                      AlertBox(
                              context: context,
                              title: allTranslations.text('please_wait'))
                          .showLoading();
                    } else {
                      Navigator.of(context).pop();
                      if (state.fail) {
                        AlertBox(
                                context: context,
                                title: allTranslations.text('error'),
                                description: state.message)
                            .showFlushbar(warning: true);
                      }
                    }
                  }
                },
                child: BlocBuilder(
                  bloc: _blocDocuments,
                  buildWhen: (previousState, state) {
                    bool rebuild;
                    if ((state is StateDocumentsUninitialized) ||
                        (state is StateDocumentsList)) {
                      rebuild = true;
                    } else {
                      rebuild = false;
                    }
                    return rebuild;
                  },
                  builder: (context, state) {
                    Widget screen = Container();

                    if (state is StateDocumentsList) {
                      // affichage de la tabview

                      screen = ViewDocumentsTabview(
                          tabController: _tabController,
                          pdfList: state.pdfList,
                          reportList: state.reportList,
                          openReport: _openReport,
                          openPdf: _openPdf,
                          pdfError: state.pdfErrorDetails == null
                              ? null
                              : state.pdfErrorDetails!.message,
                          reportError: state.reportErrorDetails == null
                              ? null
                              : state.reportErrorDetails!.message);
                    }

                    if (state is StateDocumentsUninitialized) {
                      // affichage chargement
                      screen = PlatformCircularIndicator(darkIndicator: false);
                    }

                    return screen;
                  },
                ),
              )),
        ),
      ),
    );
  }

  void _openReport(Doc doc) {
    // ouvre un rapport
    _blocDocuments
        .add(EventDocumentsGetDocument(name: doc.nom, table: _tableReports));
  }

  void _openPdf(Doc doc) {
    // ouvre un pdf
    _blocDocuments
        .add(EventDocumentsGetDocument(name: doc.nom, table: _tablePdf));
  }
}
