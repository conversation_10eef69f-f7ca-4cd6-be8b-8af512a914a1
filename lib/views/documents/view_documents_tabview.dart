import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/doc.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/documents/document_tile.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/error_text.dart';

/// Tabview des documents avec la view des rapports et la view des pdf

class ViewDocumentsTabview extends StatelessWidget {
  final tabController;
  final List<Doc> reportList;
  final List<Doc> pdfList;
  final void Function(Doc) openReport;
  final void Function(Doc) openPdf;
  final String? pdfError;
  final String? reportError;

  final tablePdf = Config.tableFakeDocs;

  ViewDocumentsTabview(
      {required this.tabController,
      required this.reportList,
      required this.pdfList,
      required this.openPdf,
      required this.openReport,
      this.reportError,
      this.pdfError});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: TabBarView(
        physics: NeverScrollableScrollPhysics(),
        controller: tabController,
        children: <Widget>[
          // rapports
          reportError == null
              ? reportList.length > 0
                  ? CustomScrollbar(
                      scrollbarColor: AppTheme.backgroundColor,
                      child: ListView.builder(
                        itemCount: reportList.length,
                        itemBuilder: (context, index) {
                          return DocumentTile(
                              isReport: true,
                              doc: reportList[index],
                              callbackOpenDoc: openReport);
                        },
                      ),
                    )
                  : ErrorText(
                      message: allTranslations.text("no_report_available"))
              : ErrorText(message: reportError!),

          // pdf
          pdfError == null
              ? pdfList.length > 0
                  ? CustomScrollbar(
                      scrollbarColor: AppTheme.backgroundColor,
                      child: ListView.builder(
                        itemCount: pdfList.length,
                        itemBuilder: (context, index) {
                          return DocumentTile(
                              isReport: false,
                              doc: pdfList[index],
                              callbackOpenDoc: openPdf);
                        },
                      ),
                    )
                  : ErrorText(message: allTranslations.text("no_pdf_available"))
              : ErrorText(message: reportError!)
        ],
      ),
    );
  }
}
