import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:theradom/models/biologic_data_content.dart';
import 'package:theradom/utils/change_orientation.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/expand_text.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/widgets/multiline_chart.dart';
import 'package:theradom/config.dart';
import 'package:fl_chart/fl_chart.dart';

/// Vue d'un graphique pour les données de bio et le commentaire associé

class ViewChartsBio extends StatefulWidget {
  final BiologicDataContent biologicDataContent;
  final List<dynamic> iLinedataSetList;
  final List<dynamic> limitLineList;

  ViewChartsBio({
    required this.biologicDataContent,
    required this.iLinedataSetList,
    required this.limitLineList,
  });

  @override
  _ViewChartsBioState createState() => _ViewChartsBioState();
}

class _ViewChartsBioState extends State<ViewChartsBio> {
  @override
  void initState() {
    super.initState();
    OrientationSetter.change.toLandscape();
    Config.blockBackBtnPop = false;
  }

  @override
  void dispose() {
    OrientationSetter.change.toPortrait();
    Config.blockBackBtnPop = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        OrientationSetter.change.toPortrait();
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          toolbarHeight: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        body: Container(
          color: AppTheme.primaryColor,
          child: Container(
            margin: EdgeInsets.all(10.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: AppTheme.backgroundColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Row(
                    children: [
                      Text(
                        widget.biologicDataContent.typeBilan,
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: FontSizeController.of(context).fontSize,
                        ),
                      ),
                      Spacer(),
                      SvgButton(
                        asset: "assets/images/icons/icReduceScreen.svg",
                        onTap: () {
                          OrientationSetter.change.toPortrait();
                          Navigator.of(context).pop();
                        },
                      ),
                    ],
                  ),
                ),
                widget.biologicDataContent.commentaire == ""
                    ? Container()
                    : Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.0),
                        child: ExpandText(
                          widget.biologicDataContent.commentaire,
                          maxLines: 1,
                          dividerColor: Colors.transparent,
                          style: TextStyle(
                            fontSize: FontSizeController.of(context).fontSize,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ),
                Expanded(
                  child: MultilineChart(
                    iLineDataSetList: widget.iLinedataSetList,
                    limitLineList2: widget.limitLineList,
                    enableGestures: true,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
