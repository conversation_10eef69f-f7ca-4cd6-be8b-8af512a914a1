import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/bilans_bio/event_bilans_bio.dart';
import 'package:theradom/blocs/bilans_bio/state_bilans_bio.dart';
import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/models/biologic_data_content.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/blocs/bilans_bio/bloc_bilans_bio.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/bilans_bio/ViewAddBilanBio.dart';
import 'package:theradom/views/bilans_bio/bio_table_element.dart';
import 'package:theradom/views/bilans_bio/bio_analysis_list.dart';
import 'package:theradom/widgets/bilans_bio/bio_result_element.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/bilans_bio/view_charts_bio.dart';
import 'package:theradom/widgets/DateRangeAction.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/buttons/fab.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/widgets/tab_item.dart';

/// Builder et listener pour BlocBilansBio et appBar avec widgets de filtre sur les données et de choix des dates

class ViewBilansBioGeneral extends StatefulWidget {
  ViewBilansBioGeneral();

  @override
  _ViewBilansBioGeneral createState() => _ViewBilansBioGeneral();
}

class _ViewBilansBioGeneral extends State<ViewBilansBioGeneral>
    with SingleTickerProviderStateMixin {
  late final BlocBilanBio _blocBilanBio;
  late DateTime _startDateTime;
  late DateTime _endDateTime = DateTime.now();
  late final TabController _tabController;
  int _tabIndex = 0;
  final AutoSizeGroup _myGroup = AutoSizeGroup();
  final TextEditingController _editingController = TextEditingController();
  late final ScrollController _scrollController;

  void initState() {
    super.initState();
    _blocBilanBio = BlocProvider.of<BlocBilanBio>(context);
    _startDateTime =
        DateTime(_endDateTime.year, _endDateTime.month - 1, _endDateTime.day);
    _scrollController = ScrollController(keepScrollOffset: true);
    _tabController =
        TabController(vsync: this, length: 2, initialIndex: _tabIndex);
    _tabController.addListener(_getFloatingActionButton);
  }

  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _unfocusTextfield,
      behavior: HitTestBehavior.translucent,
      child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: DefaultTabController(
            length: 2,
            initialIndex: 0,
            child: Background(
              child: Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: PreferredSize(
                    preferredSize: Size.fromHeight(125),
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.only(top: 10.0, bottom: 10.0),
                          child: Center(
                            child: Text(
                              allTranslations.text('txt_bio'),
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  color: AppTheme.backgroundColor,
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: "OpenSans"),
                            ),
                          ),
                        ),
                        Container(
                            color: Colors.transparent,
                            padding: EdgeInsets.all(10.0),
                            margin: EdgeInsets.symmetric(horizontal: 5.0),
                            child: TabBar(
                                controller: _tabController,
                                onTap: _selectTab,
                                indicator: BoxDecoration(
                                    borderRadius: BorderRadius.circular(25.0),
                                    color: AppTheme.secondaryColor),
                                labelColor: AppTheme.primaryColor,
                                unselectedLabelColor: AppTheme.backgroundColor,
                                indicatorSize: TabBarIndicatorSize.tab,
                                tabs: [
                                  TabItem(
                                      title: allTranslations.text("analysis"),
                                      group: _myGroup),
                                  TabItem(
                                      title: allTranslations.text("results"),
                                      group: _myGroup)
                                ]))
                      ],
                    ),
                  ),
                  body: BlocListener<BlocBilanBio, StateBilansBio>(
                    bloc: _blocBilanBio,
                    listener: (context, state) {
                      if (state is StateBilansBioLoading) {
                        // dialogue de chargement
                        if (state.loading) {
                          AlertBox(
                                  context: context,
                                  title: allTranslations.text('please_wait'))
                              .showLoading();
                        } else {
                          Navigator.of(context).pop();
                          _unfocusAndClearTextfield();
                        }
                      }

                      if (state is StateBilansBioShowCharts) {
                        // ouvrir un écran pour afficher le graphique

                        Navigator.push(
                            context,
                            CupertinoPageRoute(
                                builder: (context) => ViewChartsBio(
                                    biologicDataContent:
                                        state.biologicDataContent,
                                    iLinedataSetList: state.iLineDataSetList,
                                    limitLineList: state.limitLineList)));
                      }

                      if (state is StateBilansBioDeleted) {
                        // suppression d'un bilan bio
                        AlertBox(context: context, description: state.message)
                            .showFlushbar(warning: !state.success);
                        _launchEventSearchBilansBio();
                      }

                      if (state is StateBilansBioNotif) {
                        AlertBox(
                                context: context,
                                title: state.success
                                    ? ""
                                    : allTranslations.text('error'),
                                description: state.message)
                            .showFlushbar(warning: !state.success);
                        if (state.success) _launchEventSearchBilansBio();
                      }
                    },
                    child: BlocBuilder<BlocBilanBio, StateBilansBio>(
                      bloc: _blocBilanBio,
                      buildWhen: (previousState, state) {
                        bool rebuild;
                        if ((state is StateBilansBioLoading) ||
                            (state is StateBilansBioShowCharts) ||
                            (state is StateBilansBioDeleted) ||
                            (state is StateBilansBioNotif)) {
                          rebuild = false;
                        } else {
                          rebuild = true;
                        }
                        return rebuild;
                      },
                      builder: (context, state) {
                        Widget _screen = Container();

                        if (state is StateBilansBioUninitialised) {
                          // indicateur de chargement lors de l'initialisation
                          _screen =
                              PlatformCircularIndicator(darkIndicator: false);
                        }

                        if (state is StateBilansBioDataTypePicked) {
                          if (state.error == null) {
                            _screen = TabBarView(
                                physics: NeverScrollableScrollPhysics(),
                                controller: _tabController,
                                children: [
                                  state.biologicDataContentList.length > 0
                                      ? BioAnalysisList(
                                          textEditingController:
                                              _editingController,
                                          biologicDataContentList:
                                              state.biologicDataContentList,
                                          onAnalysisSelected:
                                              _launchOpenChartEvent,
                                          onOpenBiologicalDataContentType2:
                                              _onOpenBilanAction)
                                      : _buildEmptyPage(allTranslations
                                          .text('bio_no_analysis_msg')),
                                  state.biologicAnalysisDataList.length > 0
                                      ? CustomScrollbar(
                                          scrollbarColor:
                                              AppTheme.backgroundColor,
                                          child: ListView(
                                            children: List.generate(
                                                state.biologicAnalysisDataList
                                                    .length, (index) {
                                              return BioResultElement(
                                                  biologicAnalysisData: state
                                                          .biologicAnalysisDataList[
                                                      index],
                                                  onTap: () => _onOpenBilanAction(
                                                      state.biologicAnalysisDataList[
                                                          index]),
                                                  onDelete: () =>
                                                      _launchDeleteEvent(state
                                                              .biologicAnalysisDataList[
                                                          index]));
                                            }),
                                          ))
                                      : _buildEmptyPage(allTranslations
                                          .text('bio_no_report_msg'))
                                ]);
                          } else {
                            _screen = _buildEmptyPage(state.error!);
                          }
                        }

                        Widget widget;

                        if (_tabController.index == 0) {
                          widget = NestedScrollView(
                            controller: _scrollController,
                            headerSliverBuilder: (BuildContext context,
                                bool innerBoxIsScrolled) {
                              return <Widget>[
                                SliverAppBar(
                                  pinned: true,
                                  floating: true,
                                  snap: false,
                                  elevation: 0,
                                  forceElevated: innerBoxIsScrolled,
                                  expandedHeight: 130.0,
                                  flexibleSpace: FlexibleSpaceBar(
                                      collapseMode: CollapseMode.pin,
                                      background: Column(
                                        children: [
                                          DateRangeAction(
                                              startDate: _startDateTime,
                                              endDate: _endDateTime,
                                              onAction:
                                                  _clearSearchControllerAndLaunchSearchEvent,
                                              setDates: _setStartAndEndDates),
                                          SizedBox(height: 10.0),
                                          Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 10.0),
                                              child: TextField(
                                                controller: _editingController,
                                                cursorColor:
                                                    AppTheme.backgroundColor,
                                                enableInteractiveSelection:
                                                    true,
                                                style: TextStyle(
                                                    fontSize:
                                                        FontSizeController.of(
                                                                context)
                                                            .fontSize,
                                                    color: AppTheme
                                                        .backgroundColor),
                                                decoration: InputDecoration(
                                                    isDense: true,
                                                    contentPadding:
                                                        EdgeInsets.only(
                                                            top: 5.0,
                                                            bottom: 10.0,
                                                            right: 10.0,
                                                            left: 20.0),
                                                    suffixIcon: Container(
                                                      padding:
                                                          EdgeInsets.all(10.0),
                                                      child: SvgButton(
                                                        onTap: () =>
                                                            _editingController
                                                                .clear(),
                                                        asset:
                                                            "assets/images/icons/icClear.svg",
                                                        assetColor: AppTheme
                                                            .backgroundColor
                                                            .withOpacity(0.5),
                                                      ),
                                                    ),
                                                    filled: true,
                                                    fillColor: AppTheme
                                                        .primaryColorDarker,
                                                    hintText: allTranslations
                                                        .text("search"),
                                                    hintStyle: TextStyle(
                                                        color: AppTheme
                                                            .backgroundColor
                                                            .withOpacity(0.5),
                                                        fontSize:
                                                            FontSizeController.of(context)
                                                                .fontSize),
                                                    border: OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius.all(
                                                                Radius.circular(
                                                                    18.0)),
                                                        borderSide:
                                                            BorderSide.none)),
                                              ))
                                        ],
                                      )),
                                  bottom: PreferredSize(
                                    preferredSize: Size.fromHeight(20.0),
                                    child: Container(
                                      margin: EdgeInsets.only(bottom: 10.0),
                                      child: Container(
                                        height: 10,
                                        width: 45,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(25.0),
                                            color: AppTheme.backgroundColor
                                                .withOpacity(0.5)),
                                      ),
                                    ),
                                  ),
                                )
                              ];
                            },
                            body: _screen,
                          );
                        } else {
                          widget = NestedScrollView(
                              controller: _scrollController,
                              headerSliverBuilder: (BuildContext context,
                                  bool innerBoxIsScrolled) {
                                return <Widget>[
                                  SliverAppBar(
                                    pinned: true,
                                    floating: true,
                                    snap: false,
                                    elevation: 0,
                                    forceElevated: innerBoxIsScrolled,
                                    flexibleSpace: FlexibleSpaceBar(
                                        collapseMode: CollapseMode.pin,
                                        background: Column(
                                          children: [
                                            DateRangeAction(
                                                startDate: _startDateTime,
                                                endDate: _endDateTime,
                                                onAction:
                                                    _clearSearchControllerAndLaunchSearchEvent,
                                                setDates: _setStartAndEndDates),
                                          ],
                                        )),
                                    bottom: PreferredSize(
                                        preferredSize: Size.fromHeight(20.0),
                                        child: Container(
                                          margin: EdgeInsets.only(bottom: 10.0),
                                          child: Container(
                                            height: 10,
                                            width: 45,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(25.0),
                                                color: AppTheme.backgroundColor
                                                    .withOpacity(0.5)),
                                          ),
                                        )),
                                  )
                                ];
                              },
                              body: _screen);
                        }

                        return widget;
                      },
                    ),
                  )),
            ),
          ),
          floatingActionButton: _tabIndex == 0
              ? null
              : Padding(
                  padding: EdgeInsets.only(bottom: 80.0),
                  child: Fab(
                      svgPicture:
                          SvgPicture.asset("assets/images/icons/icAddFile.svg"),
                      onPressed: () => _openAddBilanBioPopUp()),
                )),
    );
  }

  Widget _buildEmptyPage(String error) {
    return Container(
      padding: EdgeInsets.all(20.0),
      child: Center(
        child: Text(
          error,
          textAlign: TextAlign.center,
          style: TextStyle(
              color: AppTheme.backgroundColor,
              fontSize: FontSizeController.of(context).fontSize),
        ),
      ),
    );
  }

  void _onOpenBilanAction(BiologicAnalysisData biologicAnalysisData) {
    // lance l'event pour ouvrir le bilan pdf ou affiche le tableau de données
    if (biologicAnalysisData.pdf == null) {
      // afficher un tableau
      Navigator.push(
        context,
        CupertinoPageRoute(
            builder: (context) =>
                BioTableElement(biologicAnalysisData: biologicAnalysisData)),
      );
    } else {
      // afficher un document pdf
      _launchGetBilanBioPdfEvent(biologicAnalysisData);
    }
  }

  void _launchGetBilanBioPdfEvent(BiologicAnalysisData biologicAnalysisData) {
    // lance l'event de requete d'un pdf de bio
    _blocBilanBio.add(EventBilanBioGetBilan(
        bilanDate: biologicAnalysisData.cDate,
        numEnreg: biologicAnalysisData.idPdf));
  }

  void _selectTab(int i) {
    _scrollController.jumpTo(0);
    switch (i) {
      case 0:
        {}
        break;
      case 1:
        {
          _unfocusAndClearTextfield();
        }
        break;
    }
  }

  void _clearSearchControllerAndLaunchSearchEvent() {
    // fonction lors du clic sur le bouton de recherche des analyses et bilans
    _editingController.clear(); // vide le filtre
    _launchEventSearchBilansBio();
  }

  void _launchEventSearchBilansBio() {
    // lance l'event de recherche des bilans biologique de _startDate à _endDate
    _blocBilanBio.add(
        EventBilansBioSearch(startDate: _startDateTime, endDate: _endDateTime));
  }

  void _launchOpenChartEvent(BiologicDataContent biologicDataContent) {
    // lance event d'ouverture du graphique
    BlocProvider.of<BlocBilanBio>(context)
        .add(EventBilanBioOpenCharts(biologicDataContent: biologicDataContent));
  }

  void _launchDeleteEvent(BiologicAnalysisData biologicAnalysisData) {
    // lance l'event de supression d'un bilan bio pdf
    _blocBilanBio.add(
        EventBilansBioDeleteBilan(biologicAnalysisData: biologicAnalysisData));
  }

  void _launchEventAddBilanBio(String path, DateTime date) {
    // lance l'event d'ajout d'un bilan bio
    _blocBilanBio.add(EventBilansBioAddBilan(path: path, date: date));
  }

  Future<void> _openAddBilanBioPopUp() async {
    // ouvre un dialogue pour ajouter un bilan bio
    List? list = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return ViewAddBilanBio();
        });
    if (list != null) {
      _launchEventAddBilanBio(list[0], list[1]);
    }
  }

  void _setStartAndEndDates(DateTime startDate, DateTime endDate) {
    // set les dates reçues en paramètre par le DateRangeAction
    _startDateTime = startDate;
    _endDateTime = endDate;
  }

  void _getFloatingActionButton() {
    // listener pour afficher ou non le bouton d'ajout d'un bilan bio, selon l'onglet sélectionné
    setState(() {
      _tabIndex = _tabController.index;
    });
  }

  void _unfocusAndClearTextfield() {
    _unfocusTextfield();
    _clearTextfield();
  }

  void _unfocusTextfield() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
  }

  void _clearTextfield() {
    _editingController.clear();
  }
}
