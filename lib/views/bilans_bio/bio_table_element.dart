import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/background.dart';
import 'package:theradom/widgets/background_header.dart';
import 'package:theradom/widgets/custom_app_bar.dart';
import 'package:theradom/widgets/custom_scroll_behavior.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';

/// Affiche un bialn biologique reçu sous forme d'un tableau

class BioTableElement extends StatefulWidget {
  final BiologicAnalysisData biologicAnalysisData;

  BioTableElement({required this.biologicAnalysisData});

  _BioTableElementState createState() => _BioTableElementState();
}

class _BioTableElementState extends State<BioTableElement> {
  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
  }

  @override
  void dispose() {
    Config.blockBackBtnPop = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        backBtnEnabled: true,
      ),
      body: Background(
          child: Column(
        children: [
          BackgroundHeader(
            label: widget.biologicAnalysisData.typeBilan,
          ),
          Expanded(
            child: ScrollConfiguration(
                behavior: CustomScrollBehavior(),
                child: CustomScrollbar(
                  scrollbarColor: AppTheme.primaryColor,
                  child: SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child: Column(children: _buildDataList())),
                )),
          )
        ],
      )),
    );
  }

  List<Widget> _buildDataList() {
    List<Widget> elemsList = [];

    AutoSizeGroup group = AutoSizeGroup();

    int i = 0;

    widget.biologicAnalysisData.donnees
        ?.forEach((dataName, biologicDataContent) {
      elemsList.add(Container(
          color: i % 2 == 0 ? AppTheme.backgroundColor : AppTheme.lightGrey,
          child: Column(
            children: [
              ListTile(
                  title: AutoSizeText(
                    dataName,
                    maxLines: 1,
                    maxFontSize: FontSizeController.of(context).fontSize,
                    style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  trailing: Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(3.14),
                    child: IconButton(
                        icon: Icon(
                          Icons.comment,
                          color: biologicDataContent.commentaire != ""
                              ? AppTheme.primaryColor
                              : Colors.transparent,
                        ),
                        onPressed: () => biologicDataContent.commentaire != ""
                            ? _showCommentDialog(
                                dataName, biologicDataContent.commentaire)
                            : null),
                  )),
              ListTile(
                  title: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          biologicDataContent.valeurAv != ""
                              ? AutoSizeText(
                                  allTranslations.text('before') +
                                      ": ${biologicDataContent.valeurAv}",
                                  group: group,
                                  maxFontSize:
                                      FontSizeController.of(context).fontSize,
                                  style: TextStyle(
                                    decoration: TextDecoration.underline,
                                    fontSize:
                                        FontSizeController.of(context).fontSize,
                                    color: AppTheme.primaryColor,
                                  ),
                                )
                              : Container(),
                          SizedBox(
                            width: 20.0,
                          ),
                          biologicDataContent.valeurAp != ""
                              ? AutoSizeText(
                                  allTranslations.text('after') +
                                      ": ${biologicDataContent.valeurAp}",
                                  group: group,
                                  maxFontSize:
                                      FontSizeController.of(context).fontSize,
                                  style: TextStyle(
                                    decoration: TextDecoration.underline,
                                    fontSize:
                                        FontSizeController.of(context).fontSize,
                                    color: AppTheme.primaryColor,
                                  ),
                                )
                              : Container(),
                          SizedBox(
                            width: 20.0,
                          ),
                          biologicDataContent.normale != ""
                              ? AutoSizeText(
                                  allTranslations.text('normal') +
                                      ": ${biologicDataContent.normale}",
                                  group: group,
                                  maxFontSize:
                                      FontSizeController.of(context).fontSize,
                                  style: TextStyle(
                                    decoration: TextDecoration.underline,
                                    fontSize:
                                        FontSizeController.of(context).fontSize,
                                    color: AppTheme.primaryColor,
                                  ),
                                )
                              : Container(),
                          SizedBox(
                            width: 20.0,
                          )
                        ],
                      )))
            ],
          )));
      i++;
    });

    return elemsList;
  }

  void _showCommentDialog(String title, String content) {
    // ouvre un dialogue affichant le commentaire associé à la donnée
    AlertBox(
            context: context,
            title: allTranslations.text('comment') + ": $title",
            description: content)
        .showInfo();
  }
}
