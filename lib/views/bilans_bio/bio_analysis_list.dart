import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/bilans_bio/bloc_bilans_bio.dart';
import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/models/biologic_data_content.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/widgets/bilans_bio/bio_analysis_element.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';

/// Liste avec filtre sur les titres des analyses biologiques

class BioAnalysisList extends StatefulWidget {
  final TextEditingController textEditingController;
  final List<BiologicDataContent> biologicDataContentList;
  final void Function(BiologicDataContent) onAnalysisSelected;
  final void Function(BiologicAnalysisData) onOpenBiologicalDataContentType2;

  BioAnalysisList(
      {required this.biologicDataContentList,
      required this.textEditingController,
      required this.onAnalysisSelected,
      required this.onOpenBiologicalDataContentType2});

  _BioAnalysisListState createState() => _BioAnalysisListState();
}

class _BioAnalysisListState extends State<BioAnalysisList> {
  late final TextEditingController _textEditingController;
  List<String> _duplicateItemStringList = [];
  List<String> _items = <String>[];
  late final BlocBilanBio _blocBilanBio;

  @override
  void initState() {
    _initStringLists();
    super.initState();
    _blocBilanBio = BlocProvider.of<BlocBilanBio>(context);
    _textEditingController = widget.textEditingController;
    _textEditingController
        .addListener(() => _filterSearchResults(_textEditingController.text));
  }

  @override
  void dispose() {
    _textEditingController.removeListener(
        () => _filterSearchResults(_textEditingController.text));
    super.dispose();
  }

  void _filterSearchResults(String query) {
    // filtre la recherche selon le contenu passé en paramètre
    List<String> dummySearchList = <String>[];
    dummySearchList.addAll(_duplicateItemStringList);
    if (query.isNotEmpty) {
      // quand le user saisi dans la zone de saisie, j'ajoute à dummySearchList
      List<String> dummyListData = <String>[];
      dummySearchList.forEach((item) {
        if (item.toLowerCase().contains(query)) {
          dummyListData.add(item);
        }
      });
      if (mounted) {
        setState(() {
          _items.clear();
          _items.addAll(dummyListData);
        });
      }
      return;
    } else {
      if (mounted) {
        setState(() {
          _items.clear();
          _items.addAll(_duplicateItemStringList);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: CustomScrollbar(
        scrollbarColor: AppTheme.backgroundColor,
        child: ListView(
            children: List.generate(_items.length, (index) {
          List<dynamic> repDataSet = _blocBilanBio
              .getBiologicDataContentToSeriesDoubleDouble(_items[index], false);
          return Container(
            padding: EdgeInsets.all(5.0),
            child: BioAnalysisElement(
                biologicDataContent: widget.biologicDataContentList
                    .where((biologicDataContent) =>
                        biologicDataContent.typeBilan == _items[index])
                    .first,
                repDataSetList: repDataSet,
                onMaximizeChart: widget.onAnalysisSelected,
                onOpenBiologicalDataContentType2:
                    widget.onOpenBiologicalDataContentType2),
          );
        })),
      ),
    );
  }

  void _initStringLists() {
    // initialisation des listes de string pour le filtrage des éléments
    widget.biologicDataContentList.forEach((biologicDataContent) {
      _duplicateItemStringList.add(biologicDataContent.typeBilan);
    });
    _items.addAll(_duplicateItemStringList);
  }
}
