import 'dart:io';
//import 'package:file_picker/file_picker.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/MultiImagePickerUtils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/pdf_builder.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/CustomBottomSheet.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/date_selector_widget.dart';
import 'package:theradom/widgets/dialog_background.dart';
import 'package:theradom/widgets/buttons/dialog_button.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:open_filex/open_filex.dart';

/// Boite de dialogue d'ajout d'un bilan biologique

class ViewAddBilanBio extends StatefulWidget {
  ViewAddBilanBio();

  _ViewAddBilanBioState createState() => _ViewAddBilanBioState();
}

class _ViewAddBilanBioState extends State<ViewAddBilanBio> {
  String? _pdfPath;
  DateTime _pdfDateTime = DateTime.now();
  late final DateTime _minimumDateTime;
  int _nbImMax = Config.pdfMafPages;
  bool _isPdfLoading = false;
  final AutoSizeGroup _autoSizeGroup = AutoSizeGroup();

  @override
  void initState() {
    super.initState();
    _minimumDateTime = DateTime(_pdfDateTime.year, _pdfDateTime.month, 1);
  }

  @override
  Widget build(BuildContext context) {
    return DialogBackground(
      child: Column(
        children: [
          Text(
            allTranslations.text('add_bio'),
            textAlign: TextAlign.center,
            style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
                fontSize: 18),
          ),
          SizedBox(
            height: 30.0,
          ),
          _pdfPath != null
              ? GestureDetector(
                  onTap: () =>
                      _pdfPath != null ? OpenFilex.open(_pdfPath!) : null,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset("assets/images/icons/icFilePdf.svg"),
                      Text(
                        _pdfDateTime.toYYYYMMDD() + ".pdf",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: AppTheme.primaryColor, fontSize: 14),
                      ),
                    ],
                  ),
                )
              : _isPdfLoading
                  ? PlatformCircularIndicator(darkIndicator: true)
                  : Container(),
          SizedBox(
            height: 30.0,
          ),
          Container(
            width: MediaQuery.of(context).size.width / 2,
            child: DateSelectorWidget(
              dateHint: allTranslations.text('choose_bio_result_date'),
              minimumDateTime: _minimumDateTime,
              maximumDateTime: DateTime.now(),
              locked: false,
              initialDateTime: _pdfDateTime,
              onValidate: _changeDate,
            ),
          ),
          SizedBox(
            height: 10.0,
          ),
          DialogButton(
              label: allTranslations.text('choose'),
              labelColor: AppTheme.primaryColor,
              backgroundColor: AppTheme.backgroundColor,
              group: _autoSizeGroup,
              onPressed: () async {
                int res = await CustomBottomSheet(context: context, labels: [
                  //allTranslations.text("add_pdf"),
                  allTranslations.text("photos_to_pdf")
                ]).show();
                await _bottomSheetActions(res);
              }),
          SizedBox(
            height: 60.0,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              DialogButton(
                  label: allTranslations.text('back'),
                  labelColor: AppTheme.primaryColor,
                  backgroundColor: AppTheme.backgroundColor,
                  group: _autoSizeGroup,
                  onPressed: () => Navigator.of(context).pop()),
              SizedBox(
                width: 30.0,
              ),
              DialogButton(
                  label: allTranslations.text('to_validate'),
                  labelColor: AppTheme.primaryColor,
                  backgroundColor: _isPdfLoading
                      ? AppTheme.greyColor
                      : _pdfPath == null
                          ? AppTheme.greyColor
                          : AppTheme.secondaryColor,
                  group: _autoSizeGroup,
                  onPressed: () {
                    if (_isPdfLoading) {
                      HapticFeedback.vibrate();
                    } else if (_pdfPath == null) {
                      HapticFeedback.vibrate();
                    } else {
                      Navigator.of(context).pop([_pdfPath, _pdfDateTime]);
                    }
                  })
            ],
          )
        ],
      ),
    );
  }

  Future<void> _bottomSheetActions(int? i) async {
    if (i != null) {
      switch (i) {
        case 0:
          {
            // ajouter un pdf
            // choisir des photos et créer un pdf
            String path = await _pickImagesAndConvertToPdf();
            _changePdfPathAndName(path);
          }
          break;
      }
    }
  }

  void _changeDate(DateTime date) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        _pdfDateTime = date;
      });
    });
  }

  Future<String> _pickImagesAndConvertToPdf() async {
    _changeLoadingValue();
    List<File> fileList = await _pickImages();
    late String path;
    if (fileList.length > 0) {
      path = await _convertFileListToPdf(fileList);
    }
    _changeLoadingValue();
    return path;
  }

  _changeLoadingValue() {
    setState(() {
      _isPdfLoading = !_isPdfLoading;
    });
  }

  Future<List<File>> _pickImages() async {
    List list =
        await MultiImagePickerUtils.pickImagesFromGallery(context, _nbImMax);
    List<File> fileList = list[1];
    return fileList;
  }

  Future<String> _convertFileListToPdf(List<File> fileList) async {
    String pdfPath =
        await PdfBuilder.create.withFiles(title: "bio", fileList: fileList);
    return pdfPath;
  }

  void _changePdfPathAndName(String path) {
    setState(() {
      _pdfPath = path;
    });
  }
}
