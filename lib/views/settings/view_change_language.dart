import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/config.dart';

import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/buttons/radio_button.dart';
import 'package:theradom/widgets/settings/settings_single_page.dart';
import 'package:settings_ui/settings_ui.dart';

/// Vue pour la modification de la langue de l'application

class ViewChangeLanguage extends StatefulWidget {
  ViewChangeLanguage();

  @override
  _ViewChangeLanguageState createState() => _ViewChangeLanguageState();
}

class _ViewChangeLanguageState extends State<ViewChangeLanguage> {
  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
  }

  @override
  void dispose() {
    super.dispose();
    Config.blockBackBtnPop = true;
  }

  @override
  Widget build(BuildContext context) {
    return SettingsSinglePage(
      settingsTitle: allTranslations.text('choose_language'),
      iconWidget: Icon(Icons.language, color: AppTheme.primaryColor),
      isUserProfile: false,
      child: SettingsList(
        lightTheme:
            const SettingsThemeData(settingsListBackground: Colors.white),
        sections: [
          SettingsSection(
            tiles: List.generate(Config.langageMap.length, (index) {
              String langCode = Config.langageMap.keys.toList()[index];
              return SettingsTile(
                  onPressed: (_) async => _onSelection(langCode),
                  leading: RadioButton(
                    picked: allTranslations.currentLanguage == langCode,
                  ),
                  trailing: SvgPicture.asset("assets/flags/$langCode.svg"),
                  title: Text(Config.langageMap.values.toList()[index],
                      style: TextStyle(
                          fontSize: FontSizeController.of(context).fontSize,
                          color: AppTheme.primaryColor)));
            }),
          )
        ],
      ),
    );
  }

  Future<void> _onSelection(String code) async {
    // sur sélection d'un élement
    await allTranslations.setNewLanguage(code);
    setState(() {});
  }
}
