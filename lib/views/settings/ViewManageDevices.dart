import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/services/repositories/mobile_devices_management_repository.dart';
import 'package:theradom/services/repositories/mobile_devices_deletion_repository.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseNotificationService.dart';
import 'package:theradom/config.dart';
import 'dart:convert';

class ViewManageDevicesPage extends StatefulWidget {
  @override
  _ViewManageDevicesPageState createState() => _ViewManageDevicesPageState();
}

class _ViewManageDevicesPageState extends State<ViewManageDevicesPage> {
  final MobileDevicesManagementRepository _mobileDevicesManagementRepository =
      MobileDevicesManagementRepository();
  final MobileDevicesDeletionRepository _mobileDevicesDeletionRepository =
      MobileDevicesDeletionRepository();
  var devices = [];

  @override
  void initState() {
    super.initState();
    _fetchDevices();
  }

  Future<void> _fetchDevices() async {
    String appToken =
        await FirebaseNotificationService.instance.getDeviceToken();
    String ins = Config.userAccountInfos!.INS;
    String login = Config.basicUserInfos!.login;
    var response = await _mobileDevicesManagementRepository.getDevicesList(
        appToken, login, ins);
    var test = jsonDecode(response);
    setState(() {
      devices = test['devicesList'];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('Liste des appareils'),
        ),
        body: Padding(
            padding: const EdgeInsets.only(
                top: 20.0), // Adjust this value to change the space
            child: Column(
              children: [
                Text(
                  'Liste des appareils',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                ),
                Container(
                  width: MediaQuery.of(context).size.width * 0.8,
                  child: Text(
                    'Vous trouverez ci-dessous une liste de tout les appareils associés à votre compte',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                Padding(padding: const EdgeInsets.only(top: 10)),
                Container(
                  width: MediaQuery.of(context).size.width * 0.8,
                  child: Divider(
                    color: Colors.grey,
                    height: 1,
                    thickness: 1.5,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: devices.length,
                    itemBuilder: (context, index) {
                      return ListTile(
                          leading: SvgPicture.asset(
                              'assets/images/icons/smartphone.svg',
                              width: 38,
                              height: 38),
                          title: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(devices[index]['nom'] ?? ''),
                              Text(
                                devices[index]['token'] ?? '',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                          trailing: Container(
                            padding: EdgeInsets.all(4), // Add this
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius:
                                  BorderRadius.circular(8), // Add this
                            ),
                            child: IconButton(
                              icon: Icon(Icons.delete,
                                  color: Colors
                                      .white), // Change icon color to white for visibility
                              onPressed: () {
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return AlertDialog(
                                      title: Text('Confirmer la suppression',
                                          style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold)),
                                      content: Text(
                                          'Etes-vous sûr de vouloir supprimer ${devices[index]['nom']}?'),
                                      actions: <Widget>[
                                        TextButton(
                                          child: Text('Annuler'),
                                          onPressed: () {
                                            Navigator.of(context).pop();
                                          },
                                        ),
                                        TextButton(
                                          child: Text('Supprimer',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold)),
                                          onPressed: () async {
                                            String? deviceName =
                                                devices[index]['nom'];
                                            await _mobileDevicesDeletionRepository
                                                .removeDevice(
                                                    Config
                                                        .userAccountInfos!.INS,
                                                    Config
                                                        .basicUserInfos!.login,
                                                    await FirebaseNotificationService
                                                        .instance
                                                        .getDeviceToken(),
                                                    devices[index]['token']);
                                            setState(() {
                                              devices.removeAt(index);
                                            });
                                            Navigator.of(context).pop();

                                            await showDialog(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return AlertDialog(
                                                  title: Text(
                                                      'Suppression confirmée',
                                                      style: TextStyle(
                                                          fontSize: 18,
                                                          fontWeight:
                                                              FontWeight.bold)),
                                                  content: Text(
                                                      'L\'appareil $deviceName a bien été supprimé.'),
                                                  actions: <Widget>[
                                                    TextButton(
                                                      child: Text('OK'),
                                                      onPressed: () {
                                                        Navigator.of(context)
                                                            .pop();
                                                      },
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                        ),
                                      ],
                                    );
                                  },
                                );
                              },
                            ),
                          ));
                    },
                  ),
                ),
              ],
            )));
  }
}
