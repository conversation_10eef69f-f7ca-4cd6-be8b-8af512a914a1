import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/settings/email/BlocModifyEmail.dart';
import 'package:theradom/blocs/settings/email/EventModifyEmail.dart';
import 'package:theradom/blocs/settings/email/StateModifyEmail.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/regexp_utils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/buttons/large_button.dart';
import 'package:theradom/widgets/settings/settings_single_page.dart';

/// Vue de modification de l'email utilisateur

class ViewChangeEmail extends StatefulWidget {
  ViewChangeEmail();

  _ViewChangeEmailState createState() => _ViewChangeEmailState();
}

class _ViewChangeEmailState extends State<ViewChangeEmail> {
  final BlocModifyEmail _blocModifyEmail = BlocModifyEmail();
  final _emailController = TextEditingController();
  final _emailConfirmController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final FocusNode _secondFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
  }

  @override
  void dispose() {
    _blocModifyEmail.close();
    super.dispose();
    Config.blockBackBtnPop = true;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<BlocModifyEmail, StateModifyEmail>(
      bloc: _blocModifyEmail,
      listener: (context, state) {
        if (state is StateEmailModified) {
          if (state.loading) {
            AlertBox(
                    context: context,
                    title: allTranslations.text('please_wait'))
                .showLoading();
          } else {
            Navigator.of(context).pop(); // ferme le dialog
            if (state.success!) {
              Navigator.of(context).pop(state.message);
            } else {
              AlertBox(context: context, description: state.message)
                  .showFlushbar(warning: true);
            }
          }
        }
      },
      child: SettingsSinglePage(
        settingsTitle: allTranslations.text('change_email'),
        iconWidget: SvgPicture.asset("assets/images/icons/icMessagerie.svg"),
        isUserProfile: true,
        child: Form(
          key: _formKey,
          child: Container(
            margin: EdgeInsets.only(left: 20.0, right: 20.0),
            color: Colors.transparent,
            padding: EdgeInsets.all(15),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                      color: AppTheme.backgroundColor,
                      borderRadius: BorderRadius.circular(20.0),
                      boxShadow: [
                        BoxShadow(
                            spreadRadius: 0.0,
                            blurRadius: 10.0,
                            color: AppTheme.primaryBlurColor.withOpacity(0.3))
                      ]),
                  padding: EdgeInsets.only(top: 10, bottom: 10),
                  child: TextFormField(
                      controller: _emailController,
                      enableInteractiveSelection: true,
                      obscureText: false,
                      autocorrect: false,
                      maxLines: 1,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      cursorColor: AppTheme.primaryColor,
                      style: TextStyle(
                          // style du texte rensigné
                          fontSize: FontSizeController.of(context).fontSize,
                          color: AppTheme.primaryColor),
                      decoration: InputDecoration(
                          hintText: allTranslations.text('new_email'),
                          hintMaxLines: 1,
                          hintStyle: TextStyle(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              fontSize:
                                  FontSizeController.of(context).fontSize),
                          contentPadding:
                              EdgeInsets.only(left: 20.0, right: 20.0),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20.0),
                              borderSide: BorderSide.none),
                          errorStyle: TextStyle(
                              color: AppTheme.tertiaryColor, fontSize: 14)),
                      validator: _textfieldValidation,
                      onFieldSubmitted: (String value) => FocusScope.of(context)
                          .requestFocus(_secondFocusNode)),
                ),
                SizedBox(
                  height: 20.0,
                ),
                Container(
                    decoration: BoxDecoration(
                        color: AppTheme.backgroundColor,
                        borderRadius: BorderRadius.circular(20.0),
                        boxShadow: [
                          BoxShadow(
                              spreadRadius: 0.0,
                              blurRadius: 10.0,
                              color: AppTheme.primaryBlurColor.withOpacity(0.3))
                        ]),
                    padding: EdgeInsets.only(top: 10, bottom: 10),
                    child: TextFormField(
                        autofocus: false,
                        controller: _emailConfirmController,
                        enableInteractiveSelection: true,
                        focusNode: _secondFocusNode,
                        obscureText: false,
                        autocorrect: false,
                        maxLines: 1,
                        keyboardType: TextInputType.emailAddress,
                        cursorColor: AppTheme.primaryColor,
                        style: TextStyle(
                            // style du texte rensigné
                            fontSize: FontSizeController.of(context).fontSize,
                            color: AppTheme.primaryColor),
                        decoration: InputDecoration(
                            hintText: allTranslations.text('confirm_new_email'),
                            hintMaxLines: 1,
                            hintStyle: TextStyle(
                                color: AppTheme.primaryColor.withOpacity(0.3),
                                fontSize:
                                    FontSizeController.of(context).fontSize),
                            contentPadding:
                                EdgeInsets.only(left: 20.0, right: 20.0),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20.0),
                                borderSide: BorderSide.none),
                            errorStyle: TextStyle(
                                color: AppTheme.tertiaryColor, fontSize: 14)),
                        validator: _textfieldValidation,
                        onFieldSubmitted: (String value) =>
                            _launchModifyEmaiEvent())),
                Spacer(),
                LargeButton(
                    title: allTranslations.text('to_modify'),
                    titleColor: AppTheme.primaryColor,
                    backgroundColor: AppTheme.secondaryColor,
                    onTap: _launchModifyEmaiEvent)
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _launchModifyEmaiEvent() {
    // en cas de validation, lance l'event de modification du password
    if (_formKey.currentState!.validate()) {
      FocusScope.of(context).requestFocus(FocusNode());
      _blocModifyEmail.add(EventModifyEmailValidate(
          newEmail: _emailController.text,
          newEmailConfirmation: _emailConfirmController.text));
    } else {
      HapticFeedback.vibrate();
    }
  }

  String? _textfieldValidation(String? value) {
    // validation de la saisie du textfield
    if (value == null) {
      // si vide
      return allTranslations.text("not_specified");
    } else {
      // non vide
      if (!RegexpUtils.emailAdress.hasMatch(_emailController.text)) {
        // si ce n'est pas un email
        return allTranslations.text("format_not_valid");
      } else {
        if (_emailController.text != _emailConfirmController.text) {
          // si les deux emails correspondent
          return allTranslations.text("emails_dont_match");
        } else {
          return null;
        }
      }
    }
  }
}
