import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/settings/password/BlocModifyPassword.dart';
import 'package:theradom/blocs/settings/password/EventModifyPassword.dart';
import 'package:theradom/blocs/settings/password/StateModifyPassword.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/buttons/large_button.dart';
import 'package:theradom/widgets/settings/settings_single_page.dart';

/// Vue de modification du mot de passe

class ViewChangePassword extends StatefulWidget {
  ViewChangePassword();

  _ViewChangePasswordState createState() => _ViewChangePasswordState();
}

class _ViewChangePasswordState extends State<ViewChangePassword> {
  final BlocModifyPassword _blocModifyPassword = BlocModifyPassword();
  final _pwdController = TextEditingController();
  final _pwdConfirmController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final FocusNode _secondFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
  }

  @override
  void dispose() {
    _blocModifyPassword.close();
    super.dispose();
    Config.blockBackBtnPop = true;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<BlocModifyPassword, StateModifyPassword>(
        bloc: _blocModifyPassword,
        listener: (context, state) {
          if (state is StatePasswordModified) {
            if (state.loading) {
              AlertBox(
                      context: context,
                      title: allTranslations.text('please_wait'))
                  .showLoading();
            } else {
              Navigator.of(context)
                  .pop(); // fermeture du dialogue de chargement
              if (state.success!) {
                Navigator.of(context).pop(state.message);
              } else {
                AlertBox(context: context, description: state.message)
                    .showFlushbar(warning: true);
              }
            }
          }
        },
        child: SettingsSinglePage(
            settingsTitle: allTranslations.text('change_password'),
            iconWidget:
                SvgPicture.asset("assets/images/icons/icSeanceLocked.svg"),
            isUserProfile: true,
            child: Form(
              key: _formKey,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
                child: Container(
                  margin: EdgeInsets.only(left: 20.0, right: 20.0),
                  color: Colors.transparent,
                  padding: EdgeInsets.all(15),
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.only(top: 10, bottom: 10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20.0),
                            color: AppTheme.backgroundColor,
                            boxShadow: [
                              BoxShadow(
                                  color: AppTheme.primaryBlurColor
                                      .withOpacity(0.3),
                                  spreadRadius: 0.0,
                                  blurRadius: 20.0)
                            ]),
                        child: TextFormField(
                            controller: _pwdController,
                            enableInteractiveSelection: true,
                            obscureText: true,
                            autocorrect: false,
                            maxLines: 1,
                            cursorColor: AppTheme.primaryColor,
                            textInputAction: TextInputAction.next,
                            decoration: InputDecoration(
                                hintText: allTranslations.text('new_pwd'),
                                hintMaxLines: 1,
                                hintStyle: TextStyle(
                                    color:
                                        AppTheme.primaryColor.withOpacity(0.3),
                                    fontSize: FontSizeController.of(context)
                                        .fontSize),
                                contentPadding:
                                    EdgeInsets.only(left: 20.0, right: 20.0),
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(20.0),
                                    borderSide: BorderSide.none),
                                errorStyle: TextStyle(
                                    color: AppTheme.tertiaryColor,
                                    fontSize: 14)),
                            validator: _textfieldValidation,
                            onFieldSubmitted: (String value) =>
                                FocusScope.of(context)
                                    .requestFocus(_secondFocusNode)),
                      ),
                      SizedBox(height: 20.0),
                      Container(
                        padding: EdgeInsets.only(top: 10.0, bottom: 10.0),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20.0),
                            color: AppTheme.backgroundColor,
                            boxShadow: [
                              BoxShadow(
                                  color: AppTheme.primaryBlurColor
                                      .withOpacity(0.3),
                                  spreadRadius: 0.0,
                                  blurRadius: 20.0)
                            ]),
                        child: TextFormField(
                          focusNode: _secondFocusNode,
                          controller: _pwdConfirmController,
                          enableInteractiveSelection: true,
                          cursorColor: AppTheme.primaryColor,
                          obscureText: true,
                          autocorrect: false,
                          maxLines: 1,
                          decoration: InputDecoration(
                              hintText: allTranslations.text('confirm_new_pwd'),
                              hintMaxLines: 1,
                              hintStyle: TextStyle(
                                  color: AppTheme.primaryColor.withOpacity(0.3),
                                  fontSize:
                                      FontSizeController.of(context).fontSize),
                              contentPadding:
                                  EdgeInsets.only(left: 20.0, right: 20.0),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                  borderSide: BorderSide.none),
                              errorStyle: TextStyle(
                                  color: AppTheme.tertiaryColor, fontSize: 14)),
                          validator: _textfieldValidation,
                          onFieldSubmitted: (String value) =>
                              _launchModifyPwdEvent(),
                        ),
                      ),
                      SizedBox(height: 20.0),
                      Container(
                        child: Text(
                          allTranslations.text('pwd_format'),
                          style: TextStyle(
                            color: AppTheme.lightGrey,
                            fontSize:
                                FontSizeController.of(context).fontSize - 5,
                          ),
                        ),
                      ),
                      Spacer(),
                      LargeButton(
                          title: allTranslations.text('to_modify'),
                          titleColor: AppTheme.primaryColor,
                          backgroundColor: AppTheme.secondaryColor,
                          onTap: _launchModifyPwdEvent)
                    ],
                  ),
                ),
              ),
            )));
  }

  void _launchModifyPwdEvent() {
    // si validation, lance l'event de modification du password
    if (_formKey.currentState!.validate()) {
      _blocModifyPassword.add(EventModifyPasswordValidate(
          newPassword: _pwdController.text,
          newPasswordConfirmation: _pwdConfirmController.text));
    } else {
      HapticFeedback.vibrate();
    }
  }

  String? _textfieldValidation(String? value) {
    // validator des textfields
    if (value == null || value.trim().isEmpty) {
      // si vide
      return allTranslations.text("not_specified");
    } else {
      // non vide
      Pattern pattern =
          r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$';
      RegExp regex = new RegExp(pattern.toString());
      if (_pwdController.text != _pwdConfirmController.text) {
        // si les deux mots de passe ne correspondent pas
        return "Les mots de passe ne sont pas égaux";
      } else {
        if (!regex.hasMatch(value)) {
          // si le format n'est pas valide
          return allTranslations.text("format_not_valid");
        } else {
          return null;
        }
      }
    }
  }
}
