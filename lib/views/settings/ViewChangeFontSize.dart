import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/settings/fontsize/BlocModifyFontSize.dart';
import 'package:theradom/blocs/settings/fontsize/EventModifyFontSize.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/buttons/radio_button.dart';
import 'package:theradom/widgets/settings/settings_single_page.dart';
import 'package:settings_ui/settings_ui.dart';

/// Vue pour la modification de la taille de la police

class ViewChangeFontSize extends StatefulWidget {
  ViewChangeFontSize();

  @override
  _ViewChangeFontSizeState createState() => _ViewChangeFontSizeState();
}

class _ViewChangeFontSizeState extends State<ViewChangeFontSize> {
  BlocModifyFontSize _blocModifyFontSize = BlocModifyFontSize();
  late int _indexSelection;
  List<String> _libelleList = [];

  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
    _libelleList = FontSizeController().fontSizeStrList;
  }

  @override
  void dispose() {
    super.dispose();
    Config.blockBackBtnPop = true;
  }

  @override
  Widget build(BuildContext context) {
    _indexSelection = FontSizeController.of(context)
        .fontSizeMap
        .keys
        .toList()
        .indexOf(FontSizeController.of(context).fontSizeObj);

    return SettingsSinglePage(
      settingsTitle: allTranslations.text('choose_font_size'),
      iconWidget: SvgPicture.asset("assets/images/icons/icTextSize.svg"),
      isUserProfile: false,
      child: Container(
          child: SettingsList(
        lightTheme:
            const SettingsThemeData(settingsListBackground: Colors.white),
        sections: [
          SettingsSection(
              tiles: List.generate(
                  FontSizeController.of(context).fontSizeMap.length,
                  (index) => SettingsTile(
                        title: Text(
                          _libelleList[index],
                          style: TextStyle(
                              fontSize: FontSizeController.of(context).fontSize,
                              color: AppTheme.primaryColor),
                        ),
                        onPressed: (_) async => await _onSelection(index),
                        leading: RadioButton(
                          picked: _indexSelection == index ? true : false,
                        ),
                        trailing: Icon(Icons.circle, color: Colors.transparent),
                      )))
        ],
      )),
    );
  }

  Future<void> _onSelection(int index) async {
    // sur sélection d'un élement
    setState(() {
      _indexSelection = index;
    });
    FontSize fontSizeObj =
        FontSizeController.of(context).fontSizeMap.keys.toList()[index];
    FontSizeController.of(context).setFontSize(fontSizeObj);
    _launchModifyFontSizeEvent();
  }

  void _launchModifyFontSizeEvent() {
    // lance l'event de modification de fontSize
    _blocModifyFontSize.add(EventModifyFontSizeValidate(
        fontSize: FontSizeController.of(context).fontSize));
  }
}
