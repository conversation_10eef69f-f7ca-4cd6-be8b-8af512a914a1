import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/blocs/settings/BlocSettings.dart';
import 'package:theradom/blocs/settings/EventSettings.dart';
import 'package:theradom/blocs/settings/StateSettings.dart';
import 'package:theradom/blocs/settings/email/BlocModifyEmail.dart';
import 'package:theradom/blocs/settings/email/StateModifyEmail.dart';
import 'package:theradom/blocs/settings/password/BlocModifyPassword.dart';
import 'package:theradom/blocs/settings/password/StateModifyPassword.dart';
import 'package:theradom/config.dart';

import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/biometric.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/log_out_timer/logout_timer_stream.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/settings/ViewChangeFontSize.dart';
import 'package:theradom/views/settings/ViewManageDevices.dart';
import 'package:theradom/views/settings/ViewChangeTimeBeforeLogOut.dart';
import 'package:theradom/widgets/settings/profile_element.dart';
import 'package:theradom/views/settings/view_change_language.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:settings_ui/settings_ui.dart';

class ViewSettingsGeneral extends StatefulWidget {
  @override
  _ViewSettingsGeneralState createState() => _ViewSettingsGeneralState();

  ViewSettingsGeneral();
}

class _ViewSettingsGeneralState extends State<ViewSettingsGeneral> {
  late bool _cgu;
  late String _timeLimitStr;
  late bool _canDeviceUseBiometrics;
  late String _fontSizeInfo;
  late final BlocSettings _blocSettings;
  late final BlocModifyPassword _blocModifyPassword;
  late final BlocModifyEmail _blocModifyEmail;

  final TextStyle _sectionTitleStyle = TextStyle(
      fontSize: 14,
      color: AppTheme.primaryColor,
      fontWeight: FontWeight.w600,
      fontFamily: "OpenSans");

  final TextStyle _tileSubtitleStyle = TextStyle(
      fontSize: 14,
      fontFamily: "OpenSans",
      color: AppTheme.primaryColor.withOpacity(0.5));

  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
    _blocSettings = BlocProvider.of<BlocSettings>(context);
    _blocModifyPassword = BlocModifyPassword();
    _blocModifyEmail = BlocModifyEmail();
    _cgu = Config.basicUserInfos!.accordCgu;
  }

  @override
  void dispose() {
    super.dispose();
    Config.blockBackBtnPop = true;
  }

  @override
  Widget build(BuildContext context) {
    final _tileTitleStyle = TextStyle(
      fontSize: FontSizeController.of(context).fontSize,
    );

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: MultiBlocListener(
        listeners: [
          BlocListener<BlocSettings, StateSettings>(
            bloc: _blocSettings,
            listener: (context, state) {
              if (state is StateSettingsModifyTOU) {
                if (state.success) {
                  // succès, update variable globale
                  if (state.accordCGU != null) {
                    Config.basicUserInfos!.accordCgu = state.accordCGU!;
                    if (!state.accordCGU!) {
                      _launchLogOutEvent(Config.appKey.currentContext!);
                    }
                  }
                } else {
                  // échec de la modification
                  _showFlushbar(state.success, allTranslations.text('error'),
                      state.message);
                  setState(() {
                    _cgu = !_cgu;
                  });
                }
              }

              if (state is StateSettingsLoading) {
                if (state.loading) {
                  AlertBox(
                          context: context,
                          title: allTranslations.text('please_wait'))
                      .showLoading();
                } else {
                  Navigator.of(context).pop();
                }
              }
            },
          ),
          BlocListener<BlocModifyEmail, StateModifyEmail>(
            bloc: _blocModifyEmail,
            listener: (context, state) {
              if (state is StateEmailModified) {
                if (state.loading) {
                  AlertBox(
                          context: context,
                          title: allTranslations.text('please_wait'))
                      .showLoading();
                } else {
                  Navigator.of(context).pop();
                  _showFlushbar(state.success!, allTranslations.text('error'),
                      state.message!);
                }
              }
            },
          ),
          BlocListener<BlocModifyPassword, StateModifyPassword>(
            bloc: _blocModifyPassword,
            listener: (context, state) {
              if (state is StatePasswordModified) {
                if (state.loading) {
                  AlertBox(
                          context: context,
                          title: allTranslations.text('please_wait'))
                      .showLoading();
                } else {
                  Navigator.of(context).pop();
                  _showFlushbar(state.success!, allTranslations.text('error'),
                      state.message!);
                }
              }
            },
          )
        ],
        child: FutureBuilder(
          future: Future.wait([
            _getTimeDurationLimitStr(), // 0
            _getDeviceCanUseBiometrics(), // 1
            _getFontSizeInfoStr(), // 2
          ]),
          builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
            if (snapshot.hasData) {
              _timeLimitStr = snapshot.data![0]; // timeLimitStr
              _canDeviceUseBiometrics =
                  snapshot.data![1]; // canDeviceUseBiometrics
              _fontSizeInfo = snapshot.data![2]; // fontSizeStr

              return Column(
                children: [
                  // expandable container du profil utilisateur
                  ProfileExpandableContainer(),

                  // liste des paramètres
                  Expanded(
                    child: Container(
                      child: SettingsList(
                        lightTheme: const SettingsThemeData(
                            settingsListBackground: Colors.white),
                        sections: [
                          // section "application"
                          SettingsSection(
                            title: Text(
                              allTranslations.text('application').toUpperCase(),
                              style: _sectionTitleStyle,
                            ),
                            tiles: [
                              // déconnexion
                              SettingsTile(
                                title: Text(
                                  allTranslations.text("logout"),
                                  style: _tileTitleStyle,
                                ),
                                leading: SvgPicture.asset(
                                  "assets/images/icons/icLogOut.svg",
                                  colorFilter: ColorFilter.mode(
                                      AppTheme.tertiaryColor, BlendMode.srcIn),
                                ),
                                onPressed: (_) async =>
                                    await _showAlertLogout(),
                              ),

                              // langue
                              SettingsTile(
                                  enabled: true,
                                  title: Text(allTranslations.text("language"),
                                      style: _tileTitleStyle),
                                  onPressed: (_) => _pushToLanguageScreen(),
                                  leading: SvgPicture.asset(
                                      "assets/flags/${allTranslations.currentLanguage}.svg")),

                              // taille de la police
                              SettingsTile(
                                title: Text(
                                  allTranslations.text("fontSize"),
                                  style: _tileTitleStyle,
                                ),
                                leading: SvgPicture.asset(
                                    "assets/images/icons/icTextSize.svg"),
                                onPressed: (_) => _pushToFontSizeScreen(),
                                description: Text(_fontSizeInfo,
                                    style: _tileSubtitleStyle),
                              ),

                              // manuel utilisateur
                              SettingsTile(
                                  title: Text(allTranslations.text("userGuide"),
                                      style: _tileTitleStyle),
                                  onPressed: (_) => _launchOpenUserGuideEvent(),
                                  leading: Image(
                                    image: AssetImage(
                                      "assets/images/logo/logo_ifu.png",
                                    ),
                                    height: 20,
                                  )),

                              /*
                              SettingsTile(
                                  title: allTranslations.text("default_screen"),
                                  titleTextStyle: _tileTitleStyle,
                                  onTap: () => _showAlertDefaultScreenSelection()
                              )
                              */

                              // confidentiality
                              SettingsTile(
                                title: Text(
                                    allTranslations.text("privacyPolicy"),
                                    style: _tileTitleStyle),
                                onPressed: (_) =>
                                    _launchOpenPrivacyPolicyEvent(),
                                leading: SvgPicture.asset(
                                  "assets/images/icons/icPrivacyPolicy.svg",
                                  colorFilter: ColorFilter.mode(
                                      AppTheme.primaryColor, BlendMode.srcIn),
                                  height: 26,
                                ),
                              )
                            ],
                          ),

                          // section "sécurité"
                          SettingsSection(
                            title: Text(
                                allTranslations.text('safety').toUpperCase(),
                                style: _sectionTitleStyle),
                            tiles: [
                              // délais avant déconnexion
                              SettingsTile(
                                  title: Text(
                                      allTranslations
                                          .text("time_before_logout"),
                                      style: _tileTitleStyle),
                                  description: Text(_timeLimitStr,
                                      style: _tileSubtitleStyle),
                                  leading: SvgPicture.asset(
                                      "assets/images/icons/icUserTimer.svg"),
                                  onPressed: (_) async =>
                                      await _pushToTimeBeforeLogOutScreen()),

                              SettingsTile(
                                  title: Text(
                                      allTranslations.text("manage_devices"),
                                      style: _tileTitleStyle),
                                  leading: SvgPicture.asset(
                                    "assets/images/icons/smartphone.svg",
                                    height: 32.0, // Réduisez la hauteur
                                    width: 32.0, // Réduisez la largeur
                                  ),
                                  onPressed: (_) async =>
                                      await _pushToManageDevicesScreen()),

                              // authentification biométrique
                              SettingsTile.switchTile(
                                  initialValue: Config.useBiometricID,
                                  title: Text(
                                      allTranslations
                                          .text("biometric_authentication"),
                                      style: _tileTitleStyle),
                                  description: _canDeviceUseBiometrics
                                      ? null
                                      : Text(
                                          allTranslations.text("not_available"),
                                          style: _tileSubtitleStyle,
                                        ),
                                  //subtitle:
                                  enabled: _canDeviceUseBiometrics,
                                  activeSwitchColor: AppTheme.secondaryColor,
                                  leading: SvgPicture.asset(
                                    "assets/images/icons/icPrint.svg",
                                  ),
                                  onToggle: (value) async =>
                                      await _onBiometricSwitchToggle(value)),
                            ],
                          ),

                          // section CGU
                          SettingsSection(
                            title: Text(
                                allTranslations.text('TOUshort').toUpperCase(),
                                style: _sectionTitleStyle),
                            tiles: [
                              // consulter les CGU
                              SettingsTile(
                                  title: Text(allTranslations.text("readTOU"),
                                      style: _tileTitleStyle),
                                  leading: SvgPicture.asset(
                                      "assets/images/icons/icDocCgu.svg"),
                                  onPressed: (_) => _launchOpenTOU()),

                              // accord CGU
                              SettingsTile.switchTile(
                                  title: Text(allTranslations.text("TOU"),
                                      style: _tileTitleStyle),
                                  leading: SvgPicture.asset(
                                      "assets/images/icons/icCgv.svg"),
                                  initialValue: _cgu,
                                  activeSwitchColor: AppTheme.secondaryColor,
                                  onToggle: (bool value) =>
                                      _launchEventModifyCGU(value)),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              );
            } else {
              return PlatformCircularIndicator(darkIndicator: false);
            }
          },
        ),
      ),
    );
  }

  void _pushToLanguageScreen() async {
    // push vers la vue de modification de la langue
    await Navigator.push(context,
        CupertinoPageRoute(builder: (context) => ViewChangeLanguage()));
    setState(() {});
  }

  Future<void> _pushToTimeBeforeLogOutScreen() async {
    // ouvre l'écran de sélection du temps avant déconnexion
    await Navigator.push(context,
        CupertinoPageRoute(builder: (context) => ViewChangeTimeBeforeLogOut()));
    setState(() {});
  }

  Future<void> _pushToManageDevicesScreen() async {
    // ouvre l'écran de gestion des appareils
    await Navigator.push(context,
        CupertinoPageRoute(builder: (context) => ViewManageDevicesPage()));
    setState(() {});
  }

  Future<void> _pushToFontSizeScreen() async {
    // ouvre l'écran de sélection du temps avant déconnexion
    await Navigator.push(context,
        CupertinoPageRoute(builder: (context) => ViewChangeFontSize()));
    setState(() {});
  }

  /*
  void _showAlertDefaultScreenSelection() {
    // ouvre l'écran de sélection de l'écran par défaut
    Navigator.push(context, CupertinoPageRoute(builder: (context) => ViewChangeDefaultScreen(
      fontSize: Config.basicUserInfos.taillePolice,
      screenIndex: DefaultScreenController.of(context).screenIndex,
    )));
  }
   */

  Future<void> _onBiometricSwitchToggle(bool value) async {
    // sur switch biométrie
    bool resAuth = false;
    if (value) {
      // si le switch passe à true
      resAuth = await Biometric.authentication.start();
      if (resAuth) {
        await SharedPreferencesUtil.instance
            .setString(Config.prefUsername, Config.userAccountInfos!.login);
        await SharedPreferencesUtil.instance.setString(
            Config.prefPassword, Config.userAccountInfos!.passwordHash);
      } else {
        await SharedPreferencesUtil.instance.remove(Config.prefUsername);
        await SharedPreferencesUtil.instance.remove(Config.prefPassword);
      }
    }
    await SharedPreferencesUtil.instance
        .setBool(Config.prefUseBiometricID, resAuth);
    setState(() {
      Config.useBiometricID = resAuth;
    });
  }

  Future<void> _showAlertLogout() async {
    // dialogue pour la déconnexion
    bool? quit = await AlertBox(
            context: context,
            title: allTranslations.text('logout'),
            description: allTranslations.text('logout_question'),
            confirmBtnLabel: allTranslations.text('leave'))
        .showConfirmation(warning: true);
    if (quit != null && quit) _launchLogOutEvent(Config.appKey.currentContext!);
  }

  void _showFlushbar(bool success, String title, String description) {
    AlertBox(context: context, title: title, description: description)
        .showFlushbar(warning: !success);
  }

  void _launchLogOutEvent(BuildContext context) {
    // lance l'event de déconnexion
    Navigator.of(context)
        .popUntil((route) => route.isFirst); // pop jusqu'à la première route
    BlocProvider.of<BlocAuthentication>(context).add(EventLoggedOut());
  }

  void _launchOpenUserGuideEvent() {
    // lance l'event pour obtenir le manuel utilisateur
    _blocSettings.add(EventSettingsGetUserGuide());
  }

  void _launchOpenPrivacyPolicyEvent() {
    // lance l'event pour voir la politique de confidentialité
    _blocSettings.add(EventSettingsGetPrivacyPolicy());
  }

  void _launchOpenTOU() {
    // lance l'event pour obtenir les CGU
    _blocSettings.add(EventSettingsGetTOU());
  }

  void _launchEventModifyCGU(bool value) {
    // lance event de modification des CGU
    setState(() {
      // update interface
      _cgu = value;
    });
    _blocSettings.add(EventSettingsModifyTOU(accordCGU: value, success: false));
  }

  Future<String?> _getTimeDurationLimitStr() async {
    // récupère de manière asynchrone des données pour la vue
    // get limite avant log out auto
    int duration = Config.durationBeforeLogout;
    String? res = LogOutTimerStream.mapIntString()[duration] ?? "";
    return res;
  }

  Future<bool> _getDeviceCanUseBiometrics() async {
    // récupère de manière asynchrone l'info si le device permet d'utiliser la biométrie pour l'authentification
    //bool res = await BiometricUtils.canUseBiometrics();
    bool res = await Biometric.authentication.canCheck();
    return res;
  }

  Future<String> _getFontSizeInfoStr() async {
    // récupère de manière asynchrone le str qui indique la taille de la police
    return FontSizeController.of(context).fontSizeInfoStr;
  }
}
