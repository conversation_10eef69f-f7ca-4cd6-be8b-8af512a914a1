import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/config.dart';

import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/log_out_timer/logout_timer_stream.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/buttons/radio_button.dart';
import 'package:theradom/widgets/settings/settings_single_page.dart';
import 'package:settings_ui/settings_ui.dart';

/// Vue pour la modification du délais avant déconnexion

class ViewChangeTimeBeforeLogOut extends StatefulWidget {
  ViewChangeTimeBeforeLogOut();

  @override
  _ViewChangeTimeBeforeLogOutState createState() =>
      _ViewChangeTimeBeforeLogOutState();
}

class _ViewChangeTimeBeforeLogOutState
    extends State<ViewChangeTimeBeforeLogOut> {
  late int _indexSelection;
  Map<TimeLimit, int> _mapTimeLimitInt = LogOutTimerStream.mapTimeLimitInt();
  Map<int, String> _mapIntString = LogOutTimerStream.mapIntString();

  @override
  void initState() {
    super.initState();
    Config.blockBackBtnPop = false;
    _indexSelection = _mapTimeLimitInt.values
        .toList()
        .indexWhere((element) => element == Config.durationBeforeLogout);
  }

  @override
  void dispose() {
    super.dispose();
    Config.blockBackBtnPop = true;
  }

  @override
  Widget build(BuildContext context) {
    return SettingsSinglePage(
        settingsTitle: allTranslations.text('choose_delay_before_logout'),
        iconWidget: SvgPicture.asset("assets/images/icons/icUserTimer.svg"),
        isUserProfile: false,
        child: SettingsList(
          lightTheme: const SettingsThemeData(
            settingsListBackground: Colors.white,
          ),
          sections: [
            SettingsSection(
                tiles: List.generate(
                    _mapIntString.length,
                    (index) => SettingsTile(
                          onPressed: (_) => _onTileSelection(index),
                          leading: RadioButton(
                            picked: _indexSelection == index ? true : false,
                          ),
                          title: Text(_mapIntString.values.toList()[index],
                              style: TextStyle(
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                  color: AppTheme.primaryColor)),
                          trailing:
                              Icon(Icons.circle, color: Colors.transparent),
                        )))
          ],
        ));
  }

  void _onTileSelection(int index) {
    // sur sélection d'un élement
    setState(() {
      _indexSelection = index;
    });
    int value = _mapTimeLimitInt.values.toList()[index];
    LogOutTimerStream.instance.changeDuration(value);
  }
}
