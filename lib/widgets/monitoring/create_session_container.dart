import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';

class CreateSessionContainer extends StatelessWidget {
  final void Function() onTap;
  final double? height;
  final AutoSizeGroup autoSizeGroup;
  final bool isSession;

  CreateSessionContainer(
      {required this.onTap,
      this.height,
      required this.autoSizeGroup,
      required this.isSession});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: double.maxFinite,
      child: Card(
          elevation: 4.0,
          color: AppTheme.secondaryColor,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(20.0),
                  bottomLeft: Radius.circular(20.0))),
          child: <PERSON>gn(
            alignment: Alignment.bottomCenter,
            child: ListTile(
              visualDensity: VisualDensity(horizontal: 0, vertical: -3),
              contentPadding: EdgeInsets.only(left: 20.0, right: 20.0),
              onTap: onTap,
              leading: SvgPicture.asset("assets/images/icons/icNewSeance.svg"),
              title: AutoSizeText(
                allTranslations
                    .text(isSession ? "create_session" : "create_follow_up"),
                maxLines: 1,
                group: autoSizeGroup,
                maxFontSize: FontSizeController.of(context).fontSize,
                style: TextStyle(
                    fontFamily: "OpenSans",
                    fontSize: FontSizeController.of(context).fontSize,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600),
              ),
            ),
          )),
    );
  }
}
