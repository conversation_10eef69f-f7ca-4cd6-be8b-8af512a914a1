import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';

class MonitoringDropdownButton extends StatefulWidget {
  final String monitoring;
  final void Function(String) onChanged;

  MonitoringDropdownButton({required this.monitoring, required this.onChanged});

  _MonitoringDropDownButtonState createState() =>
      _MonitoringDropDownButtonState();
}

class _MonitoringDropDownButtonState extends State<MonitoringDropdownButton> {
  late String _monitoring;

  @override
  void initState() {
    super.initState();
    _monitoring = widget.monitoring;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 2.0),
          child: ChevIcon(assetPath: "assets/images/icons/btChevDw.svg"),
        ),
        Center(
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
                iconSize: 0,
                items: List.generate(
                    Config.basicUserInfos!.monitorings.length,
                    (index) => DropdownMenuItem(
                          value: Config.basicUserInfos!.monitorings.keys
                              .toList()[index],
                          child: Center(
                            child: Text(
                              Config.basicUserInfos!.monitorings.values
                                  .toList()[index]
                                  .toUpperCase(),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: TextStyle(
                                  fontFamily: "Syncopate",
                                  fontWeight: FontWeight.bold,
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                  color: AppTheme.backgroundColor),
                            ),
                          ),
                        )),
                onChanged: _onDropdownValueSelection,
                value: _monitoring,
                isExpanded: true,
                dropdownColor: AppTheme.primaryColor),
          ),
        )
      ],
    );
  }

  void _onDropdownValueSelection(String? suivi) {
    // sur sélection d'une tab
    if (_monitoring != suivi) {
      setState(() {
        _monitoring = suivi!;
      });
      widget.onChanged(suivi!);
    }
  }
}
