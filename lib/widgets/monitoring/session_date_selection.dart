import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/CustomDatePicker.dart';
import 'package:theradom/widgets/buttons/large_button.dart';

/// Widget de sélection de date et de création d'une nouvelle séance

class SessionDateSelection extends StatefulWidget {
  final bool estSeance;
  final DateTime minDateTime;
  final DateTime maxDateTime;
  final DateTime initialDateTime;
  final bool seanceEnCours;
  final void Function(DateTime) onDaySelected;

  SessionDateSelection(
      {required this.estSeance,
      required this.minDateTime,
      required this.maxDateTime,
      required this.initialDateTime,
      required this.seanceEnCours,
      required this.onDaySelected});

  _SessionDateSelectionState createState() => _SessionDateSelectionState();
}

class _SessionDateSelectionState extends State<SessionDateSelection> {
  double _fontSize = 18.0;

  @override
  void initState() {
    super.initState();
    widget.onDaySelected(widget.initialDateTime);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppTheme.backgroundColor,
      child: SingleChildScrollView(
          child: Padding(
        padding:
            EdgeInsets.only(top: 30.0, left: 20.0, right: 20.0, bottom: 20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              allTranslations.text('pick_date'),
              style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontSize: _fontSize,
                  fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
            Padding(
              padding: EdgeInsets.only(top: 50.0, bottom: 50.0),
              child: CustomDatePicker(
                  startDay: widget.minDateTime,
                  endDay: widget.maxDateTime,
                  initialSelectedDay: widget.initialDateTime,
                  onDaySelected: widget.onDaySelected),
            ),
            Padding(
              padding: EdgeInsets.only(left: 40.0, right: 40.0, bottom: 50.0),
              child: Text(
                  widget.seanceEnCours
                      ? allTranslations.text(widget.estSeance
                          ? 'session_already_in_progress'
                          : 'follow_up_already_in_progress')
                      : "",
                  style: TextStyle(
                      fontSize: _fontSize, color: AppTheme.tertiaryColor),
                  textAlign: TextAlign.center),
            ),
            LargeButton(
                title: widget.seanceEnCours
                    ? allTranslations.text(widget.estSeance
                        ? 'launch_ongoing_session'
                        : 'launch_ongoing_follow_up')
                    : allTranslations.text(
                        widget.estSeance ? 'new_session' : 'new_follow_up'),
                titleColor: AppTheme.primaryColor,
                backgroundColor: AppTheme.backgroundColor,
                showShadow: true,
                onTap: () => widget.seanceEnCours
                    ? Navigator.of(context).pop(0)
                    : Navigator.of(context).pop(1)),
            LargeButton(
                title: allTranslations.text('cancel'),
                titleColor: AppTheme.backgroundColor,
                showShadow: false,
                backgroundColor: AppTheme.primaryColor,
                onTap: () => Navigator.of(context).pop())
          ],
        ),
      )),
    );
  }
}
