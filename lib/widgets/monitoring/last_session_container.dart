import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';
import 'package:theradom/extensions/datetime_extension.dart';

class LastSessionContainer extends StatelessWidget {
  final SeancePrecedente? seance;
  final double? height;
  final bool isSession;
  final bool? termine;
  final AutoSizeGroup autoSizeGroup;
  final void Function(SeancePrecedente, bool) onTap;

  const LastSessionContainer({
    Key? key,
    required this.seance,
    this.height,
    required this.isSession,
    required this.termine,
    required this.autoSizeGroup,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// Cas 1 : Il y a bien une séance
    if (seance != null) {
      return Container(
        height: height,
        width: double.maxFinite,
        child: Card(
          elevation: 4.0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: ListTile(
              contentPadding: EdgeInsets.only(
                left: 20.0,
                right: 20.0,
                top: 10.0,
                bottom: 10.0,
              ),
              isThreeLine: true,
              onTap: () => onTap(seance!, termine ?? false),
              title: AutoSizeText(
                allTranslations.text(
                      isSession ? 'last_session' : 'last_follow_up',
                    ) +
                    ":",
                maxLines: 1,
                group: autoSizeGroup,
                maxFontSize: FontSizeController.of(context).fontSize,
                style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  fontWeight: FontWeight.w600,
                  fontFamily: "OpenSans",
                  color: AppTheme.primaryColor,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date de la dernière séance
                  AutoSizeText(
                    seance!.dateSeance.toLanguageString(),
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 30,
                      fontFamily: "OpenSans",
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  // Indication "en cours" ou "terminée"
                  AutoSizeText(
                    (!termine!)
                        ? allTranslations.text('in_progress_session')
                        : allTranslations.text(
                            isSession ? 'finished_f' : 'finished_m',
                          ),
                    maxLines: 1,
                    group: autoSizeGroup,
                    maxFontSize: FontSizeController.of(context).fontSize,
                    style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      fontFamily: "OpenSans",
                      fontWeight: FontWeight.bold,
                      color: !termine!
                          ? AppTheme.tertiaryColor
                          : AppTheme.primaryColor.withOpacity(0.3),
                    ),
                  ),
                ],
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ChevIcon(assetPath: "assets/images/icons/btChevBlancR.svg")
                ],
              ),
            ),
          ),
        ),
      );
    }

    /// Cas 2 : Aucune séance enregistrée (seance = null)
    else {
      return Container(
        height: height,
        width: double.maxFinite,
        child: Card(
          elevation: 4.0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: ListTile(
              contentPadding: EdgeInsets.only(
                left: 20.0,
                right: 20.0,
                top: 10.0,
                bottom: 10.0,
              ),
              isThreeLine: false,
              title: Center(
                child: AutoSizeText(
                  // Ici vous pouvez directement mettre "Aucune séance enregistrée"
                  // si vous le souhaitez, ou continuer d'utiliser vos traductions.
                  "aucune session précédente enregistrée",
                  maxLines: 1,
                  group: autoSizeGroup,
                  maxFontSize: FontSizeController.of(context).fontSize,
                  style: TextStyle(
                    fontSize: FontSizeController.of(context).fontSize,
                    fontWeight: FontWeight.bold,
                    fontFamily: "OpenSans",
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }
  }
}
