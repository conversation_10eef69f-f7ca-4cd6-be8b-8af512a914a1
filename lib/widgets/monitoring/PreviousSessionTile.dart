import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';

class PreviousSessionTile extends StatelessWidget {
  final SeancePrecedente seancePrecedente;
  final bool estTerminee;
  final bool isSession;
  final AutoSizeGroup group;
  final Function(SeancePrecedente) onTap;

  PreviousSessionTile(
      {required this.seancePrecedente,
      required this.estTerminee,
      required this.isSession,
      required this.group,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(seancePrecedente),
      child: Container(
        margin:
            EdgeInsets.only(left: 21.0, right: 21.0, bottom: 10.0, top: 10.0),
        height: 95,
        decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(20.0)),
            boxShadow: [
              BoxShadow(
                  color: AppTheme.primaryBlurColor,
                  blurRadius: 10,
                  spreadRadius: 0)
            ]),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 24.0, right: 22.0),
              child: SvgPicture.asset(seancePrecedente.verrou
                  ? "assets/images/icons/icSeanceLocked.svg"
                  : "assets/images/icons/icSeanceUnlocked.svg"),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AutoSizeText(
                  seancePrecedente.dateSeance.toLanguageString(),
                  maxLines: 1,
                  maxFontSize: FontSizeController.of(context).fontSize,
                  group: group,
                  overflow: TextOverflow.fade,
                  style: TextStyle(
                      fontSize: FontSizeController.of(context)
                          .fontSizeMap[FontSize.large],
                      fontFamily: "OpenSans",
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold),
                ),
                AutoSizeText(
                    estTerminee
                        ? allTranslations
                            .text(isSession ? 'finished_f' : 'finished_m')
                        : allTranslations.text('in_progress_session'),
                    maxLines: 1,
                    group: group,
                    style: TextStyle(
                        color: estTerminee
                            ? AppTheme.primaryColor.withOpacity(0.5)
                            : AppTheme.tertiaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizeController.of(context).fontSize,
                        fontFamily: "OpenSans"))
              ],
            ),
            Spacer(),
            Padding(
              padding: EdgeInsets.only(right: 20.0),
              child:
                  ChevIcon(assetPath: "assets/images/icons/btChevBlancR.svg"),
            )
          ],
        ),
      ),
    );
  }
}
