import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';
import 'package:theradom/widgets/monitoring/PreviousSessionTile.dart';
import 'package:theradom/widgets/monitoring/create_session_container.dart';
import 'package:theradom/widgets/monitoring/last_session_container.dart';
import 'package:theradom/widgets/monitoring/previous_session_list.dart';

class SessionCardsStack extends StatefulWidget {
  final List<SeancePrecedente> sessions;
  final List<String> sessionToEndIds;
  final bool isSession;
  final Function(SeancePrecedente, bool) onSessionSelected;
  final Function() onSessionCreationSelected;

  SessionCardsStack(
      {required this.sessions,
      required this.sessionToEndIds,
      required this.isSession,
      required this.onSessionSelected,
      required this.onSessionCreationSelected});

  _SessionCardsStack createState() => _SessionCardsStack();
}

class _SessionCardsStack extends State<SessionCardsStack> {
  late SeancePrecedente? _lastSession;
  final AutoSizeGroup _group = AutoSizeGroup();

  @override
  void initState() {
    super.initState();
    try {
      print(widget.sessions.first.toJson().toString());
      _lastSession = widget.sessions.first;
    } catch (StateError) {
      print("lastSessions vide");
      _lastSession = null;
      //_lastSession = SeancePrecedente(
      //    numSeance: "0", dateSeance: DateTime.now(), verrou: true);
    }
    List<SeancePrecedente> sameDateSessions = widget.sessions
        .where((session) => session.dateSeance == session.dateSeance)
        .toList();
    sameDateSessions.forEach((sameDateSession) {
      if (widget.sessionToEndIds.contains(sameDateSession.numSeance)) {
        _lastSession = sameDateSession;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //height: MediaQuery.of(context).size.height/3,
      child: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(top: 95),
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(16.0),
                bottomRight: Radius.circular(16.0),
              ),
            ),
            child: PreviousSessionList(
              expandedHeight: MediaQuery.of(context).size.height / 2.5,
              leading: SvgPicture.asset("assets/images/icons/icClock.svg"),
              title: AutoSizeText(
                allTranslations.text(widget.isSession
                    ? 'my_previous_session'
                    : 'my_previous_follow_up'),
                group: _group,
                maxLines: 1,
                maxFontSize: FontSizeController.of(context).fontSize,
                style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              trailing: ChevIcon(
                assetPath: "assets/images/icons/btChevBlancDw.svg",
              ),
              children: List.generate(
                  widget.sessions.length,
                  (index) => PreviousSessionTile(
                        seancePrecedente: widget.sessions[index],
                        group: _group,
                        isSession: widget.isSession,
                        estTerminee: widget.sessions[index].termine ??
                            !widget.sessionToEndIds.contains(widget
                                .sessions[index]
                                .numSeance), // Vdu : le statut termine de la séance est renvoyé normalement par le ws, par historique on regarde également les idNonTermine renvoyés par Mobie_notification si la valeur n'est pas passée
                        onTap: (seance) {
                          widget.onSessionSelected(
                              seance,
                              !widget.sessionToEndIds
                                  .contains(widget.sessions[index].numSeance));
                        },
                      )),
            ),
          ),
          Padding(
              padding: EdgeInsets.only(top: 80),
              child: CreateSessionContainer(
                height: 85,
                onTap: widget.onSessionCreationSelected,
                autoSizeGroup: _group,
                isSession: widget.isSession,
              )),
          Container(
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.all(Radius.circular(16.0)),
            ),
            child: LastSessionContainer(
              height: 125,
              // On ne met plus "!" ici : on passe simplement la valeur de _lastSession
              seance: _lastSession,
              termine: _lastSession != null
                  ? _lastSession!.termine ??
                      !widget.sessionToEndIds.contains(_lastSession!.numSeance)
                  : null,
              isSession: widget.isSession,
              autoSizeGroup: _group,
              onTap: widget.onSessionSelected,
            ),
          )
        ],
      ),
    );
  }
}
