import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';

class ErrorText extends StatelessWidget {
  final String message;

  ErrorText({required this.message});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.0),
      child: Center(
          child: Text(
        message,
        textAlign: TextAlign.center,
        style: TextStyle(
            color: AppTheme.backgroundColor,
            fontSize: FontSizeController.of(context).fontSize),
      )),
    );
  }
}
