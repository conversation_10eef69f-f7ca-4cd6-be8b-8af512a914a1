import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/doc.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';

/// Widget de représentation d'un document

class DocumentTile extends StatelessWidget {
  final Doc doc;
  final bool isReport;
  final void Function(Doc) callbackOpenDoc;

  DocumentTile({
    required this.doc,
    required this.isReport,
    required this.callbackOpenDoc,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => callbackOpenDoc(doc),
        child: Container(
          height: 80.0,
          margin: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
          decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(20.0)),
          ),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 21.0, right: 23.0),
                child: SvgPicture.asset(isReport
                    ? "assets/images/icons/icFile.svg"
                    : "assets/images/icons/icFilePdf.svg"),
              ),
              Flexible(
                child: Text(doc.nom.toLowerCase(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize,
                        fontWeight: FontWeight.w600,
                        fontFamily: "OpenSans")),
              )
            ],
          ),
        ));
  }
}
