import 'package:flutter/material.dart';
import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/models/biologic_data_content.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/bilans_bio/bio_result_element.dart';
import 'package:theradom/widgets/expand_text.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/widgets/multiline_chart.dart';

/// Widget représentant un élément d'analyse biologique

class BioAnalysisElement extends StatefulWidget {
  final BiologicDataContent biologicDataContent;
  final List<dynamic> repDataSetList;
  final void Function(BiologicDataContent) onMaximizeChart;
  final void Function(BiologicAnalysisData) onOpenBiologicalDataContentType2;

  BioAnalysisElement(
      {required this.biologicDataContent,
      required this.repDataSetList,
      required this.onMaximizeChart,
      required this.onOpenBiologicalDataContentType2});

  _BioAnalysisElementState createState() => _BioAnalysisElementState();
}

class _BioAnalysisElementState extends State<BioAnalysisElement> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.biologicDataContent.type == "1") {
      return Container(
          padding:
              EdgeInsets.only(left: 20.0, right: 20.0, top: 16.0, bottom: 5.0),
          margin:
              EdgeInsets.only(left: 20.0, right: 20.0, top: 5.0, bottom: 5.0),
          decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(20.0)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // titre de l'analyse
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.biologicDataContent.typeBilan,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: FontSizeController.of(context).fontSize,
                          color: AppTheme.primaryColor),
                    ),
                  ),
                  SvgButton(
                      asset: "assets/images/icons/icFullScreen.svg",
                      onTap: () =>
                          widget.onMaximizeChart(widget.biologicDataContent))
                ],
              ),

              Container(
                height: 250,
                child: MultilineChart(
                  iLineDataSetList: widget.repDataSetList[0],
                  limitLineList: widget.repDataSetList[1],
                  enableGestures: false,
                ),
              ),

              // commentaire
              widget.biologicDataContent.commentaire == ""
                  ? Container()
                  : ExpandText(
                      widget.biologicDataContent.commentaire,
                      maxLines: 1,
                      dividerColor: Colors.transparent,
                      icon: ChevIcon(
                        assetPath: "assets/images/icons/btChevBlancDw.svg",
                      ),
                      style: TextStyle(
                          fontSize: FontSizeController.of(context).fontSize,
                          color: AppTheme.primaryColor,
                          fontStyle: FontStyle.italic),
                    ),
            ],
          ));
    } else if (widget.biologicDataContent.type == "2") {
      BiologicAnalysisData biologicAnalysisData = BiologicAnalysisData(
          typeBilan: widget.biologicDataContent.typeBilan,
          cDate: widget.biologicDataContent.date,
          donnees: {
            widget.biologicDataContent.typeBilan: widget.biologicDataContent
          },
          dernUser: "",
          pdf: null,
          idPdf: "");

      return BioResultElement(
          biologicAnalysisData: biologicAnalysisData,
          onTap: () =>
              widget.onOpenBiologicalDataContentType2(biologicAnalysisData),
          onDelete: () {});
    } else {
      return Container();
    }
  }
}
