import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/biologic_analysis_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/extensions/datetime_extension.dart';

class BioResultElement extends StatefulWidget {
  final BiologicAnalysisData biologicAnalysisData;
  final void Function() onDelete;
  final void Function() onTap;

  BioResultElement(
      {required this.biologicAnalysisData,
      required this.onTap,
      required this.onDelete});

  _BioResultElementState createState() => _BioResultElementState();
}

class _BioResultElementState extends State<BioResultElement> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 80.0,
        margin: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
        padding: EdgeInsets.only(left: 10.0, right: 10.0, top: 5.0),
        decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(20.0)),
        child: ListTile(
          onTap: widget.onTap,
          leading: SvgPicture.asset(widget.biologicAnalysisData.pdf != null
              ? "assets/images/icons/icFilePdf.svg"
              : "assets/images/icons/icFile.svg"),
          title: Text(
            widget.biologicAnalysisData.typeBilan,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: FontSizeController.of(context).fontSize,
                color: AppTheme.primaryColor),
          ),
          subtitle: Text(
            widget.biologicAnalysisData.cDate.toLanguageString(),
            style: TextStyle(
                fontSize: 16.0,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold),
          ),
          trailing: SvgButton(
              asset: "assets/images/icons/icTrash.svg",
              assetColor: widget.biologicAnalysisData.pdf != null
                  ? AppTheme.tertiaryColor
                  : Colors.transparent,
              onTap: _onDelete),
        ));
  }

  Future<void> _onDelete() async {
    bool? delete = await AlertBox(
            context: context,
            title: widget.biologicAnalysisData.typeBilan,
            description: allTranslations.text('delete_bio_msg'),
            confirmBtnLabel: allTranslations.text('to_delete'))
        .showConfirmation(warning: true);

    if (delete != null && delete) widget.onDelete();
  }
}
