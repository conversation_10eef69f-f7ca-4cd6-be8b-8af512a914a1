import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/MyDatePicker.dart';
import 'package:theradom/extensions/datetime_extension.dart';

class DateRangeAction extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;
  final void Function() onAction;
  final void Function(DateTime, DateTime) setDates;

  DateRangeAction({
    required this.startDate,
    required this.endDate,
    required this.onAction,
    required this.setDates,
  });

  _DateRangeActionState createState() => _DateRangeActionState();
}

class _DateRangeActionState extends State<DateRangeAction> {
  late DateTime _startDateTime;
  late DateTime _endDateTime;
  late DateTime _minDateTime;
  AutoSizeGroup _group = AutoSizeGroup();

  EdgeInsets padding =
      EdgeInsets.only(top: 8.0, bottom: 6.0, left: 30.0, right: 30.0);

  BoxDecoration boxDecoration = BoxDecoration(
      color: AppTheme.primaryColorDarker,
      borderRadius: BorderRadius.all(Radius.circular(18.0)));

  @override
  void initState() {
    super.initState();
    DateTime now = DateTime.now();
    _startDateTime = widget.startDate;
    _endDateTime = widget.endDate;
    _minDateTime = DateTime(now.year - 10, now.month, now.day);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 10.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // date début
            Flexible(
              child: GestureDetector(
                  child: Container(
                    padding: padding,
                    decoration: boxDecoration,
                    child: AutoSizeText(
                      _startDateTime.toLanguageStringShort(),
                      maxLines: 1,
                      group: _group,
                      minFontSize: 12.0,
                      maxFontSize: FontSizeController.of(context).fontSize,
                      style: TextStyle(
                          color: AppTheme.backgroundColor,
                          fontSize: FontSizeController.of(context).fontSize,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                  onTap: () async => await _changeStartDate()),
            ),

            SizedBox(width: 10.0),

            SvgPicture.asset("assets/images/icons/icToTime.svg"),

            SizedBox(width: 10.0),

            // date fin
            Flexible(
                child: GestureDetector(
                    child: Container(
                      padding: padding,
                      decoration: boxDecoration,
                      child: AutoSizeText(
                        _endDateTime.toLanguageStringShort(),
                        maxLines: 1,
                        group: _group,
                        minFontSize: 12.0,
                        maxFontSize: FontSizeController.of(context).fontSize,
                        style: TextStyle(
                            color: AppTheme.backgroundColor,
                            fontSize: FontSizeController.of(context).fontSize,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    onTap: () async => await _changeEndDate()))
          ],
        ));
  }

  Future<void> _changeStartDate() async {
    // appel du picker pour modifier la date de début
    DateTime? selectedDateTime = await MyDatePicker(
            dateHint: allTranslations.text('select_start_date'),
            context: context,
            initialDate: _startDateTime,
            minDate: _minDateTime,
            maxDate: _endDateTime.subtract(Duration(days: 1)))
        .pickDate();
    if (selectedDateTime != null) {
      _setStartDate(selectedDateTime);
      _search();
    }
  }

  void _setStartDate(DateTime selectedDateTime) {
    // set _startDate avec la valeur choisie
    setState(() {
      _startDateTime = selectedDateTime;
    });
  }

  Future<void> _changeEndDate() async {
    // appel du picker pour modifier la date de fin
    DateTime? selectedDateTime = await MyDatePicker(
            dateHint: allTranslations.text('select_end_date'),
            context: context,
            initialDate: _endDateTime,
            minDate: _startDateTime.add(Duration(days: 1)),
            maxDate: DateTime.now())
        .pickDate();
    if (selectedDateTime != null) {
      _setEndDate(selectedDateTime);
      _search();
    }
  }

  void _setEndDate(DateTime selectedDateTime) {
    // set _endDate avec la valeur choisie
    setState(() {
      _endDateTime = selectedDateTime;
    });
  }

  void _search() {
    widget.setDates(_startDateTime, _endDateTime);
    widget.onAction();
  }
}
