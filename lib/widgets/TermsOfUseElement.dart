import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:open_filex/open_filex.dart';

/// Widget de l'élément pour accepter les CGU

class TermsOfUseElement extends StatefulWidget {
  final void Function() onAgreement;

  TermsOfUseElement({required this.onAgreement});

  _TermsOfUseState createState() => _TermsOfUseState();
}

class _TermsOfUseState extends State<TermsOfUseElement> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.all(20.0),
        child: Column(
          children: [
            SizedBox(height: 10.0),
            Text(
              allTranslations.text('TOU'),
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor),
            ),
            SizedBox(height: 50),
            GestureDetector(
              onTap: () => _openTermsOfUsePdf(),
              child: Container(
                height: 50,
                child: Text(
                  allTranslations.text('readTOU'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      decoration: TextDecoration.underline,
                      color: AppTheme.primaryColor,
                      fontSize: 18.0),
                ),
              ),
            ),
            SizedBox(height: 50),
            SizedBox(
              height: 50,
              width: double.maxFinite,
              child: TextButton(
                onPressed: widget.onAgreement,
                child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppTheme.secondaryColor,
                      borderRadius: BorderRadius.all(Radius.circular(16.0)),
                    ),
                    child: Center(
                      child: AutoSizeText(
                        allTranslations.text('i_have_read_and_accepted_tou'),
                        maxLines: 2,
                        maxFontSize: 18.0,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 18.0),
                      ),
                    )),
              ),
            )
          ],
        ));
  }

  Future<void> _openTermsOfUsePdf() async {
    String path =
        await HelperPj.copyAssetToTempDirectory("assets/pdf/cgu_v3.pdf");
    await OpenFilex.open(path);
  }
}
