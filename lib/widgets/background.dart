import 'package:flutter/cupertino.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class Background extends StatelessWidget {
  final Widget child;

  Background({required this.child});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: 10.0,
      ),
      child: Container(
          decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.vertical(top: Radius.circular(25.0))),
          child: child),
    );
  }
}
