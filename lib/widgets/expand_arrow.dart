import 'package:flutter/material.dart';

/// Render mode selection of the [ExpandArrow] widget.
enum ExpandArrowStyle {
  /// Only display an icon, tipically a [Icons.expand_more] icon.
  icon,

  /// Display just the text. It will be dependant on the widget state.
  text,

  /// Display both icon & text at the same tame, using a [Row] widget.
  both,
}

/// This widget is used in both [ExpandChild] & [ExpandText] widgets to show
/// the hidden information to the user. It posses an [animation] parameter.
/// Most widget parameters are customizable.
class ExpandArrow extends StatelessWidget {
  /// String used as a tooltip when the widget is minimized.
  /// Default value set to [MaterialLocalizations.of(context).collapsedIconTapHint].
  final String collapsedHint;

  /// String used as a tooltip when the widget is maximazed.
  /// Default value set to [MaterialLocalizations.of(context).expandedIconTapHint].
  final String expandedHint;

  /// Controlls the arrow's fluid(TM) animation for
  /// the arrow's rotation.
  final Animation<double> animation;

  /// Defines padding value.
  ///
  /// Default value if this widget's icon-only: [EdgeInsets.all(4)].
  /// If text is shown: [EdgeInsets.all(8)].
  final EdgeInsets padding;

  /// Callback to controll what happeds when the arrow is clicked.
  final void Function()? onTap;

  /// Defines arrow's color.
  final Color arrowColor;

  /// Defines arrow's background color.
  final Color arrowBackgroundColor;

  /// Defines arrow's size. Default is [30].
  final double arrowSize;

  /// Icon that will be used instead of an arrow.
  /// Default is [Icons.expand_more].
  final Widget icon;

  /// Style of the displayed message.
  final TextStyle hintTextStyle;

  ///  Defines arrow rendering style. Default is [ExpandArrowStyle.icon].
  final ExpandArrowStyle expandArrowStyle;

  /// Autocapitalise tooltip text.
  final bool capitalArrowtext;

  const ExpandArrow({
    this.collapsedHint = "",
    this.expandedHint = "",
    required this.animation,
    this.padding = const EdgeInsets.all(0),
    this.onTap,
    this.arrowColor = Colors.black,
    this.arrowBackgroundColor = Colors.black,
    this.arrowSize = 16.0,
    this.icon = const Icon(Icons.expand_more),
    this.hintTextStyle = const TextStyle(),
    this.expandArrowStyle = ExpandArrowStyle.icon,
    this.capitalArrowtext = true,
  }) : super();

  @override
  Widget build(BuildContext context) {
    final tooltipMessage =
        animation.value < 0.25 ? collapsedHint : expandedHint;

    final isNotIcon = expandArrowStyle == ExpandArrowStyle.text ||
        expandArrowStyle == ExpandArrowStyle.both;

    final List<Widget> children = [];

    if (expandArrowStyle != ExpandArrowStyle.text) {
      children.add(RotationTransition(
        turns: animation,
        child: icon,
      ));
    }

    if (isNotIcon) {
      children.add(const SizedBox(width: 2.0));
      children.add(DefaultTextStyle(
        style: Theme.of(context).textTheme.titleSmall!,
        child: Text(
          capitalArrowtext == true
              ? tooltipMessage.toUpperCase()
              : tooltipMessage,
          style: hintTextStyle,
        ),
      ));
      children.add(const SizedBox(width: 2.0));
    }

    final arrow = InkResponse(
      containedInkWell: isNotIcon,
      highlightShape: isNotIcon ? BoxShape.rectangle : BoxShape.circle,
      child: Padding(
        padding: padding,
        child: Row(mainAxisSize: MainAxisSize.min, children: children),
      ),
      onTap: onTap,
    );

    return expandArrowStyle != ExpandArrowStyle.icon
        ? arrow
        : Tooltip(
            message: capitalArrowtext == true
                ? tooltipMessage.toUpperCase()
                : tooltipMessage,
            child: arrow,
          );
  }
}
