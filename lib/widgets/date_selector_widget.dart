import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/widgets/MyDatePicker.dart';

/// Widget de sélection de l'heure de début de séance

class DateSelectorWidget extends StatefulWidget {
  final String dateHint;
  final DateTime initialDateTime;
  final DateTime minimumDateTime;
  final DateTime maximumDateTime;
  final bool locked;
  final void Function(DateTime) onValidate;

  DateSelectorWidget(
      {required this.dateHint,
      required this.initialDateTime,
      required this.minimumDateTime,
      required this.maximumDateTime,
      required this.locked,
      required this.onValidate});

  @override
  _DateSelectorWidgetState createState() => _DateSelectorWidgetState();
}

class _DateSelectorWidgetState extends State<DateSelectorWidget> {
  late DateTime _dateTime;

  @override
  void initState() {
    super.initState();
    _dateTime = widget.initialDateTime;
    widget.onValidate(_dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async => await _onTap(),
      child: Container(
        padding: EdgeInsets.all(10.0),
        width: double.maxFinite,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16.0)),
            color: widget.locked
                ? AppTheme.primaryColorDarker
                : AppTheme.backgroundColor,
            boxShadow: [
              BoxShadow(
                  color: AppTheme.primaryBlurColor.withOpacity(0.3),
                  spreadRadius: 0.0,
                  blurRadius: 10.0)
            ]),
        child: Center(
          child: Text(
            _dateTime.toLanguageString(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: widget.locked
                    ? AppTheme.backgroundColor
                    : AppTheme.primaryColor,
                fontSize: 30),
          ),
        ),
      ),
    );
  }

  Future<void> _onTap() async {
    // ouverture du picker de l'heure et update de la valeur choisie
    if (!widget.locked) {
      DateTime? selectedDateTime = await MyDatePicker(
              context: context,
              dateHint: widget.dateHint,
              initialDate: _dateTime,
              minDate: widget.minimumDateTime,
              maxDate: widget.maximumDateTime)
          .pickDate();
      if (selectedDateTime != null) _setTimeOfDay(selectedDateTime);
    }
  }

  void _setTimeOfDay(DateTime dateTime) {
    // set la variable de temps et execute le callback
    setState(() {
      _dateTime = dateTime;
    });
    widget.onValidate(dateTime);
  }
}
