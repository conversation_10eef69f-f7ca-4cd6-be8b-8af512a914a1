import 'package:another_flushbar/flushbar.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/buttons/dialog_button.dart';

class AlertBox {
  final double dialogFontSize = 18.0;
  final AutoSizeGroup group = AutoSizeGroup();

  final BuildContext context;
  final String title;
  final String confirmBtnLabel;
  final String? description;
  final Widget? content;
  final void Function()? dontPopValidationCallback;

  AlertBox(
      {required this.context,
      this.title = "",
      this.confirmBtnLabel = "Ok",
      this.description,
      this.content,
      this.dontPopValidationCallback});

  Widget _buildDialog(Widget child) {
    // dialog
    return WillPopScope(
        onWillPop: () async => false,
        child: Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0)),
          child: Padding(
            padding: EdgeInsets.all(15.0),
            child: IntrinsicHeight(
              child: Stack(
                children: [
                  Align(
                      alignment: Alignment.topLeft,
                      child: Container(
                        height: 30.0,
                        width: 30.0,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppTheme.primaryColor,
                            boxShadow: [
                              BoxShadow(
                                  color: AppTheme.primaryBlurColor
                                      .withOpacity(0.5),
                                  blurRadius: 20.0,
                                  spreadRadius: 0.0)
                            ]),
                        child: GestureDetector(
                            child: SvgPicture.asset(
                                "assets/images/icons/icClear.svg"),
                            onTap: () => Navigator.of(context).pop(false)),
                      )),
                  Padding(
                      padding: EdgeInsets.only(
                          top: 50.0, left: 10.0, right: 10.0, bottom: 10.0),
                      child: child)
                ],
              ),
            ),
          ),
        ));
  }

  void showLoading() {
    showGeneralDialog(
        context: context,
        barrierDismissible: false,
        barrierLabel:
            MaterialLocalizations.of(context).modalBarrierDismissLabel,
        pageBuilder: (BuildContext buildContext, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return SimpleDialog(
            contentPadding: EdgeInsets.all(20.0),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0)),
            children: [
              Center(
                child: Column(
                  children: [
                    PlatformCircularIndicator(darkIndicator: true),
                    SizedBox(
                      height: 10.0,
                    ),
                    Text(
                      title,
                      style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                          fontSize: dialogFontSize),
                    )
                  ],
                ),
              )
            ],
          );
        });
  }

  void showInfo() async {
    // montre la boite de dialogue
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext buildContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return _buildDialog(Column(
          children: [
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontSize: dialogFontSize,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(
              height: 30.0,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: description != null
                    ? Text(
                        description!,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: dialogFontSize),
                      )
                    : content,
              ),
            ),
            SizedBox(
              height: content != null ? 0.0 : 60.0,
            ),
          ],
        ));
      },
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    );
  }

  Future<bool?> showConfirmation({bool warning = false}) async {
    // montre la boite de dialogue avec un bouton de confirmation
    // retourne true pour la confirmation, false sinon
    return await showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext buildContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return _buildDialog(Column(
          children: [
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                  fontSize: dialogFontSize),
            ),
            SizedBox(
              height: 30.0,
            ),
            Expanded(
                child: SingleChildScrollView(
                    child: content == null
                        ? Text(
                            description!,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: dialogFontSize,
                                color: AppTheme.primaryColor),
                          )
                        : content)),
            SizedBox(
              height: content != null ? 20.0 : 60.0,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                DialogButton(
                  label: allTranslations.text('back'),
                  labelColor: AppTheme.primaryColor,
                  backgroundColor: AppTheme.backgroundColor,
                  group: group,
                  onPressed: () => Navigator.of(context).pop(false),
                ),
                SizedBox(
                  width: 10.0,
                ),
                DialogButton(
                    label: confirmBtnLabel,
                    labelColor: AppTheme.primaryColor,
                    backgroundColor: warning
                        ? AppTheme.tertiaryColor
                        : AppTheme.secondaryColor,
                    group: group,
                    onPressed: () => dontPopValidationCallback != null
                        ? dontPopValidationCallback!()
                        : Navigator.of(context).pop(true))
              ],
            )
          ],
        ));
      },
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    );
  }

  Future<dynamic> showAppointment(bool teleconsult, bool canJoin) async {
    // montre la boite de dialogue pour un RDV
    // retourne 1 pour ajouter au calendrier de l'os
    // retourne 2 pour rejoindre la teleconsultation
    AutoSizeGroup _group = AutoSizeGroup();
    return await showGeneralDialog(
        context: context,
        pageBuilder: (BuildContext buildContext, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return _buildDialog(Column(
            children: [
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: dialogFontSize),
              ),
              SizedBox(
                height: 30.0,
              ),
              Expanded(
                child: SingleChildScrollView(
                    child: Text(
                  description!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: AppTheme.primaryColor, fontSize: dialogFontSize),
                )),
              ),
              SizedBox(
                height: 30.0,
              ),
              teleconsult
                  ? GestureDetector(
                      onTap: () =>
                          canJoin ? Navigator.of(context).pop(2) : null,
                      child: Container(
                          height: 50,
                          padding: EdgeInsets.symmetric(horizontal: 10.0),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.0),
                              color: canJoin
                                  ? AppTheme.tertiaryColor
                                  : AppTheme.lightGrey),
                          child: Center(
                            child: AutoSizeText(
                              allTranslations.text('teleconsultation'),
                              maxLines: 1,
                              group: _group,
                              style: TextStyle(
                                  fontSize: dialogFontSize,
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.w600),
                            ),
                          )),
                    )
                  : Container(),
              SizedBox(
                height: 10.0,
              ),
              GestureDetector(
                onTap: () => Navigator.of(context).pop(1),
                child: Container(
                    height: 50,
                    padding: EdgeInsets.symmetric(horizontal: 10.0),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.0),
                        color: AppTheme.secondaryColor),
                    child: Center(
                      child: AutoSizeText(
                        allTranslations.text('add_to_calendar'),
                        maxLines: 1,
                        group: _group,
                        style: TextStyle(
                            fontSize: dialogFontSize,
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w600),
                      ),
                    )),
              )
            ],
          ));
        });
  }

  Future<dynamic> showFlushbar({bool warning = false}) {
    if (warning) HapticFeedback.vibrate();
    return Flushbar(
            duration: !warning ? Duration(seconds: 3) : null,
            icon: !warning
                ? Icon(FontAwesomeIcons.circleCheck,
                    color: AppTheme.primaryColor)
                : Icon(
                    Icons.error_outline,
                    color: AppTheme.primaryColor,
                  ),
            titleText: title.isNotEmpty
                ? Text(
                    title,
                    style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontFamily: "OpenSans",
                        fontSize: dialogFontSize,
                        fontWeight: FontWeight.w600),
                  )
                : null,
            messageText: description != null
                ? Text(
                    description!,
                    style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontFamily: "OpenSans",
                        fontSize: dialogFontSize),
                  )
                : null,
            isDismissible: true,
            shouldIconPulse: false,
            blockBackgroundInteraction: true,
            flushbarPosition: FlushbarPosition.TOP,
            boxShadows: [
              BoxShadow(
                  color: AppTheme.primaryBlurColor,
                  spreadRadius: 0,
                  blurRadius: 10.0)
            ],
            padding: EdgeInsets.all(10.0),
            margin: EdgeInsets.all(8.0),
            backgroundColor:
                !warning ? AppTheme.secondaryColor : AppTheme.tertiaryColor,
            animationDuration: Duration(seconds: 1),
            borderRadius: BorderRadius.circular(16.0))
        .show(context);
  }
}
