import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class DialogBackground extends StatelessWidget {
  final Widget child;

  DialogBackground({required this.child});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0)),
      child: Padding(
        padding: EdgeInsets.all(15.0),
        child: IntrinsicHeight(
          child: IntrinsicWidth(
            child: Stack(
              children: [
                Align(
                    alignment: Alignment.topLeft,
                    child: Container(
                      height: 30.0,
                      width: 30.0,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppTheme.primaryColor,
                          boxShadow: [
                            BoxShadow(
                                color:
                                    AppTheme.primaryBlurColor.withOpacity(0.5),
                                blurRadius: 20.0,
                                spreadRadius: 0.0)
                          ]),
                      child: GestureDetector(
                          child: SvgPicture.asset(
                              "assets/images/icons/icClear.svg"),
                          onTap: () => Navigator.of(context).pop()),
                    )),
                Padding(
                    padding: EdgeInsets.only(
                        top: 50.0, left: 10.0, right: 10.0, bottom: 10.0),
                    child: child)
              ],
            ),
          ),
        ),
      ),
    );
  }
}
