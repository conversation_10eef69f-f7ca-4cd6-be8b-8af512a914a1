import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';

class MyDatePicker {
  final BuildContext context;
  final String? dateHint;
  final String? timeHint;
  final DateTime? initialDate;
  final DateTime? minDate;
  final DateTime? maxDate;
  final TimeOfDay? initialTime;

  MyDatePicker(
      {required this.context,
      this.dateHint,
      this.timeHint,
      this.initialDate,
      this.minDate,
      this.maxDate,
      this.initialTime});

  /// Sélection d'une date
  Future<DateTime?> pickDate() async {
    // affiche le widget de selection d'une date selon l'OS
    DateTime? selectedDateTime;
    selectedDateTime = await showDatePicker(
      context: context,
      initialDate: initialDate!,
      firstDate: minDate!,
      lastDate: maxDate!,
      locale: allTranslations.locale,
      helpText: dateHint,
      fieldLabelText: allTranslations.text("enter_date"),
      errorFormatText: allTranslations.text("invalid_date"),
      errorInvalidText: allTranslations.text("out_of_range_date"),
      builder: (_, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              surface: AppTheme.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );
    return selectedDateTime;
  }

  /// Sélection d'une heure
  Future<TimeOfDay?> pickTime() async {
    // affiche le widget de sélection d'une heure, selon l'OS
    TimeOfDay? selectedTimeOfDay;
    selectedTimeOfDay = await showTimePicker(
        context: context,
        initialTime: initialTime != null
            ? initialTime!
            : TimeOfDay.fromDateTime(DateTime.now()),
        helpText: timeHint,
        builder: (_, child) {
          return Theme(
              data: ThemeData.light().copyWith(
                buttonTheme: ButtonThemeData(),
                primaryColor: AppTheme.primaryColor,
                colorScheme: ColorScheme.light(
                  primary: AppTheme.primaryColor,
                )
                    .copyWith(background: AppTheme.primaryColor)
                    .copyWith(secondary: AppTheme.primaryColor),
              ),
              child: child!);
        });
    return selectedTimeOfDay;
  }

  /// Sélection date, puis heure
  Future<List> pickDateAndTime() async {
    // affiche les picker de date puis d'heure
    DateTime? selectedDateTime;
    TimeOfDay? selectedTimeOfDay;
    selectedDateTime = await pickDate();
    if (selectedDateTime != null) {
      selectedTimeOfDay = await pickTime();
    }
    return [selectedDateTime, selectedTimeOfDay];
  }
}
