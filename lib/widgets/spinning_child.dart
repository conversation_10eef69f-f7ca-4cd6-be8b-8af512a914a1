import 'dart:math';
import 'package:flutter/material.dart';

class SpinningChild extends StatefulWidget {
  final Widget child;
  final double height;
  final double width;

  SpinningChild(
      {required this.child, required this.height, required this.width});

  _SpinningChildState createState() => _SpinningChildState();
}

class _SpinningChildState extends State<SpinningChild>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller =
        AnimationController(vsync: this, duration: Duration(seconds: 2))
          ..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: widget.width,
      child: Center(
        child: AnimatedBuilder(
            animation: _controller,
            builder: (_, child) {
              return Transform(
                transform: Matrix4.rotationY(_controller.value * 2 * pi),
                alignment: FractionalOffset.center,
                child: child,
              );
            },
            child: widget.child),
      ),
    );
  }
}
