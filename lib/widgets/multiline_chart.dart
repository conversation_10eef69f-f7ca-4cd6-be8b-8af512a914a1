import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:theradom/utils/theme/app_theme.dart';

/// Widget pour paraméter l'aperçu d'une courbe
/// Migrated from mp_chart to fl_chart

class Multiline<PERSON>hart extends StatefulWidget {
  final dynamic
      iLineDataSetList; // Can be List<LineChartBarData> or List<dynamic>
  final dynamic limitLineList2; // Can be List<HorizontalLine> or List<dynamic>
  final bool enableGestures;
  // Legacy support
  final dynamic limitLineList;

  const MultilineChart({
    Key? key,
    required this.iLineDataSetList,
    this.limitLineList2,
    this.limitLineList,
    required this.enableGestures,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _MultilineChartState();
}

class _MultilineChartState extends State<MultilineChart> {
  double _fontSize = 16.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: LineChart(
        _createLineChartData(),
        duration: const Duration(milliseconds: 250),
      ),
    );
  }

  LineChartData _createLineChartData() {
    // Convert data to fl_chart format if needed
    final List<LineChartBarData> barDataList =
        _ensureLineChartBarDataList(widget.iLineDataSetList);
    final List<HorizontalLine> horizontalLines = _ensureHorizontalLineList(
        widget.limitLineList2 ?? widget.limitLineList ?? []);

    // Protection contre les données vides
    if (barDataList.isEmpty) {
      print("🔍 CHART DEBUG: Aucune donnée à afficher");
      return LineChartData(
        lineBarsData: [],
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        gridData: FlGridData(show: false),
      );
    }

    return LineChartData(
      lineTouchData: LineTouchData(
        enabled: widget.enableGestures,
        touchTooltipData: LineTouchTooltipData(
          getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
            return touchedBarSpots.map((barSpot) {
              final value = barSpot.y;

              return LineTooltipItem(
                'Valeur: ${value.toStringAsFixed(1)}',
                TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: _fontSize,
                ),
              );
            }).toList();
          },
        ),
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 1,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: AppTheme.primaryColor.withOpacity(0.3),
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: AppTheme.primaryColor.withOpacity(0.3),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: (value, meta) {
              // Simplification pour éviter les erreurs d'extension
              return Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: AppTheme.primaryColorDarker,
                    fontWeight: FontWeight.bold,
                    fontSize: _fontSize,
                  ),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 1,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toStringAsFixed(1),
                style: TextStyle(
                  color: AppTheme.primaryColorDarker,
                  fontWeight: FontWeight.bold,
                  fontSize: _fontSize,
                ),
              );
            },
            reservedSize: 42,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      minX: _getMinX(barDataList),
      maxX: _getMaxX(barDataList),
      minY: _getMinY(barDataList),
      maxY: _getMaxY(barDataList),
      lineBarsData: barDataList,
      extraLinesData: ExtraLinesData(
        horizontalLines: horizontalLines,
      ),
    );
  }

  double _getMinX(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 0;

    double minX = double.infinity;
    for (var lineData in barDataList) {
      if (lineData.spots.isNotEmpty) {
        if (lineData.spots.first.x < minX) {
          minX = lineData.spots.first.x;
        }
      }
    }

    return minX == double.infinity ? 0 : minX;
  }

  double _getMaxX(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 10;

    double maxX = double.negativeInfinity;
    for (var lineData in barDataList) {
      if (lineData.spots.isNotEmpty) {
        if (lineData.spots.last.x > maxX) {
          maxX = lineData.spots.last.x;
        }
      }
    }

    return maxX == double.negativeInfinity ? 10 : maxX;
  }

  double _getMinY(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 0;

    double minY = double.infinity;
    for (var lineData in barDataList) {
      for (var spot in lineData.spots) {
        if (spot.y < minY) {
          minY = spot.y;
        }
      }
    }

    // Add some padding below the minimum value
    return minY == double.infinity ? 0 : minY * 0.9;
  }

  double _getMaxY(List<LineChartBarData> barDataList) {
    if (barDataList.isEmpty) return 10;

    double maxY = double.negativeInfinity;
    for (var lineData in barDataList) {
      for (var spot in lineData.spots) {
        if (spot.y > maxY) {
          maxY = spot.y;
        }
      }
    }

    // Add some padding above the maximum value
    return maxY == double.negativeInfinity ? 10 : maxY * 1.1;
  }

  // Ensure we have a List<LineChartBarData>
  List<LineChartBarData> _ensureLineChartBarDataList(dynamic data) {
    if (data == null) return [];

    if (data is List<LineChartBarData>) {
      return data;
    }

    // If it's a List<dynamic>, check each element
    if (data is List) {
      List<LineChartBarData> result = [];
      for (var item in data) {
        if (item is LineChartBarData) {
          result.add(item);
        } else {
          print(
              "🔍 CHART DEBUG: Skipping non-LineChartBarData item: ${item.runtimeType}");
        }
      }
      return result;
    }

    return [];
  }

  // Ensure we have a List<HorizontalLine>
  List<HorizontalLine> _ensureHorizontalLineList(dynamic data) {
    if (data == null) return [];

    if (data is List<HorizontalLine>) {
      return data;
    }

    // If it's a List<dynamic>, try to convert or return empty
    if (data is List) {
      try {
        return data.cast<HorizontalLine>();
      } catch (e) {
        print("Could not convert limit lines: $e");
        return [];
      }
    }

    return [];
  }
}
