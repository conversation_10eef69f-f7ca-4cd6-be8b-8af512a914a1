import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';

class BackgroundHeader extends StatelessWidget {
  final String label;

  BackgroundHeader({required this.label});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(top: 22.0, bottom: 13.0),
        color: Colors.transparent,
        width: double.maxFinite,
        child: Center(
          child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.0),
              child: AutoSizeText(
                label,
                maxLines: 1,
                maxFontSize: FontSizeController.of(context).fontSize,
                style: TextStyle(
                    color: AppTheme.backgroundColor,
                    fontSize: FontSizeController.of(context).fontSize,
                    fontWeight: FontWeight.w600),
              )),
        ));
  }
}
