import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/extensions/datetime_extension.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';

class CustomDatePicker extends StatefulWidget {
  final DateTime initialSelectedDay;
  final DateTime startDay;
  final DateTime endDay;
  final void Function(DateTime) onDaySelected;

  CustomDatePicker(
      {required this.initialSelectedDay,
      required this.startDay,
      required this.endDay,
      required this.onDaySelected});

  _CustomDatePickerState createState() => _CustomDatePickerState();
}

class _CustomDatePickerState extends State<CustomDatePicker>
    with SingleTickerProviderStateMixin {
  late DateTime _focusedDay;
  late final AnimationController _animationController;
  double _fontSize = 18.0;
  late bool _showLeftIcon;
  late bool _showRightIcon;

  @override
  void initState() {
    super.initState();
    _changeCalendarNavigation(
        DateTime(widget.endDay.year, widget.endDay.month, 1),
        DateTime(widget.endDay.year, widget.endDay.month, 31));
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _animationController.forward();
    _focusedDay = widget.initialSelectedDay;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        borderRadius: BorderRadius.all(Radius.circular(30.0)),
        color: AppTheme.primaryColor,
        child: Container(
          width: MediaQuery.of(context).size.width - 40,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(20.0))),
          child: TableCalendar(
              locale: allTranslations.currentLanguage,
              calendarFormat: CalendarFormat.month,
              startingDayOfWeek: StartingDayOfWeek.monday,
              focusedDay: _focusedDay,
              firstDay: widget.startDay,
              lastDay: widget.endDay,
              availableGestures: AvailableGestures.none,
              availableCalendarFormats: {CalendarFormat.month: ''},
              onPageChanged: (day) {
                setState(() {
                  _changeCalendarNavigation(day, day);
                });
              },
              calendarStyle: CalendarStyle(
                  outsideDaysVisible: false, canMarkersOverflow: false),
              daysOfWeekHeight: 30,
              daysOfWeekStyle: DaysOfWeekStyle(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(
                    Radius.circular(8.0),
                  ),
                  color: AppTheme.primaryColorDarker,
                ),
                dowTextFormatter: (date, locale) {
                  String dayStr = DateFormat.E(locale).format(date);
                  return dayStr[0].toUpperCase() + dayStr.substring(1, 3);
                },
                weekendStyle: TextStyle(
                    fontSize: _fontSize, color: AppTheme.backgroundColor),
                weekdayStyle: TextStyle(
                    fontSize: _fontSize, color: AppTheme.backgroundColor),
              ),
              headerStyle: HeaderStyle(
                  titleTextStyle: TextStyle(
                      fontSize: _fontSize,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.backgroundColor,
                      fontFamily: "OpenSans"),
                  titleTextFormatter: (date, locale) {
                    String str = DateFormat.yMMMM(locale).format(date);
                    return str[0].toUpperCase() + str.substring(1);
                  },
                  leftChevronVisible: _showLeftIcon,
                  rightChevronVisible: _showRightIcon,
                  leftChevronIcon: Icon(FontAwesomeIcons.circleChevronLeft,
                      color: AppTheme.backgroundColor, size: 22),
                  rightChevronIcon: Icon(FontAwesomeIcons.circleChevronRight,
                      color: AppTheme.backgroundColor, size: 22),
                  headerPadding: EdgeInsets.only(top: 15.0, bottom: 15.0),
                  titleCentered: true,
                  formatButtonVisible: false),
              calendarBuilders:
                  CalendarBuilders(selectedBuilder: (context, date, _) {
                // build jour sélectionné par le user
                return FadeTransition(
                  opacity:
                      Tween(begin: 0.0, end: 1.0).animate(_animationController),
                  child: Container(
                    height: 55,
                    margin: EdgeInsets.only(top: 5.0, bottom: 8.0),
                    decoration: BoxDecoration(
                        border: Border.all(color: AppTheme.primaryColor),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.secondaryBlurColor,
                            spreadRadius: 2,
                            blurRadius: 15,
                            offset: Offset(0, 0), // changes position of shadow
                          )
                        ]),
                    child: Container(
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(colors: [
                            AppTheme.secondaryColorDarker,
                            AppTheme.secondaryColor
                          ])),
                      child: Center(
                        child: Text('${date.day}',
                            style: TextStyle(
                                color: AppTheme.primaryColor,
                                fontSize: _fontSize,
                                fontWeight: FontWeight.w600,
                                fontFamily: "OpenSans")),
                      ),
                    ),
                  ),
                );
              }, todayBuilder: (context, date, _) {
                return Container(
                  margin: EdgeInsets.only(top: 5.0, bottom: 8.0),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: AppTheme.backgroundColor)),
                  child: Center(
                    child: Text(
                      '${date.day}',
                      style: TextStyle(
                          fontSize: _fontSize,
                          color: AppTheme.backgroundColor,
                          fontFamily: "OpenSans-SemiBold",
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                );
              }, defaultBuilder: (context, date, _) {
                // build un jour dans le calendrier
                return Container(
                  margin: EdgeInsets.only(top: 5.0, bottom: 8.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${date.day}',
                      style: TextStyle(
                          fontSize: _fontSize,
                          color: AppTheme.backgroundColor,
                          fontFamily: "OpenSans",
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                );
              }, disabledBuilder: (context, date, _) {
                return Container(
                  margin: EdgeInsets.only(top: 5.0, bottom: 8.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${date.day}',
                      style: TextStyle(
                          fontSize: _fontSize,
                          color: AppTheme.backgroundColor.withOpacity(0.3),
                          fontFamily: "OpenSans",
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                );
              }),
              onDaySelected: (selectedDate, _) {
                setState(() {
                  _focusedDay = selectedDate;
                });
                _animationController.forward(from: 0.0);
                widget.onDaySelected(selectedDate);
              },
              selectedDayPredicate: (day) {
                return day.isSameDate(_focusedDay);
              }),
        ),
      ),
    );
  }

  void _changeCalendarNavigation(
      DateTime calendarFirstDay, DateTime calendarLastDay) {
    _showRightIcon = true;
    _showLeftIcon = true;
    if (widget.endDay.month == widget.startDay.month) {
      // same month
      _showLeftIcon = false;
      _showRightIcon = false;
    } else {
      if (calendarFirstDay.isBefore(widget.startDay)) {
        _showLeftIcon = false;
      }
      if (calendarLastDay.isAfter(widget.endDay)) {
        _showRightIcon = false;
      }
    }
  }
}
