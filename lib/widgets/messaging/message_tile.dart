import 'package:flutter/material.dart';
import 'package:theradom/models/message.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/extensions/string_extension.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';

/// Tile d'un mesasge dans la liste

class MessageTile extends StatelessWidget {
  final void Function(Message) onTap;
  final Message message;

  MessageTile({required this.onTap, required this.message});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100.0,
      padding: EdgeInsets.all(10),
      child: ListTile(
        onTap: () => onTap(message),
        contentPadding: EdgeInsets.only(
          left: 20.0,
          right: 20.0,
          top: 5.0,
        ),
        title: Row(
          children: <Widget>[
            SizedBox(
              height: 14.0,
              width: 14.0,
              child: Container(
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: message.dejaLu
                          ? AppTheme.primaryColor
                          : AppTheme.secondaryColor)),
            ),
            SizedBox(width: 11),
            Flexible(
              child: Text("${message.emetteur}",
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      color: AppTheme.backgroundColor,
                      fontWeight: FontWeight.bold),
                  maxLines: 1),
            ),
          ],
        ),
        subtitle: Row(
          children: <Widget>[
            SizedBox(width: 25),
            Flexible(
              child: Padding(
                padding: EdgeInsets.only(top: 5.0),
                child: Text("${message.motif}",
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      color: AppTheme.backgroundColor,
                    ),
                    maxLines: 1),
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ChevIcon(
              assetPath: "assets/images/icons/btChevBleuR.svg",
            ),
            Text(
              message.zDate.formatYYYYMMDD2Language(),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(color: AppTheme.backgroundColor, fontSize: 14),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }
}
