import 'dart:io';
import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/message.dart';
import 'package:theradom/services/repositories/mobile_pj_repository.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/ThumbnailWidget.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';

/// Widget d'affichage des pj d'un message

class ListePjMessage extends StatefulWidget {
  final Message message;

  ListePjMessage({
    required this.message,
  });

  _ListePjMessageState createState() => _ListePjMessageState();
}

class _ListePjMessageState extends State<ListePjMessage> {
  late final Message _message;
  List<Widget> _listPJreceived = [];
  late final Future _futureFetchPj;
  final MobilePjRepository _mobilePjRepository = MobilePjRepository();

  @override
  void initState() {
    super.initState();
    _message = widget.message;
    _futureFetchPj = _mobilePjRepository.fetchFilesnamesAndContent(
        _message.numEnregMessagerie, Config.tableMailing, false);
  }

  @override
  void dispose() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _futureFetchPj,
      builder: (context, AsyncSnapshot snapshot) {
        _listPJreceived.clear();
        if (snapshot.connectionState != ConnectionState.done) {
          // je n'ai pas encore de réponse
          return LinearProgressIndicator(
            backgroundColor: AppTheme.secondaryColor,
            minHeight: 0.5,
          );
        } else {
          // réponse
          _handleSnapshotResponse(snapshot, context);
        }

        return Container(
          height: _listPJreceived.length == 0 ? 0 : 110,
          width: double.maxFinite,
          padding:
              EdgeInsets.only(left: 15.0, right: 15.0, top: 5.0, bottom: 5.0),
          child: CustomScrollbar(
            scrollbarColor: AppTheme.backgroundColor,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: _listPJreceived),
            ),
          ),
        );
      },
    );
  }

  dynamic _handleSnapshotResponse(
      AsyncSnapshot snapshot, BuildContext context) {
    // gestion de la reponse de la fonction appelée pour avoir les pj
    // retourne une erreur ou la liste

    String error;
    List<File> fileList = [];

    if (snapshot.data is String) {
      error = snapshot.data;
      return Center(
        child: Text(
          error,
          style: TextStyle(
              color: AppTheme.tertiaryColor,
              fontSize: FontSizeController.of(context).fontSize),
        ),
      );
    } else {
      fileList = snapshot.data;
      fileList.forEach((file) {
        String name = file.path.split('/').last;

        if ((name.contains(".aac")) ||
            (name.contains(".mp3") || name.contains(".m4a"))) {
          // fichier audio
          _listPJreceived.add(Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.0),
              child: Column(
                children: <Widget>[
                  ThumbnailWidget(
                      image: null, file: file, isAudio: true, onRemoval: null),
                  Container(
                    width: 80,
                    child: Text(
                      name,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12.0, color: AppTheme.backgroundColor),
                    ),
                  )
                ],
              )));
        } else {
          _listPJreceived.add(Padding(
            padding: EdgeInsets.symmetric(horizontal: 5.0),
            child: Column(
              children: <Widget>[
                ThumbnailWidget(
                  image: (name.contains(".jpg") || (name.contains('.png')))
                      ? Image.memory(file.readAsBytesSync())
                      : null,
                  file: file,
                  isAudio: false,
                  onRemoval: null,
                ),
                Container(
                    width: 80,
                    child: Text(
                      name,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12.0, color: AppTheme.backgroundColor),
                    ))
              ],
            ),
          ));
        }
      });

      return Container(
        height: _listPJreceived.length == 0 ? 0 : 110,
        width: double.maxFinite,
        padding: EdgeInsets.all(5),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _listPJreceived),
        ),
      );
    }
  }
}
