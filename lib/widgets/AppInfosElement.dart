import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:open_filex/open_filex.dart';

class AppInfosElement extends StatelessWidget {
  AppInfosElement();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ListTile(
          onTap: () async {
            String path = await HelperPj.copyAssetToTempDirectory("assets/");
            await OpenFilex.open(path);
          },
          leading: Image(
            image: AssetImage(
              "assets/images/logo/logo_ifu.png",
            ),
            height: 20,
          ),
          title: Text(
            allTranslations.text("userGuide"),
            style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 18.0,
                fontWeight: FontWeight.w600),
          ),
        ),
        SizedBox(
          height: 20.0,
        ),
        Text("${allTranslations.text('compatible_from')}:",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 18,
                decoration: TextDecoration.underline)),
        Text(
          "Hemaweb v." +
              Config.appHMWcompatibility +
              ", Hemadialyse v." +
              Config.appHMDcompatibility,
          textAlign: TextAlign.center,
          style: TextStyle(color: AppTheme.primaryColor, fontSize: 18.0),
        ),
        SizedBox(
          height: 20.0,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image(
              height: 25.0,
              image: AssetImage("assets/images/logo/logo_usine.jpg"),
            ),
            SizedBox(
              width: 10.0,
            ),
            Text("${allTranslations.text('publisher')}:",
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 18,
                    decoration: TextDecoration.underline)),
            SizedBox(
              width: 30.0,
            )
          ],
        ),
        Text("${Config.appPublisher},\n${Config.appPublisherAddress}",
            textAlign: TextAlign.center,
            style: TextStyle(color: AppTheme.primaryColor, fontSize: 18)),
        SizedBox(
          height: 20.0,
        ),
        Text(
          "${allTranslations.text('website')}:",
          textAlign: TextAlign.center,
          style: TextStyle(
              color: AppTheme.primaryColor,
              fontSize: 18,
              decoration: TextDecoration.underline),
        ),
        Text(
          Config.appPublisherWebsite,
          textAlign: TextAlign.center,
          style: TextStyle(color: AppTheme.primaryColor, fontSize: 18),
        ),
        SizedBox(
          height: 20.0,
        ),
        Text(
          "Hotline:",
          textAlign: TextAlign.center,
          style: TextStyle(
              decoration: TextDecoration.underline,
              color: AppTheme.primaryColor,
              fontSize: 18),
        ),
        Text(
          Config.appHotlineContact,
          textAlign: TextAlign.center,
          style: TextStyle(color: AppTheme.primaryColor, fontSize: 18),
        ),
        SizedBox(
          height: 20.0,
        ),
        Text(
          "${allTranslations.text('contact')}:",
          textAlign: TextAlign.center,
          style: TextStyle(
              color: AppTheme.primaryColor,
              fontSize: 18,
              decoration: TextDecoration.underline),
        ),
        Text(
          Config.appSupportContact,
          textAlign: TextAlign.center,
          style: TextStyle(color: AppTheme.primaryColor, fontSize: 18),
        ),
        SizedBox(
          height: 30.0,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Image(
                image: AssetImage("assets/images/logo/logo_ce.png"),
                height: 20),
            SizedBox(
              width: 5.0,
            ),
            Text(
              Config.appCEmarkingYear,
              style: TextStyle(color: Colors.black, fontSize: 10.0),
            )
          ],
        ),
        SizedBox(
          height: 30.0,
        ),
      ],
    );
  }
}
