import 'dart:async';
import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/buttons/large_button.dart';
import 'package:record/record.dart' as record;

enum RecordingStatus { Initialized, Stopped, Recording, Paused, Unset }

// ignore: must_be_immutable
class AudioRecorder extends StatefulWidget {
  File? audioFile;
  RecordingStatus recordingStatus = RecordingStatus.Unset;
  AudioRecorder({Key? key}) : super(key: key);

  @override
  State<AudioRecorder> createState() => _AudioRecorderState();
}

class _AudioRecorderState extends State<AudioRecorder> {
  int _recordDuration = 0;
  Timer? _timer;
  final record.AudioRecorder _audioRecorder = record.AudioRecorder();
  String? _path;
  final int _audioFileMaxDurationInMinutes = Config.audioFileMaxDuration;
  final AudioPlayer _audioPlayer = AudioPlayer();
  final double _fontSize = 14.0;

  @override
  void initState() {
    super.initState();
    widget.recordingStatus = RecordingStatus.Initialized;
  }

  Future<void> _start() async {
    try {
      if (await _audioRecorder.hasPermission()) {
        await _audioRecorder.isEncoderSupported(
          record.AudioEncoder.aacLc,
        );
        _path = null;
        widget.audioFile = null;
        await _audioRecorder.start(const record.RecordConfig(),
            path: 'temp_audio.m4a');
        _recordDuration = 0;
        _startTimer();
        widget.recordingStatus = RecordingStatus.Recording;
        setState(() {});
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  Future<void> _stop() async {
    _timer?.cancel();
    _path = await _audioRecorder.stop();
    widget.audioFile = File(_path!);
    widget.recordingStatus = RecordingStatus.Stopped;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: <Widget>[
          Text(
            "${_buildTimer()} / ${_formatNumber(_audioFileMaxDurationInMinutes)}:00",
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 30, fontWeight: FontWeight.w600),
          ),
          SizedBox(
            height: 10.0,
          ),
          Text(
            _recordingStatusToString(),
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: _fontSize, color: Theme.of(context).primaryColor),
          ),
          SizedBox(
            height: 30.0,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.0),
                  child: LargeButton(
                      backgroundColor: widget.recordingStatus !=
                              RecordingStatus.Recording
                          ? widget.recordingStatus == RecordingStatus.Stopped
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).colorScheme.secondary
                          : Theme.of(context).colorScheme.error,
                      titleColor:
                          widget.recordingStatus == RecordingStatus.Stopped
                              ? Theme.of(context).colorScheme.surface
                              : Theme.of(context).primaryColor,
                      title: _buttonLabel(),
                      onTap: _onButtonTap),
                ),
              )
            ],
          ),
          widget.recordingStatus == RecordingStatus.Stopped
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30.0),
                        child: LargeButton(
                            backgroundColor:
                                Theme.of(context).colorScheme.secondary,
                            titleColor: Theme.of(context).primaryColor,
                            title: allTranslations.text('to_listen'),
                            onTap: _playAudio),
                      ),
                    )
                  ],
                )
              : Container()
        ]);
  }

  String _recordingStatusToString() {
    switch (widget.recordingStatus) {
      case RecordingStatus.Recording:
        {
          return allTranslations.text('recording');
        }
      case RecordingStatus.Paused:
        {
          return allTranslations.text('ready_to_record');
        }
      case RecordingStatus.Stopped:
        {
          return allTranslations.text('recording_stopped');
        }
      case RecordingStatus.Initialized:
        {
          return allTranslations.text('ready_to_record');
        }
      default:
        {
          return "";
        }
    }
  }

  String _buttonLabel() {
    String str;
    switch (widget.recordingStatus) {
      case RecordingStatus.Recording:
        {
          str = "Stop";
        }
        break;
      case RecordingStatus.Initialized:
        {
          str = allTranslations.text('start');
        }
        break;
      case RecordingStatus.Stopped:
        {
          str = allTranslations.text('restart');
        }
        break;
      default:
        {
          str = "";
        }
        break;
    }
    return str;
  }

  void _onButtonTap() {
    switch (widget.recordingStatus) {
      case RecordingStatus.Stopped:
        {
          setState(() {
            widget.audioFile = null;
            widget.recordingStatus = RecordingStatus.Initialized;
            _recordDuration = 0;
          });
        }
        break;
      case RecordingStatus.Recording:
        {
          _stop();
        }
        break;
      case RecordingStatus.Paused:
        {}
        break;
      case RecordingStatus.Initialized:
        {
          _start();
        }
        break;
      case RecordingStatus.Unset:
        {}
        break;
    }
  }

  void _playAudio() async {
    if ((widget.recordingStatus == RecordingStatus.Stopped) ||
        (widget.recordingStatus == RecordingStatus.Paused) ||
        (widget.recordingStatus == RecordingStatus.Initialized)) {
      _audioPlayer.play(UrlSource(_path!));
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _audioRecorder.dispose();
    super.dispose();
  }

  String _buildTimer() {
    final String minutes = _formatNumber(_recordDuration ~/ 60);
    final String seconds = _formatNumber(_recordDuration % 60);
    return "$minutes:$seconds";
  }

  String _formatNumber(int number) {
    String numberStr = number.toString();
    if (number < 10) {
      numberStr = '0$numberStr';
    }
    return numberStr;
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      if (_recordDuration < _audioFileMaxDurationInMinutes * 60) {
        setState(() {
          _recordDuration++;
        });
      } else {
        if (widget.recordingStatus == RecordingStatus.Recording) {
          _stop();
        }
      }
    });
  }
}
