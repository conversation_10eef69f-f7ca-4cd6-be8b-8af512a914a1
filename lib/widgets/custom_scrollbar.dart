import 'package:flutter/material.dart';
import 'package:theradom/widgets/custom_scroll_behavior.dart';

class CustomScrollbar extends StatelessWidget {
  final Widget child;
  final Color scrollbarColor;

  CustomScrollbar({required this.child, required this.scrollbarColor});

  @override
  Widget build(BuildContext context) {
    return Theme(
        data: ThemeData(
            highlightColor: scrollbarColor, platform: TargetPlatform.android),
        child: Padding(
          padding: EdgeInsets.only(right: 1.0),
          child: Scrollbar(
              thickness: 3,
              child: ScrollConfiguration(
                  behavior: CustomScrollBehavior(), child: child)),
        ));
  }
}
