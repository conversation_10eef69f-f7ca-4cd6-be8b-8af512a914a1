import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';

class SessionSection extends StatelessWidget {
  final String title;
  final List<Widget> childrenList;
  final bool locked;

  SessionSection(
      {required this.title, required this.childrenList, required this.locked});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(5.0),
      child: Container(
        padding: EdgeInsets.all(5.0),
        decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border:
                Border.all(color: Theme.of(context).primaryColor, width: 1)),
        //color: Colors.cyan,
        child: Column(
          children: <Widget>[
            Padding(
              padding: EdgeInsets.all(5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  Text(
                    title.toUpperCase(),
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.backgroundColor),
                  ),
                ],
              ),
            ),
            Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: childrenList),
          ],
        ),
      ),
    );
  }
}
