import 'package:flutter/material.dart';
import 'package:theradom/models/FieldDescription.dart';
import 'package:theradom/models/session_input_data.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/SessionSection.dart';
import 'package:theradom/widgets/session_details/SmileySelectionRow.dart';

class SessionMonitoringDetailsPainSectionBuilder extends StatefulWidget {
  final String painValue;
  final bool locked;
  final bool normalite;
  final FieldDescription fieldDescription;
  final void Function(String, String, String) onPainValueChanged;

  SessionMonitoringDetailsPainSectionBuilder(
      {required this.painValue,
      required this.locked,
      required this.normalite,
      required this.fieldDescription,
      required this.onPainValueChanged});

  _SessionMonitoringDetailsPainSectionBuilderState createState() =>
      _SessionMonitoringDetailsPainSectionBuilderState();
}

class _SessionMonitoringDetailsPainSectionBuilderState
    extends State<SessionMonitoringDetailsPainSectionBuilder> {
  late int _value;
  late final SessionInputData _sessionInputData;

  @override
  void initState() {
    super.initState();
    _sessionInputData = SessionInputData(
        fieldDescription: widget.fieldDescription, normalite: widget.normalite);
    try {
      _value = int.parse(widget.painValue);
    } catch (e) {
      _value = -1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SessionSection(
      title: allTranslations.text('pain_level'),
      locked: widget.locked,
      childrenList: [
        SmileySelectionRow(
            sessionInputData: _sessionInputData,
            painValue: _value,
            locked: widget.locked,
            onSelection: widget.onPainValueChanged)
      ],
    );
  }
}
