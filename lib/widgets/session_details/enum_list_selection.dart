import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/widgets/buttons/radio_button.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';

class EnumListSelection extends StatefulWidget {
  final String selectedData;
  final List<String> dataList;
  final void Function(String) onTap;

  EnumListSelection(
      {required this.selectedData,
      required this.dataList,
      required this.onTap});

  _EnumListSelectionState createState() => _EnumListSelectionState();
}

class _EnumListSelectionState extends State<EnumListSelection> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.dataList.indexOf(widget.selectedData);
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollbar(
        scrollbarColor: AppTheme.primaryColor,
        child: ListView.builder(
            itemCount: widget.dataList.length,
            itemBuilder: (BuildContext context, int index) {
              return ListTile(
                onTap: () => _selectElement(index),
                leading:
                    RadioButton(picked: index == _selectedIndex ? true : false),
                title: Text(
                  widget.dataList[index],
                  style:
                      TextStyle(color: AppTheme.primaryColor, fontSize: 18.0),
                ),
              );
            }));
  }

  void _selectElement(int index) {
    setState(() {
      _selectedIndex = index;
    });
    widget.onTap(widget.dataList[index]);
  }
}
