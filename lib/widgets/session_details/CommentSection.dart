import 'package:flutter/material.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/ListePjDetailsSession.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/session_details/SessionSection.dart';

/// Zone des commentaires d'un relevé

class CommentSection extends StatefulWidget {
  final SeancePrecedente seance;
  final TextField textField;
  final String suivi;
  final void Function(List<String>) onDatabaseFilesFetched;

  CommentSection(
      {required this.seance,
      required this.textField,
      required this.suivi,
      required this.onDatabaseFilesFetched});

  _CommentSectionState createState() => _CommentSectionState();
}

class _CommentSectionState extends State<CommentSection> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SessionSection(
      title: allTranslations.text("comments"),
      locked: widget.seance.verrou,
      childrenList: [
        // zone de saisie
        Container(
          decoration: BoxDecoration(
              color: widget.seance.verrou
                  ? AppTheme.primaryColor
                  : AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(20.0)),
          child: CustomScrollbar(
            scrollbarColor: AppTheme.backgroundColor,
            child: Padding(
                padding: EdgeInsets.only(top: 5.0, left: 20.0, right: 20.0),
                child: widget.textField),
          ),
        ),

        ListePjDetailsSession(
            seance: widget.seance,
            suivi: widget.suivi,
            onFilesFetched: _updateDbFileNameList)
      ],
    );
  }

  void _updateDbFileNameList(List<String> nameList) {
    // met à jour les noms des pj en base dans session details
    widget.onDatabaseFilesFetched(nameList);
  }
}
