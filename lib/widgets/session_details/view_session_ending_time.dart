import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/buttons/large_button.dart';
import 'package:theradom/widgets/session_details/start_time_session_selector.dart';
import 'package:theradom/widgets/date_selector_widget.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

class ViewSessionEndingTime extends StatefulWidget {
  final DateTime minimumDateTime;
  final DateTime maximumDateTime;
  final TimeOfDay initialEndSessionTime;

  ViewSessionEndingTime(
      {required this.minimumDateTime,
      required this.maximumDateTime,
      required this.initialEndSessionTime});

  _ViewSessionEndingTime createState() => _ViewSessionEndingTime();
}

class _ViewSessionEndingTime extends State<ViewSessionEndingTime> {
  late DateTime _selectedDate;
  late TimeOfDay _selectedTime;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.minimumDateTime;
    _selectedTime = widget.initialEndSessionTime;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // page princiaple
        Scaffold(
          appBar: AppBar(
              toolbarHeight: 100,
              elevation: 0,
              backgroundColor: AppTheme.primaryColor,
              leading: Container(),
              systemOverlayStyle: SystemUiOverlayStyle.light),
          body: Container(
            padding: EdgeInsets.only(
              top: 80.0,
            ),
            child: Column(
              children: [
                Text(
                  allTranslations.text('note_session_end_time'),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontSize: FontSizeController.of(context).fontSize,
                      fontWeight: FontWeight.w600),
                ),
                Expanded(
                  child: Padding(
                    padding:
                        EdgeInsets.only(left: 60.0, right: 60.0, bottom: 30.0),
                    child: Column(
                      children: [
                        SizedBox(height: 50.0),
                        DateSelectorWidget(
                          locked: false,
                          dateHint:
                              allTranslations.text('select_session_end_date'),
                          initialDateTime: _selectedDate,
                          minimumDateTime: widget.minimumDateTime,
                          maximumDateTime: widget.maximumDateTime,
                          onValidate: (DateTime selectedDate) {
                            _selectedDate = selectedDate;
                          },
                        ),
                        SizedBox(height: 20.0),
                        StartTimeSessionSelector(
                          startTime: _selectedTime,
                          locked: false,
                          activateMidnightColor: true,
                          onValidate: (TimeOfDay selectedTime) {
                            setState(() {
                              _selectedTime = selectedTime;
                            });
                          },
                        ),
                        Spacer(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                child: LargeButton(
                                    title: allTranslations.text('cancel'),
                                    titleColor: AppTheme.primaryColor,
                                    backgroundColor: AppTheme.backgroundColor,
                                    onTap: () => Navigator.of(context).pop())),
                            //Spacer(),
                            Expanded(
                                child: LargeButton(
                                    title: allTranslations.text('to_validate'),
                                    titleColor: AppTheme.primaryColor,
                                    backgroundColor: _selectedTime.isMidnight()
                                        ? AppTheme.greyColor
                                        : AppTheme.secondaryColor,
                                    onTap: () {
                                      if (_selectedTime.isMidnight()) {
                                        HapticFeedback.vibrate();
                                      } else {
                                        Navigator.of(context).pop(
                                            [_selectedDate, _selectedTime]);
                                      }
                                    }))
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // icon
        Padding(
          padding: EdgeInsets.only(top: 60.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                  height: 70,
                  width: 70,
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppTheme.backgroundColor,
                      boxShadow: [
                        BoxShadow(
                            color: AppTheme.primaryBlurColor,
                            blurRadius: 20.0,
                            spreadRadius: 0,
                            offset: Offset(0, 10))
                      ]),
                  child: Center(
                    child: Container(
                        height: 25.0,
                        width: 25.0,
                        child: SvgPicture.asset(
                            "assets/images/icons/icClock.svg")),
                  ))
            ],
          ),
        ),
      ],
    );
  }
}
