import 'package:flutter/material.dart';
import 'package:theradom/models/session_input_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/session_details/formfield_element.dart';
import 'package:theradom/widgets/session_details/enum_list_selection.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';

/// Widget énumération dans le formulaire de saisie d'une séance

class EnumFormfield extends StatefulWidget {
  final List<String> dataList;
  final void Function(String, String, String) onValueChanged;
  final String? valeur;
  final bool locked;
  final SessionInputData sessionInputData;

  EnumFormfield(
      {required this.dataList,
      required this.onValueChanged,
      required this.valeur,
      required this.locked,
      required this.sessionInputData});

  _EnumFormfieldState createState() => _EnumFormfieldState();
}

class _EnumFormfieldState extends State<EnumFormfield> {
  late final String _title;
  late String _value;
  String _defaultValue = "- -";

  @override
  void initState() {
    super.initState();
    _title = widget.sessionInputData.fieldDescription.libelle;
    if (widget.sessionInputData.fieldDescription.unite != "")
      _title = _title + " (${widget.sessionInputData.fieldDescription.unite})";
    _value = widget.valeur == null ? _defaultValue : widget.valeur!;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 5.0, bottom: 5.0),
      decoration: BoxDecoration(
          color: widget.locked
              ? AppTheme.primaryColorDarker
              : AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(color: AppTheme.primaryColor)),
      child: FormFieldElement(
        child: ListTile(
            title: Text(
              _title,
              style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  fontWeight: FontWeight.w600,
                  color: widget.locked
                      ? AppTheme.backgroundColor
                      : AppTheme.primaryColor),
            ),
            subtitle: Text(
              _value,
              style: TextStyle(
                color: widget.locked
                    ? AppTheme.backgroundColor
                    : AppTheme.primaryColor,
                fontSize: FontSizeController.of(context).fontSize,
              ),
            ),
            trailing: !widget.locked
                ? ChevIcon(assetPath: "assets/images/icons/btChevBlancDw.svg")
                : null,
            onTap: () => widget.locked ? null : _showEnum(_value)),
        validator: (value) {
          bool alarm = widget.sessionInputData.checkOnFormValidation(_value);
          if (alarm) {
            return widget.sessionInputData.fieldDescription.libelle +
                " " +
                allTranslations.text("not_specified");
          } else {
            return null;
          }
        },
      ),
    );
  }

  void _showEnum(String selectedValue) {
    // ouvre le picker de l'énumération
    AlertBox(
        context: context,
        title: _title,
        confirmBtnLabel: allTranslations.text('to_validate'),
        content: Container(
            height: 200,
            width: double.maxFinite,
            child: EnumListSelection(
              selectedData: selectedValue,
              dataList: widget.dataList,
              onTap: (value) {
                selectedValue = value;
              },
            )),
        dontPopValidationCallback: () async {
          Navigator.of(context).pop();
          await _onValueSelected(selectedValue);
        }).showConfirmation();
  }

  void _updateValue(String newValue) {
    setState(() {
      _value = newValue;
    });
    widget.onValueChanged(widget.sessionInputData.fieldDescription.idBD,
        widget.sessionInputData.fieldDescription.libelle, newValue);
  }

  Future<void> _onValueSelected(String newValue) async {
    // leve une alarme si besoin
    bool repAlarme = widget.sessionInputData.check(newValue);
    if (repAlarme) {
      bool? isValueValidated = await AlertBox(
              context: context,
              title: _title,
              description: "$newValue\n\n" +
                  allTranslations.text('field_value_alert_msg'),
              confirmBtnLabel: allTranslations.text('continue'))
          .showConfirmation();
      if (isValueValidated != null && isValueValidated) {
        _updateValue(newValue);
      } else {
        _showEnum(newValue);
      }
    } else {
      _updateValue(newValue);
    }
  }
}
