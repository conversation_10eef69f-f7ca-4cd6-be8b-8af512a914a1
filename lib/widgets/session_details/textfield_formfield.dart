import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/extensions/string_extension.dart';
import 'package:theradom/utils/session_details/textfield_validators.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/models/session_input_data.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/session_details/formfield_element.dart';

/// Widget de saisie manuelle dans le formulaire d'une séance

class TextfieldFormfield extends StatefulWidget {
  final String value;
  final bool locked;
  final SessionInputData sessionInputData;
  final void Function(String, String, String) onValueUpdated;

  TextfieldFormfield(
      {required this.value,
      required this.locked,
      required this.sessionInputData,
      required this.onValueUpdated});

  @override
  _TextfieldFormfieldState createState() => _TextfieldFormfieldState();
}

class _TextfieldFormfieldState extends State<TextfieldFormfield> {
  late String _title;
  late String _valeur;
  TextEditingController _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _title = widget.sessionInputData.fieldDescription.libelle;
    if (widget.sessionInputData.fieldDescription.unite != "")
      _title = _title + " (${widget.sessionInputData.fieldDescription.unite})";
    _valeur = widget.value; // ???
    _controller.text = _valeur;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 5.0, bottom: 5.0),
      decoration: BoxDecoration(
          color: widget.locked
              ? AppTheme.primaryColorDarker
              : AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(color: AppTheme.primaryColor)),
      child: FormFieldElement(
        child: ListTile(
          title: Text(
            _title,
            style: TextStyle(
                fontSize: FontSizeController.of(context).fontSize,
                fontWeight: FontWeight.w600,
                color: widget.locked
                    ? AppTheme.backgroundColor
                    : AppTheme.primaryColor),
          ),
          subtitle: Text(
            _valeur,
            style: TextStyle(
              color: widget.locked
                  ? AppTheme.backgroundColor
                  : AppTheme.primaryColor,
              fontSize: FontSizeController.of(context).fontSize,
            ),
          ),
          trailing: !widget.locked
              ? SvgPicture.asset("assets/images/icons/icEdit.svg")
              : null,
          onTap: () => !widget.locked ? _openDetailsDialog(_valeur) : null,
        ),
        validator: (value) {
          bool alarm = widget.sessionInputData.checkOnFormValidation(_valeur);
          if (alarm) {
            return widget.sessionInputData.fieldDescription.libelle +
                " " +
                allTranslations.text("not_specified");
          } else {
            return null;
          }
        },
      ),
    );
  }

  void _updateValue(String newValue) {
    setState(() {
      _valeur = newValue;
    });
    widget.onValueUpdated(widget.sessionInputData.fieldDescription.idBD,
        widget.sessionInputData.fieldDescription.libelle, _controller.text);
  }

  void _openDetailsDialog(String value) async {
    // ouvre une boite de dialogue pour renseigner une valeur
    _controller.text = value;
    AlertBox(
            context: context,
            title: _title,
            content: Form(
                key: _formKey,
                child: Container(
                    padding: EdgeInsets.only(bottom: 10.0),
                    decoration: BoxDecoration(
                        color: AppTheme.backgroundColor,
                        borderRadius: BorderRadius.circular(20.0),
                        boxShadow: [
                          BoxShadow(
                              spreadRadius: 0.0,
                              blurRadius: 10.0,
                              color: AppTheme.primaryBlurColor.withOpacity(0.3))
                        ]),
                    child: TextFormField(
                      validator: _validator,
                      style: TextStyle(fontSize: 18.0),
                      autocorrect: false,
                      cursorColor: AppTheme.primaryColor,
                      autofocus: true,
                      enableInteractiveSelection: true,
                      inputFormatters:
                          widget.sessionInputData.textInputFormatterList,
                      keyboardType: widget.sessionInputData.keyboardType,
                      enabled: true,
                      controller: _controller,
                      decoration: InputDecoration(
                          contentPadding: EdgeInsets.all(20.0),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20.0),
                              borderSide: BorderSide.none),
                          hintText: widget.sessionInputData.hint,
                          labelStyle: TextStyle(fontSize: 18.0),
                          counterStyle: TextStyle(color: Colors.transparent)),
                    ))),
            dontPopValidationCallback: () async {
              if (_formKey.currentState!.validate()) {
                Navigator.of(context).pop();
                await _onValueSelected(_controller.text);
              } else {
                HapticFeedback.vibrate();
              }
            },
            confirmBtnLabel: allTranslations.text('to_validate'))
        .showConfirmation();
  }

  Future<void> _onValueSelected(String value) async {
    String formattedValue = _formatValue(value);
    bool showAlarm = widget.sessionInputData.check(formattedValue);
    if (showAlarm) {
      bool? isValueValidated = await AlertBox(
              context: context,
              title: _title,
              description: "$formattedValue\n\n" +
                  allTranslations.text('field_value_alert_msg'),
              confirmBtnLabel: allTranslations.text('to_validate'))
          .showConfirmation();

      if (isValueValidated != null && isValueValidated) {
        _updateValue(formattedValue);
      } else {
        _openDetailsDialog(formattedValue);
      }
    } else {
      _updateValue(formattedValue);
    }
  }

  String _formatValue(String value) {
    switch (widget.sessionInputData.fieldDescription.champSpecial) {
      case "1":
        {
          return value;
        }
      case "2":
        {
          return value;
        }
      default:
        {
          switch (widget.sessionInputData.fieldDescription.format) {
            case "\"float\"":
              {
                return value.formatDecimalNumberInput();
              }
            case "\"entier\"":
              {
                return value.formatIntegerNumberInput();
              }
            default:
              {
                return value;
              }
          }
        }
    }
  }

  String? _validator(String? value) {
    switch (widget.sessionInputData.fieldDescription.champSpecial) {
      case "1":
        {
          // tension format xxx
          return TextFieldValidators.instance.forSpecialField1(value);
        }
      case "2":
        {
          // tension format xxx/xxx
          return TextFieldValidators.instance.forSpecialField2(value);
        }
      default:
        {
          switch (widget.sessionInputData.fieldDescription.format) {
            case "\"float\"":
              {
                return TextFieldValidators.instance.forDecimalNumber(value);
              }
            case "\"entier\"":
              {
                return TextFieldValidators.instance.forInteger(value);
              }
            case "\"alphanumerique\"":
              {
                return TextFieldValidators.instance.forAlphanumericValue(value);
              }
            default:
              {
                return TextFieldValidators.instance.forText(value);
              }
          }
        }
    }
  }
}
