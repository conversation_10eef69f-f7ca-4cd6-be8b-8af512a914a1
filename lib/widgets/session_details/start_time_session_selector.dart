import 'package:flutter/material.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/extensions/timeofday_extension.dart';
import 'package:theradom/widgets/MyDatePicker.dart';

/// Widget de sélection de l'heure de début de séance

class StartTimeSessionSelector extends StatefulWidget {
  final TimeOfDay startTime;
  final bool locked;
  final void Function(TimeOfDay) onValidate;
  final bool
      activateMidnightColor; // affiche le Text en rouge si l'heure est à minuit

  StartTimeSessionSelector(
      {required this.startTime,
      required this.locked,
      required this.onValidate,
      this.activateMidnightColor = false});

  @override
  _StartTimeSessionSelectorState createState() =>
      _StartTimeSessionSelectorState();
}

class _StartTimeSessionSelectorState extends State<StartTimeSessionSelector> {
  late TimeOfDay _startTime;

  @override
  void initState() {
    super.initState();
    _startTime = widget.startTime;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async => await _onTap(),
      child: Container(
        padding: EdgeInsets.all(10.0),
        width: double.maxFinite,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16.0)),
            color: widget.locked
                ? AppTheme.primaryColorDarker
                : AppTheme.backgroundColor,
            boxShadow: [
              BoxShadow(
                  color: AppTheme.primaryBlurColor.withOpacity(0.3),
                  spreadRadius: 0.0,
                  blurRadius: 10.0)
            ]),
        child: Center(
          child: Text(
            _startTime.toLanguageString(),
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: widget.locked
                    ? AppTheme.backgroundColor
                    : widget.activateMidnightColor && _startTime.isMidnight()
                        ? AppTheme.tertiaryColor
                        : AppTheme.primaryColor,
                fontSize: 30),
          ),
        ),
      ),
    );
  }

  Future<void> _onTap() async {
    // ouverture du picker de l'heure et update de la valeur choisie
    if (!widget.locked) {
      TimeOfDay? selectedTime = await MyDatePicker(
              context: context,
              timeHint: allTranslations.text('select_session_start_time'),
              initialTime: _startTime)
          .pickTime();
      if (selectedTime != null) _setTimeOfDay(selectedTime);
    }
  }

  void _setTimeOfDay(TimeOfDay timePicked) {
    // set la variable de temps et execute le callback
    setState(() {
      _startTime = timePicked;
    });
    widget.onValidate(_startTime);
  }
}
