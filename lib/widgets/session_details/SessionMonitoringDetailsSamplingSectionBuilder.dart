import 'dart:async';
import 'dart:io';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/blocs/monitoring_details/bloc_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/event_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/state_monitoring_details.dart';
import 'package:theradom/config.dart';

import 'package:theradom/models/SamplingTimerInfos.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/models/session_sampling_data_to_save.dart';
import 'package:theradom/models/session_sampling_structure.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/sampling_data_timer/sampling_timer_stream.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/views/monitoring/view_sampling_table.dart';
import 'package:theradom/widgets/PlatformCircularIndicator.dart';
import 'package:theradom/widgets/session_details/SessionSection.dart';
import 'package:theradom/extensions/datetime_extension.dart';
// import 'package:vibration/vibration.dart'; // Temporarily commented out

class SessionMonitoringDetailsSamplingSectionBuilder extends StatefulWidget {
  final SeancePrecedente seance;
  final SessionSamplingDataToSave initialSessionSamplingDataToSave;
  final bool isBox;
  final SessionSamplingStructure sessionSamplingStructure;
  final bool normalite;
  final void Function(SessionSamplingDataToSave)? onSamplingDataUpdate;

  SessionMonitoringDetailsSamplingSectionBuilder(
      {required this.seance,
      required this.initialSessionSamplingDataToSave,
      required this.isBox,
      required this.sessionSamplingStructure,
      required this.normalite,
      this.onSamplingDataUpdate});

  _SessionMonitoringDetailsSamplingSectionBuilderState createState() =>
      _SessionMonitoringDetailsSamplingSectionBuilderState();
}

class _SessionMonitoringDetailsSamplingSectionBuilderState
    extends State<SessionMonitoringDetailsSamplingSectionBuilder> {
  late final String _title;
  late final BlocMonitoringDetails _blocMonitoringDetails;
  late bool _isSwitchReminderLocked;
  bool _reloading = false;
  int _reminderDuration = Config.samplingDurationReminder;
  late SessionSamplingDataToSave? _sessionSamplingDataToSave;
  late bool _reminderValue;

  @override
  void initState() {
    super.initState();
    _title = allTranslations
        .text(widget.isBox ? 'hemabox_sampling' : 'manual_sampling');
    _sessionSamplingDataToSave = widget.initialSessionSamplingDataToSave;
    _blocMonitoringDetails = BlocProvider.of<BlocMonitoringDetails>(context);

    if (Config.samplingTimerInfos != null &&
        Config.samplingTimerInfos!.numSeance != null) {
      _isSwitchReminderLocked = true;
      if (Config.samplingTimerInfos!.numSeance == widget.seance.numSeance) {
        // séance pour laquelle le rappel est actif
        _reminderValue = true;
      } else {
        // séance pour laquelle le rappel n'est pas actif
        _reminderValue = false;
      }
    } else {
      _reminderValue = false;
      _isSwitchReminderLocked = false;
    }
    SamplingTimerStream.instance.infosStream
        .listen((event) async => await _onSamplingTimerAlert(event));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener(
      bloc: _blocMonitoringDetails,
      listener: (context, state) {
        if (state is StateMonitoringDetailsSession) {
          if (state.errorDetailsSessionData == null) {
            if (widget.isBox) {
              _sessionSamplingDataToSave = state.sessionSamplingBoxData;
            } else {
              _sessionSamplingDataToSave = state.sessionSamplingManualData;
            }
            setState(() {
              _reloading = false;
            });
          }
        }

        if (state is StateMonitoringDetailsSessionValidation) {
          final errorDetailsData = state.errorDetailsData;
          final errorDrug = state.errorDetailsDrugStatus;
          if ((errorDetailsData == null) &&
              (errorDrug == null) &&
              (!state.rebuild)) {
            setState(() {
              _reloading = true;
            });
            _launchEventReloadSampling();
          }
        }
      },
      child: SessionSection(
        title: _title,
        locked: widget.seance.verrou,
        childrenList: [
          Container(
            padding: EdgeInsets.only(top: 10.0, left: 21.0, right: 21.0),
            decoration: BoxDecoration(
                color: widget.seance.verrou
                    ? AppTheme.primaryColorDarker
                    : AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(16.0)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                !widget.isBox
                    ? Platform.isIOS
                        ? Container()
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                  child: AutoSizeText(
                                allTranslations.text('callback_in_x_min',
                                    {"x": "$_reminderDuration"}),
                                maxFontSize:
                                    FontSizeController.of(context).fontSize,
                                maxLines: 1,
                                style: TextStyle(
                                    color: widget.seance.verrou
                                        ? AppTheme.backgroundColor
                                        : AppTheme.primaryColor,
                                    fontSize:
                                        FontSizeController.of(context).fontSize,
                                    fontWeight: FontWeight.w600),
                              )),
                              CupertinoSwitch(
                                  value: _reminderValue,
                                  dragStartBehavior: DragStartBehavior.start,
                                  activeColor: AppTheme.secondaryColor,
                                  onChanged: (value) => widget.seance.verrou
                                      ? null
                                      : _isSwitchReminderLocked
                                          ? null
                                          : _onSwitchActivated()),
                            ],
                          )
                    : Container(),
                ((_isSwitchReminderLocked) && (_reminderValue == false))
                    ? AutoSizeText(
                        allTranslations.text('already_set_for_date', {
                          "date": Config.samplingTimerInfos!.dateSeance
                              .toLanguageString()
                        }),
                        maxFontSize: 14,
                        style: TextStyle(
                            color: AppTheme.tertiaryColor,
                            fontSize: 14,
                            fontWeight: FontWeight.w600),
                      )
                    : Container(),
                GestureDetector(
                  onTap: () async =>
                      _reloading ? null : await _pushToViewSamplingTable(),
                  child: Container(
                      margin: EdgeInsets.symmetric(vertical: 21.0),
                      height: 64,
                      decoration: BoxDecoration(
                          color: _reloading
                              ? AppTheme.greyColor
                              : AppTheme.secondaryColor,
                          borderRadius: BorderRadius.circular(16.0)),
                      child: Center(
                          child: _reloading
                              ? PlatformCircularIndicator(darkIndicator: true)
                              : AutoSizeText(
                                  allTranslations.text('access_values'),
                                  maxLines: 1,
                                  maxFontSize:
                                      FontSizeController.of(context).fontSize,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: FontSizeController.of(context)
                                          .fontSize,
                                      color: AppTheme.primaryColor),
                                ))),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pushToViewSamplingTable() async {
    // push vers l'écran de détails des valeurs d'échantillonage
    SessionSamplingDataToSave? newSessionSamplingDataToSave =
        await Navigator.push(
      context,
      CupertinoPageRoute(
          builder: (context) => ViewSamplingTable(
                title: _title,
                seance: widget.seance,
                sessionSamplingDataToSave: _sessionSamplingDataToSave,
                structure: widget.sessionSamplingStructure,
                locked: widget.isBox ? true : widget.seance.verrou,
                normalite: widget.normalite,
              )),
    );

    if (newSessionSamplingDataToSave != null) {
      // enregistement de la séance seulement si elle n'est pas vérouillée
      setState(() {
        _sessionSamplingDataToSave = _sessionSamplingDataToSave;
      });
      if (widget.onSamplingDataUpdate != null)
        widget.onSamplingDataUpdate!(newSessionSamplingDataToSave);
      BlocProvider.of<BlocMonitoringDetails>(context)
          .add(EventMonitoringDetailsSaveSessionAfterSamplingEditing());
    }
  }

  Future<void> _onSamplingTimerAlert(List<dynamic> event) async {
    if (event[0] == -3) {
      if (this.mounted) {
        setState(() {
          _isSwitchReminderLocked = false;
          _reminderValue = false;
        });
      }
    }
  }

  void _onSwitchActivated() async {
    await _activerRappel();
    setState(() {
      _isSwitchReminderLocked = true;
      _reminderValue = true;
    });
  }

  Future<void> _activerRappel() async {
    // activer vibration rappel
    DateTime dateNow = DateTime.now();
    TimeOfDay timeNow = TimeOfDay.now();
    SamplingTimerInfos samplingTimerInfos = SamplingTimerInfos(
        dateSeance: widget.seance.dateSeance,
        numSeance: _blocMonitoringDetails.seance.numSeance,
        alertDate: DateTime(dateNow.year, dateNow.month, dateNow.day,
                timeNow.hour, timeNow.minute)
            .add(Duration(minutes: _reminderDuration)));
    // Vibration.vibrate(duration: 1); // Temporarily commented out
    SamplingTimerStream.instance.start(samplingTimerInfos);
  }

  void _launchEventReloadSampling() {
    // lance l'event de rechargement des données de l'échantillonage manuel
    _blocMonitoringDetails.add(EventMonitoringDetailsPreviousSession(
        tabReload: true, estTerminee: false));
  }
}
