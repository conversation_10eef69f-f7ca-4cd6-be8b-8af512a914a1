import 'package:flutter/material.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/widgets/session_details/SessionSection.dart';
import 'package:theradom/widgets/session_details/start_time_session_selector.dart';

class SessionMonitoringDetailsStartTimeSection extends StatelessWidget {
  final String suivi;
  final TimeOfDay heureDebutSeance;
  final bool locked;
  final void Function(TimeOfDay) onTimeSelected;

  SessionMonitoringDetailsStartTimeSection(
      {required this.suivi,
      required this.heureDebutSeance,
      required this.locked,
      required this.onTimeSelected});

  @override
  Widget build(BuildContext context) {
    Widget startTimeWidget = Container();

    if (MonitoringUtils.isMonitoringSession(suivi)) {
      startTimeWidget = SessionSection(
          title: allTranslations.text('session_start_time'),
          childrenList: [
            StartTimeSessionSelector(
                startTime: heureDebutSeance,
                locked: locked,
                onValidate: onTimeSelected,
                activateMidnightColor: true)
          ],
          locked: locked);
    }

    return startTimeWidget;
  }
}
