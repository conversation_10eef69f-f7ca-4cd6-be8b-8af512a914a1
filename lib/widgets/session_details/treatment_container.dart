import 'package:flutter/material.dart';
import 'package:theradom/models/consumable.dart';
import 'package:theradom/models/consumable_details.dart';
import 'package:theradom/models/drug.dart';
import 'package:theradom/models/drug_details.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/treatment_status_selection.dart';
import 'package:theradom/widgets/session_details/details_treatment.dart';
import 'package:theradom/widgets/icons/status_indicator.dart';
import 'package:theradom/widgets/expand_text.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/models/enum_status.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

// classe pour les produits et traitements en séance qui peut avoir deux ou trois états

class TreatmentContainer extends StatefulWidget {
  final Consumable? produit;
  final Drug? traitement;
  final void Function(ConsumableDetails, bool)? onConsumableUpdate;
  final void Function(DrugDetails, bool)? onDrugUpdate;
  final bool locked;
  final DateTime dateDebutSeance;
  final TimeOfDay heureDebutSeance;

  TreatmentContainer({
    this.produit,
    this.traitement,
    this.onConsumableUpdate,
    this.onDrugUpdate,
    required this.locked,
    required this.dateDebutSeance,
    required this.heureDebutSeance,
  });

  @override
  _TreatmentContainerState createState() => _TreatmentContainerState();
}

class _TreatmentContainerState extends State<TreatmentContainer>
    with SingleTickerProviderStateMixin {
  late Consumable? _produit;
  late Drug? _traitement;
  late Status _status;
  late final bool _isConsumableElement;

  @override
  void initState() {
    super.initState();
    _isConsumableElement = widget.produit != null ? true : false;
    _produit = widget.produit;
    _traitement = widget.traitement;
    _isConsumableElement
        ? _setStatus(_produit!.administre, _produit!.boolNonAdministre)
        : _setStatus(_traitement!.administre, _traitement!.boolNonAdministre);
  }

  void _setStatus(bool administre, bool nonAdministre) {
    // mise à jour de _status
    if (nonAdministre) {
      _status = Status.not_taken;
    } else {
      switch (administre) {
        case true:
          {
            _status = Status.taken;
          }
          break;
        case false:
          {
            _status = Status.not_specified;
          }
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 5.0, bottom: 5.0),
      decoration: BoxDecoration(
          color: widget.locked
              ? AppTheme.primaryColorDarker
              : AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(width: 4, color: _obtainStatusColor())),
      child: ListTile(
        title: ExpandText(
          _isConsumableElement ? _produit!.nomTrait : _traitement!.posologie,
          maxLines: 3,
          dividerColor: Colors.transparent,
          overflow: TextOverflow.fade,
          style: TextStyle(
              fontWeight: FontWeight.w600,
              color: widget.locked
                  ? AppTheme.backgroundColor
                  : AppTheme.primaryColor,
              fontSize: FontSizeController.of(context).fontSize),
        ),
        subtitle: Text(
          _isConsumableElement
              ? _status == Status.not_specified
                  ? allTranslations.text(
                      _produit!.nbPrescrit > 1
                          ? 'prescribed_plural'
                          : 'prescribed_singular',
                      {
                          "format": "uppercase",
                          "number":
                              _produit!.nbPrescrit.toString().contains(".0")
                                  ? "${_produit!.nbPrescrit.round()}"
                                  : "${_produit!.nbPrescrit}"
                        })
                  : _status == Status.taken
                      ? _produit!.heureAdministration.toLanguageString()
                      : _produit!.motifNonAdmin
              : _status == Status.not_specified
                  ? MonitoringUtils.getNumTypeLabel(_traitement!.numType)
                      .toUpperCase()
                  : _status == Status.taken
                      ? _traitement!.heureAdministration.toLanguageString()
                      : _traitement!.motifNonAdmin,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
              color: widget.locked
                  ? AppTheme.backgroundColor.withOpacity(0.3)
                  : AppTheme.primaryColor.withOpacity(0.3),
              fontSize: 14,
              fontWeight: FontWeight.w600),
        ),
        leading: GestureDetector(
          child: StatusIndicator(status: _status),
          onTap: () async => widget.locked || _status != Status.not_specified
              ? null
              : await _openStatusSelectionAndDetailedDialogs(_status),
          onDoubleTap: () => widget.locked || _status != Status.not_specified
              ? null
              : ((_isConsumableElement) && (_status == Status.not_specified))
                  ? _quickConsumableUpdate()
                  : null,
        ),
        trailing: SvgButton(
          asset: "assets/images/icons/icEdit.svg",
          assetColor: _status == Status.not_specified
              ? Colors.transparent
              : widget.locked
                  ? AppTheme.backgroundColor
                  : null,
          onTap: () => _status == Status.not_specified
              ? null
              : _showDetailedDialog(_status),
        ),
      ),
    );
  }

  Future<void> _openStatusSelectionAndDetailedDialogs(
      Status initialStatus) async {
    Status? selectedStatus = await showDialog(
        context: context,
        barrierColor: Colors.transparent,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return TreatmentStatusSelection(
            initialStatus: _status,
            treatment: _isConsumableElement ? _produit : _traitement,
          );
        });

    if (selectedStatus != null) {
      if (selectedStatus == Status.not_specified) {
        setState(() {
          _setStatus(false, false);
        });
        _updateSessionTreatmentOrConsumable();
      } else {
        _showDetailedDialog(selectedStatus);
      }
    }
  }

  void _quickConsumableUpdate() {
    ConsumableDetails consumable = ConsumableDetails(
        nom: _produit!.nomTrait,
        administre: true,
        nombre: _produit!.nombre,
        heure: TimeOfDay.now(),
        numLot: _produit!.numLot,
        motifNonAdmin: "",
        consumableID: _produit!.numTraitement,
        date: widget.dateDebutSeance);
    _updateProduit(consumable);
    widget.onConsumableUpdate!(consumable, _produit!.boolNonAdministre);
  }

  Color _obtainStatusColor() {
    // retourne la couleur en fonction de Status
    Color color;
    switch (_status) {
      case Status.not_specified:
        {
          color = AppTheme.backgroundColor;
        }
        break;
      case Status.taken:
        {
          color = AppTheme.secondaryColor;
        }
        break;
      case Status.not_taken:
        {
          color = AppTheme.tertiaryColor;
        }
        break;
    }
    return color;
  }

  void _updateSessionTreatmentOrConsumable() {
    if (_isConsumableElement) {
      ConsumableDetails consumable = ConsumableDetails(
          consumableID: _produit!.numTraitement, administre: false);
      widget.onConsumableUpdate!(consumable, false);
    } else {
      DrugDetails drug = DrugDetails(
        administre: false,
        drugID: _traitement!.numTraitement,
      );
      widget.onDrugUpdate!(drug, false);
    }
  }

  void _showDetailedDialog(Status status) async {
    dynamic response = await showDialog(
        context: context,
        barrierDismissible: true,
        barrierColor: Colors.transparent,
        builder: (context) {
          return DetailsTreatment(
            nomTrait: _isConsumableElement
                ? _produit!.nomTrait
                : _traitement!.nomTrait,
            posologie: _isConsumableElement
                ? _produit!.nomTrait
                : _traitement!.posologie,
            id: _isConsumableElement
                ? _produit!.numTraitement
                : _traitement!.numTraitement,
            initialStatus: status,
            currentStatus: _status,
            numLot: _isConsumableElement
                ? _produit!.numLot ?? ""
                : _traitement!.numLot ?? "",
            number:
                _isConsumableElement ? _produit!.nombre : _traitement!.nombre,
            numberPresc: _isConsumableElement
                ? _produit!.nbPrescrit
                : _traitement!.nbPrescrit,
            dose: _isConsumableElement ? null : _traitement!.dose,
            dosePresc: _isConsumableElement ? null : _traitement!.dosePrescrite,
            motif: _isConsumableElement
                ? _produit!.motifNonAdmin
                : _traitement!.motifNonAdmin,
            timeTaken: _status == Status.not_specified
                ? TimeOfDay.now()
                : _isConsumableElement
                    ? _produit!.heureAdministration
                    : _traitement!.heureAdministration,
            anticoagulant: _isConsumableElement
                ? false
                : ((_traitement!.multiDosesEnSeance) &&
                        (_traitement!.anticoagulantEntretien != 0))
                    ? true
                    : false,
            heureArret: _isConsumableElement
                ? TimeOfDay.now()
                : _traitement!.anticoagulantArret,
            isLocked: widget.locked,
            dateDebutSeance: widget.dateDebutSeance,
            heureDebutSeance: widget.heureDebutSeance,
            importantMessage: _isConsumableElement ? "" : _traitement!.message,
          );
        });

    if (response != null) {
      if (_isConsumableElement) {
        ConsumableDetails consumable = response;
        _updateProduit(consumable);
        widget.onConsumableUpdate!(consumable, _produit!.boolNonAdministre);
      } else {
        DrugDetails drug = response;
        _updateTraitement(drug);
        widget.onDrugUpdate!(drug, _traitement!.boolNonAdministre);
      }
    }
  }

  void _updateProduit(ConsumableDetails consumable) {
    _setStatus(consumable.administre, !consumable.administre);
    setState(() {
      _produit!.numLot = consumable.numLot;
      _produit!.heureAdministration = consumable.heure!;
      _produit!.motifNonAdmin = consumable.motifNonAdmin!;
      _produit!.nombre = consumable.nombre!;
      _produit!.administre = consumable.administre;
      _produit!.boolNonAdministre = !consumable.administre;
    });
  }

  void _updateTraitement(DrugDetails drug) {
    _setStatus(drug.administre, !drug.administre);
    setState(() {
      _traitement!.numLot = drug.numLot;
      _traitement!.administre = drug.administre;
      _traitement!.anticoagulantArret = drug.heureArret!;
      _traitement!.heureAdministration = drug.heure!;
      _traitement!.boolNonAdministre = !drug.administre;
      _traitement!.nombre = drug.nombre!;
      _traitement!.dose = drug.dose!;
      _traitement!.dosePrescrite = _traitement!.message.isNotEmpty
          ? drug.dose!
          : _traitement!.dosePrescrite;
      _traitement!.motifNonAdmin = drug.motifNonAdmin!;
      if (_traitement!.message.isNotEmpty) _traitement!.message = "";
    });
  }
}
