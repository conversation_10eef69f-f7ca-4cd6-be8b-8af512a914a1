import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/monitoring_details/bloc_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/event_monitoring_details.dart';
import 'package:theradom/blocs/monitoring_details/state_monitoring_details.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/services/repositories/mobile_pj_repository.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/MultiImagePickerUtils.dart';
import 'package:theradom/utils/RecordAudioUtils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/CustomBottomSheet.dart';
import 'package:theradom/widgets/ThumbnailWidget.dart';

/// Gestion des fichiers en base, ajout suppression et affichage

class ListePjDetailsSession extends StatefulWidget {
  final SeancePrecedente seance;
  final String suivi;
  final void Function(List<String>) onFilesFetched;

  ListePjDetailsSession({
    required this.seance,
    required this.suivi,
    required this.onFilesFetched,
  });

  @override
  _ListePjDetailsSessionState createState() => _ListePjDetailsSessionState();
}

class _ListePjDetailsSessionState extends State<ListePjDetailsSession> {
  late final BlocMonitoringDetails _blocMonitoringDetails;
  late final String _table;
  int _nbPjMaxToSelect = 1;
  late final int _nbPjMaxSaved;
  List<Widget> _pjList = [];
  late Future _future;
  late final bool _isSession;
  bool _showError = false;
  final MobilePjRepository _mobilePjRepository = MobilePjRepository();

  @override
  void initState() {
    super.initState();
    _isSession = MonitoringUtils.isMonitoringSession(widget.suivi);
    _nbPjMaxSaved = _isSession ? 5 : 3;
    _blocMonitoringDetails = BlocProvider.of<BlocMonitoringDetails>(context);
    _table = MonitoringUtils.getCorrespondingTable(widget.suivi);
    _future = _mobilePjRepository.fetchFilesnamesAndContent(
        widget.seance.numSeance, _table, _isSession);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener(
      bloc: _blocMonitoringDetails,
      listener: (context, state) {
        if (state is StateMonitoringDetailsReloadFiles) {
          if (state.error == null) {
            //reload la liste
            setState(() {
              _future = _mobilePjRepository.fetchFilesnamesAndContent(
                  widget.seance.numSeance, _table, _isSession);
            });
          }
        }
      },
      child: FutureBuilder(
          future: _future,
          builder: (context, AsyncSnapshot snapshot) {
            _pjList.clear();
            if (snapshot.connectionState != ConnectionState.done) {
              // je n'ai pas encore de réponse
              return Container(
                  padding: EdgeInsets.all(5.0),
                  child: LinearProgressIndicator(
                      backgroundColor: AppTheme.secondaryColor,
                      minHeight: 0.5));
            } else {
              // réponse

              return Column(
                  children: [
                _handleSnapshotResponse(snapshot, context),
                ((!widget.seance.verrou) &&
                        (_pjList.length < _nbPjMaxSaved) &&
                        (!_showError))
                    ? ListTile(
                        onTap: () => _showBottomSheet(),
                        tileColor: AppTheme.primaryColor,
                        leading: SvgPicture.asset(
                          "assets/images/icons/icPaperclip.svg",
                        ),
                        title: Text(
                          allTranslations.text('add_attachement'),
                          style: TextStyle(
                            fontSize: FontSizeController.of(context).fontSize,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.backgroundColor,
                          ),
                        ),
                      )
                    : Container(),
              ].reversed.toList());
            }
          }),
    );
  }

  Widget _handleSnapshotResponse(AsyncSnapshot snapshot, BuildContext context) {
    // gestion de la reponse de la fonction appelée pour avoir les pj
    // retourne une erreur ou la liste

    String error;
    List<File> fileList;
    List<String> names = [];

    if (snapshot.data is String) {
      _showError = true;
      error = snapshot.data;
      return ListTile(
          title: Text(
            error,
            style: TextStyle(
                color: AppTheme.backgroundColor,
                fontSize: FontSizeController.of(context).fontSize),
          ),
          trailing: IconButton(
            icon: Icon(Icons.refresh, color: AppTheme.backgroundColor),
            onPressed: () {
              setState(() {
                _future = _mobilePjRepository.fetchFilesnamesAndContent(
                    widget.seance.numSeance, _table, _isSession);
              });
            },
          ));
    } else {
      _showError = false;
      fileList = snapshot.data;
      fileList.forEach((file) {
        String name = file.path.split('/').last;
        names.add(name);
        if ((name.contains(".aac")) ||
            (name.contains(".mp3") || (name.contains(".m4a")))) {
          // fichier audio
          _pjList.add(Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.0),
              child: Column(
                children: <Widget>[
                  ThumbnailWidget(
                      image: null,
                      file: file,
                      isAudio: true,
                      onRemoval:
                          widget.seance.verrou ? null : _launchEventDeleteFile),
                  Container(
                    width: 80,
                    child: Text(
                      name,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12, color: AppTheme.backgroundColor),
                    ),
                  )
                ],
              )));
        } else {
          // fichier image
          _pjList.add(Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.0),
              child: Column(
                children: <Widget>[
                  ThumbnailWidget(
                      image: Image.memory(file.readAsBytesSync()),
                      file: file,
                      isAudio: false,
                      onRemoval:
                          widget.seance.verrou ? null : _launchEventDeleteFile),
                  Container(
                    width: 80,
                    child: Text(
                      name,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12, color: AppTheme.backgroundColor),
                    ),
                  )
                ],
              )));
        }
      });

      widget.onFilesFetched(names);

      return Container(
        height: _pjList.length == 0 ? 0 : 110,
        width: double.maxFinite,
        padding:
            EdgeInsets.only(top: 5.0, bottom: 5.0, left: 15.0, right: 15.0),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
              mainAxisAlignment: MainAxisAlignment.center, children: _pjList),
        ),
      );
    }
  }

  Future<void> _showBottomSheet() async {
    int? choice = await CustomBottomSheet(
      context: context,
      labels: [allTranslations.text('photos'), allTranslations.text('audio')],
    ).show();
    if (choice != null) {
      await _onBottomSheetChoice(choice);
    }
  }

  Future<void> _onBottomSheetChoice(int i) async {
    switch (i) {
      case 0:
        {
          await _pickImageInGallery();
        }
        break;
      case 1:
        {
          _pickAudioFile();
        }
    }
  }

  Future<void> _pickImageInGallery() async {
    List list = await MultiImagePickerUtils.pickImagesFromGallery(
        context, _nbPjMaxToSelect);
    if (list[1].length > 0) _handleFile(list[1].first);
  }

  void _handleFile(File? file) async {
    // gère les fichiers image sélectionnés
    if (file != null) {
      _launchEventSaveFile(file);
    }
  }

  void _pickAudioFile() async {
    File? audioFile;
    bool continuer =
        await RecordAudioUtils.recordAnotherAudioFile(context, audioFile);
    if (continuer) {
      List audioResponseList =
          await RecordAudioUtils.showAlertAudioRecorder(context);
      bool isRecorded = audioResponseList[0];
      if (isRecorded) {
        File audioFile = audioResponseList[1];
        _handleFile(audioFile);
      }
    }
  }

  void _launchEventDeleteFile(File fileToDelete) {
    // lance l'event de suppression de la pj
    _blocMonitoringDetails
        .add(EventMonitoringDetailsDeleteCommentFile(file: fileToDelete));
  }

  void _launchEventSaveFile(File file) {
    _blocMonitoringDetails.add(EventMonitoringDetailsSaveFile(file: file));
  }
}
