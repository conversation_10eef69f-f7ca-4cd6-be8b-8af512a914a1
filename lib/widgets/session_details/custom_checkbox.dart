import 'package:flutter/material.dart';
import 'package:theradom/models/session_input_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';

/// Widget pour un booléen du formulaire de saisie de la séance

class CustomCheckbox extends StatefulWidget {
  final bool initialValue;
  final void Function(String, String, dynamic) onValueChanged;
  final bool locked;
  final SessionInputData sessionInputData;

  CustomCheckbox(
      {required this.initialValue,
      required this.onValueChanged,
      required this.locked,
      required this.sessionInputData});

  _CustomCheckboxState createState() => _CustomCheckboxState();
}

class _CustomCheckboxState extends State<CustomCheckbox> {
  late final String _title;
  late bool value;

  @override
  void initState() {
    super.initState();
    _title = widget.sessionInputData.fieldDescription.libelle;
    if (widget.sessionInputData.fieldDescription.unite != "")
      _title = _title + " (${widget.sessionInputData.fieldDescription.unite})";
    value = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async => await _checkValue(!value),
      child: Container(
        padding: EdgeInsets.only(top: 5.0, bottom: 5.0),
        margin: EdgeInsets.only(top: 5.0, bottom: 5.0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(color: AppTheme.primaryColor),
            color: widget.locked
                ? AppTheme.primaryColorDarker
                : AppTheme.backgroundColor),
        child: ListTile(
            title: Text(
              _title,
              style: TextStyle(
                  color: widget.locked
                      ? AppTheme.backgroundColor
                      : AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                  fontSize: FontSizeController.of(context).fontSize),
            ),
            leading: Container(
                width: 40.0,
                height: 40.0,
                decoration: BoxDecoration(
                    color: value ? AppTheme.secondaryColor : AppTheme.lightGrey,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                          color: AppTheme.primaryBlurColor.withOpacity(0.6),
                          blurRadius: 10.0,
                          spreadRadius: 0.0)
                    ],
                    border:
                        Border.all(width: 2.0, color: AppTheme.primaryColor)),
                child: Icon(Icons.check,
                    color:
                        value ? AppTheme.primaryColor : Colors.transparent))),
      ),
    );
  }

  void _updateValue() {
    // update de la valeur du booléen
    setState(() {
      value = !value;
    });
    widget.onValueChanged(widget.sessionInputData.fieldDescription.idBD,
        widget.sessionInputData.fieldDescription.libelle, value);
  }

  Future<void> _checkValue(bool newValue) async {
    // vérifie si une alerte doit être affichée et l'affiche si besoin
    if (!widget.locked) {
      bool repAlarme = widget.sessionInputData.check(newValue);
      if (repAlarme) {
        String message = (newValue
                ? allTranslations.text('yes')
                : allTranslations.text('no')) +
            "\n\n" +
            allTranslations.text('field_value_alert_msg');
        bool? isValueValidated = await AlertBox(
                context: context,
                title: _title,
                description: message,
                confirmBtnLabel: allTranslations.text('continue'))
            .showConfirmation();
        if (isValueValidated != null && isValueValidated) {
          _updateValue();
        }
      } else {
        _updateValue();
      }
    }
  }
}
