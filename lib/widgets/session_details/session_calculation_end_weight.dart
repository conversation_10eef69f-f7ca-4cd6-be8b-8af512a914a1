import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
//import 'package:theradom/models/FieldDescription.dart';
import 'package:theradom/models/FiledCalculation.dart';
import 'package:theradom/models/area_structure.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsFieldBuilder.dart';

class SessionCalculationEndWeight extends StatefulWidget {
  final AreaStructure poidsApresAS;
  final FieldCalculation fieldCalculation;
  final bool normalite;
  final bool locked;
  final void Function(String, String, dynamic) onValueSelected;

  SessionCalculationEndWeight(
      {required this.poidsApresAS,
      required this.fieldCalculation,
      required this.normalite,
      required this.locked,
      required this.onValueSelected});

  _SessionCalculationEndWeightState createState() =>
      _SessionCalculationEndWeightState();
}

class _SessionCalculationEndWeightState
    extends State<SessionCalculationEndWeight> {
  late double _poidsPerdu;
  //late final FieldDescription _pertePoidsFieldDescription;
  late final String _calculatedFieldLabel;

  @override
  void initState() {
    super.initState();
    _poidsPerdu = _calculatePoidsPerdu();
    _calculatedFieldLabel = widget.poidsApresAS.fieldID.unite.isEmpty
        ? allTranslations.text('lost_weight')
        : "${allTranslations.text('lost_weight')} (${widget.poidsApresAS.fieldID.unite})";
    /*
    // poids à perdre
    _pertePoidsFieldDescription = FieldDescription(
      idBD: "",
      libelle: "",
      champSpecial: widget.poidsApresAS.fieldID.champSpecial,
      format: widget.poidsApresAS.fieldID.format,
      valeur: widget.poidsApresAS.fieldID.valeur,
      unite: widget.poidsApresAS.fieldID.unite,
      typeAlerte: widget.poidsApresAS.fieldID.typeAlerte,
      zone: widget.poidsApresAS.fieldID.zone,
      order: widget.poidsApresAS.fieldID.order,
      typeOperateur: widget.poidsApresAS.fieldID.typeOperateur
    );
     */
    widget.fieldCalculation.addListener(_updatePoidsPerdu);
  }

  @override
  void dispose() {
    widget.fieldCalculation.removeListener(_updatePoidsPerdu);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // poids apres
        SessionMonitoringDetailsFieldBuilder(
            valeur: widget.fieldCalculation.getPoidsApres
                .toString()
                .replaceAll(",", "."),
            normalite: widget.normalite,
            locked: widget.locked,
            fieldDescription: widget.poidsApresAS.fieldID,
            onValueSelected: _updateCalculationAndChangeSelectedValue),

        // perte de poids
        Container(
          margin: EdgeInsets.only(top: 5.0, bottom: 5.0),
          decoration: BoxDecoration(
              color: AppTheme.secondaryColor,
              borderRadius: BorderRadius.circular(16.0),
              border: Border.all(color: AppTheme.primaryColor)),
          child: ListTile(
            title: Text(
              _calculatedFieldLabel,
              style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor),
            ),
            subtitle: Text(
              _poidsPerdu.toString().replaceAll(".", ","),
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: FontSizeController.of(context).fontSize,
              ),
            ),
          ),
        )
      ],
    );
  }

  void _updateCalculationAndChangeSelectedValue(
      String idBd, String libelle, dynamic value) {
    _updateVar(idBd, value);
    widget.onValueSelected(idBd, libelle, value);
    _updatePoidsPerdu();
  }

  void _updateVar(String idBd, dynamic value) {
    switch (idBd) {
      case Config.idPoidsApres:
        {
          // poids sec
          widget.fieldCalculation.setPoidsApres =
              double.parse(value.toString().replaceAll(",", "."));
        }
        break;
    }
  }

  double _calculatePoidsPerdu() {
    double poidsPerdu = widget.fieldCalculation.getPoidsDebut +
        widget.fieldCalculation.getApportsRealises -
        widget.fieldCalculation.getPoidsApres;
    poidsPerdu = double.parse(poidsPerdu.toStringAsFixed(1));
    return poidsPerdu;
  }

  void _updatePoidsPerdu() {
    setState(() {
      _poidsPerdu = _calculatePoidsPerdu();
    });
  }
}
