import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/session_input_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/session_details/formfield_element.dart';

class SmileySelectionRow extends StatefulWidget {
  final int painValue; // valeur reçue (à partir de 0), -1 si rien sélectionné
  final bool locked;
  final SessionInputData sessionInputData;
  final void Function(String, String, String) onSelection;

  SmileySelectionRow(
      {required this.painValue,
      required this.locked,
      required this.sessionInputData,
      required this.onSelection});

  _SmileySelectionRow createState() => _SmileySelectionRow();
}

class _SmileySelectionRow extends State<SmileySelectionRow> {
  final int _nbValEchelleDouleur = 6;
  late int _value;
  late final List<int> _painLvlList;
  final List<String> _painStrList = [
    allTranslations.text('none'),
    allTranslations.text('mild'),
    allTranslations.text('moderate'),
    allTranslations.text('high'),
    allTranslations.text('very_high'),
    allTranslations.text('extreme')
  ];

  final _autoSizeGroup = AutoSizeGroup();

  @override
  void initState() {
    super.initState();
    _value = widget.painValue - 1;
    _painLvlList = List.generate(_nbValEchelleDouleur, (index) => index);
  }

  @override
  Widget build(BuildContext context) {
    return FormFieldElement(
      value: _value,
      validator: (value) {
        if (_value == -1) {
          return allTranslations.text('pain_level') +
              " " +
              allTranslations.text('not_specified');
        }
        return null;
      },
      child: CustomScrollbar(
        scrollbarColor: AppTheme.backgroundColor,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(bottom: 8.0),
          scrollDirection: Axis.horizontal,
          child: Container(
            padding: EdgeInsets.all(10.0),
            decoration: BoxDecoration(
                color: widget.locked
                    ? AppTheme.primaryColorDarker
                    : AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(20.0)),
            child: Row(
              children: List.generate(_painLvlList.length,
                  (index) => _buildSmileyWidget(_painLvlList[index])),
            ),
          ),
        ),
      ),
    );
  }

  void _onTap(int newVal) {
    // sur clique d'un smiley, déclenche les animation et met-à-jour la valeur
    if (newVal != _value) {
      _checkValue(newVal);
    }
  }

  Widget _buildSmileyWidget(int i) {
    // build smiley
    return Container(
        padding: EdgeInsets.all(15.0),
        margin: EdgeInsets.symmetric(horizontal: 15.0),
        decoration: BoxDecoration(
            color: i == _value
                ? widget.locked
                    ? AppTheme.backgroundColor
                    : AppTheme.primaryColor
                : Colors.transparent,
            borderRadius: BorderRadius.circular(16.0)),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                  color: AppTheme.painColorList[i],
                  shape: BoxShape.circle,
                  border: Border.all(color: AppTheme.primaryColor)),
              child: GestureDetector(
                onTap: () => widget.locked ? null : _onTap(i),
                child: SvgPicture.asset(
                    "assets/images/smileys/smiley_pain_$i.svg"),
              ),
            ),
            SizedBox(
              height: 10.0,
            ),
            AutoSizeText(
              _painStrList[i], //"$i",
              maxLines: 1,
              group: _autoSizeGroup,
              style: TextStyle(
                  color: AppTheme.painColorList[i],
                  fontSize: FontSizeController.of(context).fontSize,
                  fontWeight: FontWeight.bold),
            )
          ],
        ));
  }

  void _updateValue(int i) {
    setState(() {
      _value = i;
    });
    widget.onSelection(widget.sessionInputData.fieldDescription.idBD,
        widget.sessionInputData.fieldDescription.libelle, "${i + 1}");
  }

  Future<void> _checkValue(int i) async {
    // check la valeur saisie et lève une alerte si besoin
    //bool repAlarm = CheckValue.leverAlarme(i, widget.normalite, widget.fieldDescription);
    //bool repAlarm = CheckValue.leverAlarme(i, widget.normalite, widget.fieldDescription);
    bool repAlarm = widget.sessionInputData.check(i);

    if (repAlarm) {
      bool? isValueValidated = await AlertBox(
              context: context,
              title: widget.sessionInputData.fieldDescription.libelle,
              description:
                  "$i\n\n" + allTranslations.text('field_value_alert_msg'),
              confirmBtnLabel: allTranslations.text('continue'))
          .showConfirmation();
      if (isValueValidated != null && isValueValidated) {
        _updateValue(i);
      }
    } else {
      _updateValue(i);
    }
  }
}
