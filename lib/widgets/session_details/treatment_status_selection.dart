import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/consumable.dart';
import 'package:theradom/models/enum_status.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/icons/status_indicator.dart';

/// Widget sélection du Status du traitement / produit en séance

class TreatmentStatusSelection extends StatefulWidget {
  final Status initialStatus;
  final dynamic treatment; // peu aussi être un produit

  TreatmentStatusSelection(
      {required this.initialStatus, required this.treatment});

  _TreatmentStatusSelectionState createState() =>
      _TreatmentStatusSelectionState();
}

class _TreatmentStatusSelectionState extends State<TreatmentStatusSelection> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: Container(
      child: Stack(
        children: [
          Container(
            height: MediaQuery.of(context).size.height,
            padding: EdgeInsets.only(left: 10.0, right: 10.0, top: 50.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(
                  allTranslations
                      .text(widget.treatment is Consumable
                          ? "session_consumables"
                          : "session_treatments")
                      .toUpperCase(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: 25.0,
                ),
                AutoSizeText(
                  widget.treatment is Consumable
                      ? widget.treatment.nomTrait
                      : widget.treatment.posologie + widget.treatment.posologie,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      color: AppTheme.primaryColor),
                ),
                SizedBox(height: 10.0),
                widget.treatment is Consumable
                    ? Container()
                    : Text(
                        MonitoringUtils.getNumTypeLabel(
                            widget.treatment.numType),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            decoration: TextDecoration.underline,
                            fontSize: FontSizeController.of(context).fontSize,
                            color: AppTheme.primaryColor),
                      ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 50.0),
                  decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                            color: AppTheme.lightGrey.withOpacity(0.2),
                            blurRadius: 20.0,
                            spreadRadius: 30.0,
                            offset: Offset(0.0, 1.0))
                      ],
                      border: Border(
                          top: BorderSide(
                              width: 0.5,
                              color:
                                  AppTheme.primaryBlurColor.withOpacity(0.3)))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatusTile(Status.not_specified),
                      _buildStatusTile(Status.taken),
                      _buildStatusTile(Status.not_taken)
                    ],
                  ),
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.topLeft,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                margin: EdgeInsets.only(top: 15.0, left: 15.0),
                height: 30.0,
                width: 30.0,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppTheme.primaryColor,
                    boxShadow: [
                      BoxShadow(
                          color: AppTheme.primaryBlurColor.withOpacity(0.5),
                          blurRadius: 10.0,
                          spreadRadius: 0.0)
                    ]),
                child: SvgPicture.asset("assets/images/icons/icClear.svg"),
              ),
            ),
          ),
        ],
      ),
    ));
  }

  Widget _buildStatusTile(Status status) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(status),
      child: Container(
        padding:
            EdgeInsets.only(top: 10.0, bottom: 10.0, left: 5.0, right: 5.0),
        width: 100,
        height: 140,
        decoration: BoxDecoration(
            color: status == widget.initialStatus
                ? AppTheme.borderColor.withOpacity(0.05)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(
                color: widget.initialStatus == status
                    ? AppTheme.primaryColor
                    : Colors.transparent,
                width: 2)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            StatusIndicator(
              status: status,
            ),
            Text(
              _getStatusToString(status),
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor),
            )
          ],
        ),
      ),
    );
  }

  String _getStatusToString(Status status) {
    String statusString;
    switch (status) {
      case Status.not_specified:
        {
          statusString = allTranslations.text('not_specified')
            ..replaceAll(" ", "\n");
        }
        break;
      case Status.taken:
        {
          statusString = allTranslations
              .text(widget.treatment is Consumable ? 'used' : 'taken');
        }
        break;
      case Status.not_taken:
        {
          statusString = allTranslations
              .text(widget.treatment is Consumable ? 'unused' : 'not_taken');
        }
        break;
    }
    return statusString;
  }
}
