import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/FiledCalculation.dart';
import 'package:theradom/models/area_structure.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsFieldBuilder.dart';
import 'package:theradom/widgets/session_details/SessionSection.dart';
import 'package:theradom/widgets/session_details/session_calculation_end_weight.dart';
import 'package:theradom/widgets/session_details/session_calculation_weight_to_lose.dart';

class SessionMonitoringDetailsSectionBuilder extends StatefulWidget {
  final String sectionTitle;
  final bool calculatePoidsAperdreEtPoidsPerdu;
  final List<AreaStructure> areaStructureList;
  final List<dynamic> valueList;
  final bool locked;
  final bool normalite;
  final void Function(String, String, dynamic) onValueSelected;

  SessionMonitoringDetailsSectionBuilder(
      {required this.sectionTitle,
      required this.calculatePoidsAperdreEtPoidsPerdu,
      required this.areaStructureList,
      required this.valueList,
      required this.locked,
      required this.normalite,
      required this.onValueSelected});

  _SessionMonitoringDetailsSectionBuilderState createState() =>
      _SessionMonitoringDetailsSectionBuilderState();
}

class _SessionMonitoringDetailsSectionBuilderState
    extends State<SessionMonitoringDetailsSectionBuilder> {
  List<Widget> _sessionFieldList = [];

  // champs utiles à un calcul
  int? _poidsSecIndex;
  int? _poidsDebutIndex;
  int? _apportsRealisesIndex;
  int? _poidsApresIndex;

  @override
  void initState() {
    super.initState();

    widget.areaStructureList.asMap().forEach((index, areaStructure) {
      if (widget.calculatePoidsAperdreEtPoidsPerdu) {
        switch (areaStructure.fieldID.idBD) {
          // switch qui sépare les champs "classiques" des champs qui vont permettre des calculs, uniquement s'ils ne sont pas Z_0 (Informations)
          case Config.idPoidsSec:
            {
              // poids sec
              _poidsSecIndex = index;
            }
            break;
          case Config.idPoidsDebut:
            {
              // poids de début
              _poidsDebutIndex = index;
            }
            break;
          case Config.idApportsRealises:
            {
              // apports réalisés
              _apportsRealisesIndex = index;
            }
            break;
          case Config.idPoidsApres:
            {
              // poids après
              _poidsApresIndex = index;
            }
            break;
          default:
            {
              _sessionFieldList.add(SessionMonitoringDetailsFieldBuilder(
                  valeur: widget.valueList[index],
                  normalite: widget.normalite,
                  locked: widget.locked,
                  fieldDescription: widget.areaStructureList[index].fieldID,
                  onValueSelected: widget.onValueSelected));
            }
            break;
        }
      } else {
        _sessionFieldList.add(SessionMonitoringDetailsFieldBuilder(
            valeur: widget.valueList[index],
            normalite: widget.normalite,
            locked: widget.locked,
            fieldDescription: widget.areaStructureList[index].fieldID,
            onValueSelected: widget.onValueSelected));
      }
    });

    FieldCalculation fieldCalculation = FieldCalculation();
    if ((_poidsSecIndex != null) &&
        (_apportsRealisesIndex != null) &&
        (_poidsDebutIndex != null)) {
      // poids à perdre
      fieldCalculation.setPoidsDebut = double.parse(
          widget.valueList[_poidsDebutIndex!].replaceAll(",", "."));
      fieldCalculation.setApportsRealises = double.parse(
          widget.valueList[_apportsRealisesIndex!].replaceAll(",", "."));
      fieldCalculation.setPoidsSec =
          double.parse(widget.valueList[_poidsSecIndex!].replaceAll(",", "."));
      // ajout des champs dans la section et création du champ de résultat du calcul pour le poids à perdre
      _sessionFieldList.add(SessionCalculationWeighToLose(
          fieldCalculation: fieldCalculation,
          poidsDebutAS: widget.areaStructureList[_poidsDebutIndex!],
          apportsRealisesAS: _apportsRealisesIndex != null
              ? widget.areaStructureList[_apportsRealisesIndex!]
              : null,
          normalite: widget.normalite,
          locked: widget.locked,
          onValueSelected: widget.onValueSelected));
    }
    if ((_poidsApresIndex != null) &&
        (_apportsRealisesIndex != null) &&
        (_poidsDebutIndex != null)) {
      fieldCalculation.setPoidsDebut = double.parse(
          widget.valueList[_poidsDebutIndex!].toString().replaceAll(",", "."));
      fieldCalculation.setPoidsApres = double.parse(
          widget.valueList[_poidsApresIndex!].toString().replaceAll(",", "."));
      fieldCalculation.setApportsRealises = _apportsRealisesIndex != null
          ? double.parse(
              widget.valueList[_apportsRealisesIndex!].replaceAll(",", "."))
          : 0;
      _sessionFieldList.add(SessionCalculationEndWeight(
          fieldCalculation: fieldCalculation,
          poidsApresAS: widget.areaStructureList[_poidsApresIndex!],
          normalite: widget.normalite,
          locked: widget.locked,
          onValueSelected: widget.onValueSelected));
    }
  }

  @override
  Widget build(BuildContext context) {
    return SessionSection(
        title: widget.sectionTitle,
        childrenList: _sessionFieldList,
        locked: widget.locked);
  }
}
