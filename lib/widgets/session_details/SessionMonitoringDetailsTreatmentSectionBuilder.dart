import 'package:flutter/material.dart';
import 'package:theradom/models/drug.dart';
import 'package:theradom/models/drug_details.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/error_text.dart';
import 'package:theradom/widgets/session_details/SessionSection.dart';
import 'package:theradom/widgets/session_details/treatment_container.dart';

class SessionMonitoringDetailsTreatmentSectionBuilder extends StatelessWidget {
  final Map<String, Drug>? traitements;
  final DateTime dateDebutSeance;
  final TimeOfDay heureDebutSeance;
  final bool locked;
  final void Function(DrugDetails, bool) onTreatmentUpdate;

  SessionMonitoringDetailsTreatmentSectionBuilder(
      {required this.traitements,
      required this.dateDebutSeance,
      required this.heureDebutSeance,
      required this.locked,
      required this.onTreatmentUpdate});

  @override
  Widget build(BuildContext context) {
    List<Widget> traitementFieldList = [];

    if (traitements != null) {
      traitements!.forEach((k, traitement) {
        traitementFieldList.add(TreatmentContainer(
            traitement: traitement,
            onDrugUpdate: onTreatmentUpdate,
            locked: locked,
            dateDebutSeance: dateDebutSeance,
            heureDebutSeance: heureDebutSeance));
      });
    } else {
      traitementFieldList.add(ErrorText(
        message: allTranslations.text('error_loading_treatments'),
      ));
    }

    Widget treatmentSection;
    if (traitementFieldList.length > 0) {
      treatmentSection = SessionSection(
          title: allTranslations.text('session_treatments'),
          childrenList: traitementFieldList,
          locked: locked);
    } else {
      treatmentSection = Container();
    }

    return treatmentSection;
  }
}
