import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/FieldDescription.dart';
import 'package:theradom/models/GetDrugsResponse.dart';
import 'package:theradom/models/PutDataDescription.dart';
import 'package:theradom/models/SessionDetailsResponse.dart';
import 'package:theradom/models/area_structure.dart';
import 'package:theradom/models/consumable_details.dart';
import 'package:theradom/models/drug_details.dart';
import 'package:theradom/models/seance_precedente.dart';
import 'package:theradom/models/session_sampling_data_to_save.dart';
import 'package:theradom/utils/session_details/monitoring_utils.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsCommentSectionBuilder.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsPainSectionBuilder.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsSamplingSectionBuilder.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsSectionBuilder.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsStartTimeSection.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsTreatmentSectionBuilder.dart';
import 'package:theradom/widgets/session_details/SessionMonotoringDetailsConsumableSectionBuilder.dart';
import 'package:theradom/extensions/string_extension.dart';

class SessionMonitorinfDetailsSectionListBuilder extends StatefulWidget {
  final SeancePrecedente seance;
  final String suivi;
  final GetDrugsResponse? getDrugsResponse;
  final void Function(DrugDetails, bool) onTreatmentUpdate;
  final void Function(ConsumableDetails, bool) onConsumableUpdate;
  final SessionDetailsResponse? sessionDetailsResponse;
  final SessionSamplingDataToSave? initialSamplingManualData;
  final SessionSamplingDataToSave? initialSamplingBoxData;
  final void Function(SessionSamplingDataToSave) onSessionSamplingDataUpdate;
  final TextField commentTextfield;
  final void Function(List<String>) onDbFilesFetched;
  final bool normalite;
  final void Function(String, String, dynamic) onValueSelected;
  final void Function(TimeOfDay) onTimeSelected;
  final void Function(PutDataDescription) addToPutDataDesctiptionList;

  SessionMonitorinfDetailsSectionListBuilder(
      {required this.seance,
      required this.suivi,
      required this.getDrugsResponse,
      required this.onTreatmentUpdate,
      required this.onConsumableUpdate,
      required this.sessionDetailsResponse,
      required this.initialSamplingManualData,
      required this.initialSamplingBoxData,
      required this.onSessionSamplingDataUpdate,
      required this.commentTextfield,
      required this.onDbFilesFetched,
      required this.normalite,
      required this.onValueSelected,
      required this.onTimeSelected,
      required this.addToPutDataDesctiptionList});

  _SessionMonitorinfDetailsSectionListBuilderState createState() =>
      _SessionMonitorinfDetailsSectionListBuilderState();
}

class _SessionMonitorinfDetailsSectionListBuilderState
    extends State<SessionMonitorinfDetailsSectionListBuilder> {
  late final bool _calculatePoidsAperdreEtPoidsPerdu;
  late final AreaStructure? _poidsSecAS;
  late final AreaStructure? _poidsDebutAS;
  late final AreaStructure? _poidsApresAS;
  late final AreaStructure? _apportsRealisesAS;
  String _poidsSecValue = "";
  String _poidsDebutValue = "";
  String _poidsApresValue = "";
  String _apportsRealisesValue = "";

  late final TimeOfDay? _heureDebutSeance;
  late final bool _locked;
  late final FieldDescription? _painFieldDescription;
  late final String _painValue;

  List<AreaStructure> _listAreaStructure = [];

  // informations
  List<AreaStructure> _z0AreaStructureList = [];
  List<dynamic> _z0ValueList = [];

  // début de séance
  List<AreaStructure> _z1AreaStructureList = [];
  List<dynamic> _z1ValueList = [];

  // pendant la séance
  List<AreaStructure> _z2AreaStructureList = [];
  List<dynamic> _z2ValueList = [];

  // fin de séance
  List<AreaStructure> _z4AreaStructureList = [];
  List<dynamic> _z4ValueList = [];

  // consultation
  List<AreaStructure> _z5AreaStructureList = [];
  List<dynamic> _z5ValueList = [];

  // pas de zone définie
  List<AreaStructure> _zAreaStructureList = [];
  List<dynamic> _zValueList = [];

  // zones dynamiques
  List<String> _zDynNameList = [];
  List<List<AreaStructure>> _zDynListAreaStructureList = [];
  List<List<dynamic>> _zDynListValueList = [];

  List<SessionMonitoringDetailsSamplingSectionBuilder> _samplingSectionList =
      [];

  @override
  void initState() {
    super.initState();
    _heureDebutSeance =
        widget.sessionDetailsResponse!.seance.values.unique.heureDeb != null
            ? widget.sessionDetailsResponse!.seance.values.unique.heureDeb!
                .hhmm2TimeOfDay()
            : null;
    _locked = widget.seance.verrou;

    // douleur
    print("test douleur (la vraie) " +
        widget.sessionDetailsResponse?.seance.values.unique.fieldID["136_6"]);
    String painIdBD = Config.idDouleur;
    _painFieldDescription = widget.sessionDetailsResponse?.seance.structure
                .unique.internal[painIdBD] ==
            null
        ? null
        : FieldDescription.fromJson(widget.sessionDetailsResponse!.seance
            .structure.unique.internal[painIdBD]);
    // _painValue = _painFieldDescription?.valeur == null
    //     ? "Aucune donnée de douleur disponible"
    //     : widget.sessionDetailsResponse!.seance.values.unique
    //             .fieldID[Config.idDouleur] ??
    //         "Aucune donnée de douleur disponible";
    _painValue =
        widget.sessionDetailsResponse?.seance.values.unique.fieldID["136_6"] ??
            null;

    // récupération des structures
    String? name;
    widget.sessionDetailsResponse!.seance.structure.unique.areaSructure
        .forEach((key, value) {
      // dans l'ordre d'affichage
      name = value["name"];
      Map<String, dynamic> val = value;
      val.remove("name");
      val.forEach((id, value2) {
        if (name != null) {
          FieldDescription fieldDescription = FieldDescription.fromJson(value2);
          fieldDescription.zone = name!;
          fieldDescription.idBD = id;
          _listAreaStructure
              .add(AreaStructure(name: name!, fieldID: fieldDescription));
        }
      });
    });

    int poidsSecIndex = _listAreaStructure.indexWhere(
        (areaStructure) => areaStructure.fieldID.idBD == Config.idPoidsSec);
    _poidsSecAS =
        poidsSecIndex != -1 ? _listAreaStructure[poidsSecIndex] : null;

    int poidsDebutIndex = _listAreaStructure.indexWhere(
        (areaStructure) => areaStructure.fieldID.idBD == Config.idPoidsDebut);
    _poidsDebutAS =
        poidsDebutIndex != -1 ? _listAreaStructure[poidsDebutIndex] : null;

    int poidsApresIndex = _listAreaStructure.indexWhere(
        (areaStructure) => areaStructure.fieldID.idBD == Config.idPoidsApres);
    _poidsApresAS =
        poidsApresIndex != -1 ? _listAreaStructure[poidsApresIndex] : null;

    int apportsRealisesIndex = _listAreaStructure.indexWhere((areaStructure) =>
        areaStructure.fieldID.idBD == Config.idApportsRealises);
    _apportsRealisesAS = apportsRealisesIndex != -1
        ? _listAreaStructure[apportsRealisesIndex]
        : null;

    _calculatePoidsAperdreEtPoidsPerdu = ((_poidsSecAS != null) &&
            (_poidsDebutAS != null) &&
            (_poidsApresAS != null) &&
            (_apportsRealisesAS != null))
        ? true
        : false;
  }

  @override
  Widget build(BuildContext context) {
    _samplingSectionList.clear();

    if (_calculatePoidsAperdreEtPoidsPerdu) {
      // si on veut faire des calculs je retire les champs de la liste
      _poidsSecValue = widget.sessionDetailsResponse!.seance.values.unique
          .fieldID[_poidsSecAS!.fieldID.idBD]
          .toString();
      if (_poidsSecAS!.fieldID.zone != "Z_0") {
        _listAreaStructure.removeWhere((areaStructure) =>
            areaStructure.fieldID.idBD == _poidsSecAS!.fieldID.idBD);
      }

      _poidsDebutValue = widget.sessionDetailsResponse!.seance.values.unique
          .fieldID[_poidsDebutAS!.fieldID.idBD]
          .toString();
      _listAreaStructure.removeWhere((areaStructure) =>
          areaStructure.fieldID.idBD == _poidsDebutAS!.fieldID.idBD);

      _poidsApresValue = widget.sessionDetailsResponse!.seance.values.unique
          .fieldID[_poidsApresAS!.fieldID.idBD]
          .toString();
      _listAreaStructure.removeWhere((areaStructure) =>
          areaStructure.fieldID.idBD == _poidsApresAS!.fieldID.idBD);

      _apportsRealisesValue = widget.sessionDetailsResponse!.seance.values
          .unique.fieldID[_apportsRealisesAS!.fieldID.idBD]
          .toString();
      _listAreaStructure.removeWhere((areaStructure) =>
          areaStructure.fieldID.idBD == _apportsRealisesAS!.fieldID.idBD);
    }

    _listAreaStructure.forEach((areaStructure) {
      // pour chaque areaStructure

      String idBD = areaStructure.fieldID.idBD;
      String valeurBase = widget
          .sessionDetailsResponse!.seance.values.unique.fieldID[idBD]
          .toString();

      switch (areaStructure.fieldID.zone) {
        case "Z_0":
          {
            // workaround pour Z_0, valeur peut contenir la valeur à afficher qui n'est pas dans "values"...
            valeurBase = valeurBase == "null"
                ? (areaStructure.fieldID.valeur is String
                    ? areaStructure.fieldID.valeur
                    : "")
                : valeurBase;
            _z0ValueList.add(valeurBase);
            _z0AreaStructureList.add(areaStructure);
          }
          break;
        case "Z_1":
          {
            _z1ValueList.add(valeurBase);
            _z1AreaStructureList.add(areaStructure);
          }
          break;
        case "Z_2":
          {
            _z2ValueList.add(valeurBase);
            _z2AreaStructureList.add(areaStructure);
          }
          break;
        case "Z_4":
          {
            _z4ValueList.add(valeurBase);
            _z4AreaStructureList.add(areaStructure);
          }
          break;
        case "Z_5":
          {
            _z5ValueList.add(valeurBase);
            _z5AreaStructureList.add(areaStructure);
          }
          break;
        default:
          {
            // pour une zone custom
            if ((areaStructure.fieldID.libelle == "douleur") &&
                (areaStructure.fieldID.idBD == Config.idDouleur)) {
              // pour éviter un doublon de la douleur
              break;
            }

            int i = _zDynNameList.indexOf(areaStructure.name);
            if (i == -1) {
              // zone définie par l'utilisateur HMD
              _zDynNameList.add(areaStructure.name);
              _zDynListAreaStructureList
                  .add([]); // création d'une liste d'éléments pour cette zone
              _zDynListAreaStructureList[_zDynNameList.length - 1].add(
                  areaStructure); // ajout du widget dans cette nouvelle zone
              _zDynListValueList.add([]);
              _zDynListValueList[_zDynListValueList.length - 1].add(valeurBase);
            } else {
              // zone trouvée dans customZoneNameList
              _zDynListAreaStructureList[i].add(
                  areaStructure); // ajout du widget dans cette nouvelle zone
              _zDynListValueList[i].add(valeurBase);
            }
          }
          break;
      }

      // ajout dans l'objet de sortie
      if (areaStructure.fieldID.zone != "Z_0") {
        widget.addToPutDataDesctiptionList(PutDataDescription(
          idBD: idBD,
          libelle: areaStructure.fieldID.libelle,
          value: areaStructure.fieldID.format != "booleen"
              ? valeurBase
              : ((valeurBase == "1") ? true : false),
        ));
      }
    });

    if (_calculatePoidsAperdreEtPoidsPerdu) {
      // j'ajoute les champs dans les zone adéquates
      _z1AreaStructureList.add(_poidsSecAS!);
      _z1ValueList.add(_poidsSecValue);

      _z1AreaStructureList.add(_poidsDebutAS!);
      _z1ValueList.add(_poidsDebutValue);
      widget.addToPutDataDesctiptionList(PutDataDescription(
          idBD: _poidsDebutAS!.fieldID.idBD,
          libelle: _poidsDebutAS!.fieldID.libelle,
          value: widget.sessionDetailsResponse!.seance.values.unique
              .fieldID[_poidsDebutAS!.fieldID.idBD]));

      _z1AreaStructureList.add(_apportsRealisesAS!);
      _z1ValueList.add(_apportsRealisesValue);
      widget.addToPutDataDesctiptionList(PutDataDescription(
          idBD: _apportsRealisesAS!.fieldID.idBD,
          libelle: _apportsRealisesAS!.fieldID.libelle,
          value: widget.sessionDetailsResponse!.seance.values.unique
              .fieldID[_apportsRealisesAS!.fieldID.idBD]));

      _z4AreaStructureList.add(_poidsApresAS!);
      _z4ValueList.add(_poidsApresValue);
      widget.addToPutDataDesctiptionList(PutDataDescription(
          idBD: _poidsApresAS!.fieldID.idBD,
          libelle: _poidsApresAS!.fieldID.libelle,
          value: widget.sessionDetailsResponse!.seance.values.unique
              .fieldID[_poidsApresAS!.fieldID.idBD]));

      _z4AreaStructureList.add(_apportsRealisesAS!);
      _z4ValueList.add(_apportsRealisesValue);
      _z4AreaStructureList.add(_poidsDebutAS!);
      _z4ValueList.add(_poidsDebutValue);
    }

    // section des informations
    Widget z0Section;
    if (_z0AreaStructureList.length > 0) {
      z0Section = SessionMonitoringDetailsSectionBuilder(
          sectionTitle: allTranslations.text('txt_informations'),
          calculatePoidsAperdreEtPoidsPerdu: false,
          areaStructureList: _z0AreaStructureList,
          valueList: _z0ValueList,
          locked: true,
          normalite: widget.normalite,
          onValueSelected: widget.onValueSelected);
    } else {
      z0Section = Container();
    }

    //  section début de séance
    Widget z1Section;
    if (_z1AreaStructureList.length > 0) {
      z1Section = SessionMonitoringDetailsSectionBuilder(
          sectionTitle: allTranslations.text('session_start'),
          calculatePoidsAperdreEtPoidsPerdu: _calculatePoidsAperdreEtPoidsPerdu,
          areaStructureList: _z1AreaStructureList,
          valueList: _z1ValueList,
          locked: _locked,
          normalite: widget.normalite,
          onValueSelected: widget.onValueSelected);
    } else {
      z1Section = Container();
    }

    // section pendant séance
    Widget z2Section;
    if (_z2AreaStructureList.length > 0) {
      z2Section = SessionMonitoringDetailsSectionBuilder(
          sectionTitle: allTranslations.text('during_session'),
          calculatePoidsAperdreEtPoidsPerdu: false,
          areaStructureList: _z2AreaStructureList,
          valueList: _z2ValueList,
          locked: _locked,
          normalite: widget.normalite,
          onValueSelected: widget.onValueSelected);
    } else {
      z2Section = Container();
    }

    // section fin de séance
    Widget z4Section;
    if (_z4AreaStructureList.length > 0) {
      z4Section = SessionMonitoringDetailsSectionBuilder(
          sectionTitle: allTranslations.text('end_of_session'),
          calculatePoidsAperdreEtPoidsPerdu: _calculatePoidsAperdreEtPoidsPerdu,
          areaStructureList: _z4AreaStructureList,
          valueList: _z4ValueList,
          locked: _locked,
          normalite: widget.normalite,
          onValueSelected: widget.onValueSelected);
    } else {
      z4Section = Container();
    }

    // sesction suivi
    Widget z5Section;
    if (_z5AreaStructureList.length > 0) {
      z5Section = SessionMonitoringDetailsSectionBuilder(
          sectionTitle: allTranslations.text('follow_up'),
          calculatePoidsAperdreEtPoidsPerdu: false,
          areaStructureList: _z5AreaStructureList,
          valueList: _z5ValueList,
          locked: _locked,
          normalite: widget.normalite,
          onValueSelected: widget.onValueSelected);
    } else {
      z5Section = Container();
    }

    // construction du widget si aucune zone n'est définie
    if ((_z0AreaStructureList.length +
            _z1AreaStructureList.length +
            _z2AreaStructureList.length +
            _z4AreaStructureList.length +
            _z5AreaStructureList.length) ==
        0) {
      _listAreaStructure.forEach((areaStructure) {
        String idBD = areaStructure.fieldID.idBD;
        if (idBD != Config.idDouleur) {
          String valeurBase = widget
              .sessionDetailsResponse!.seance.values.unique.fieldID[idBD]
              .toString();
          _zAreaStructureList.add(areaStructure);
          _zValueList.add(valeurBase);
        }
      });
    }

    Widget zSection;
    if (_zAreaStructureList.length > 0) {
      zSection = SessionMonitoringDetailsSectionBuilder(
          sectionTitle: allTranslations.text('values_to_enter'),
          calculatePoidsAperdreEtPoidsPerdu: false,
          areaStructureList: _zAreaStructureList,
          valueList: _zValueList,
          locked: _locked,
          normalite: widget.normalite,
          onValueSelected: widget.onValueSelected);
    } else {
      zSection = Container();
    }

    List<SessionMonitoringDetailsSectionBuilder> _dynamiqueSectionList = [];
    if (_zDynListAreaStructureList.length > 0) {
      _zDynListAreaStructureList.asMap().forEach((index, areaStructure) {
        _dynamiqueSectionList.add(SessionMonitoringDetailsSectionBuilder(
            sectionTitle: _zDynNameList[index],
            calculatePoidsAperdreEtPoidsPerdu: false,
            areaStructureList: _zDynListAreaStructureList[index],
            valueList: _zDynListValueList[index],
            locked: _locked,
            normalite: widget.normalite,
            onValueSelected: widget.onValueSelected));
      });
    }

    if (widget.initialSamplingManualData != null)
      _samplingSectionList.add(SessionMonitoringDetailsSamplingSectionBuilder(
          seance: widget.seance,
          isBox: false,
          initialSessionSamplingDataToSave: widget.initialSamplingManualData!,
          normalite: widget.normalite,
          sessionSamplingStructure:
              widget.sessionDetailsResponse!.seance.structure.table,
          onSamplingDataUpdate: widget.onSessionSamplingDataUpdate));

    if (widget.initialSamplingBoxData != null)
      _samplingSectionList.add(SessionMonitoringDetailsSamplingSectionBuilder(
          seance: widget.seance,
          isBox: true,
          initialSessionSamplingDataToSave: widget.initialSamplingBoxData!,
          normalite: widget.normalite,
          sessionSamplingStructure:
              widget.sessionDetailsResponse!.seance.structure.box,
          onSamplingDataUpdate: null));

    return ListBody(
        children: [
              // produits en séance
              (MonitoringUtils.isMonitoringSession(widget.suivi) &&
                      widget.getDrugsResponse!.produits.length > 0)
                  ? SessionMonitoringDetailsConsumableSectionBuilder(
                      produits: widget.getDrugsResponse != null
                          ? widget.getDrugsResponse!.produits
                          : {},
                      dateDebutSeance: widget.seance.dateSeance,
                      heureDebutSeance: _heureDebutSeance!,
                      locked: _locked,
                      onConsumableUpdate: widget.onConsumableUpdate)
                  : Container(),

              // traitements en séance
              (MonitoringUtils.isMonitoringSession(widget.suivi) &&
                      widget.getDrugsResponse!.traitements.length > 0)
                  ? SessionMonitoringDetailsTreatmentSectionBuilder(
                      traitements: widget.getDrugsResponse != null
                          ? widget.getDrugsResponse!.traitements
                          : {},
                      dateDebutSeance: widget.seance.dateSeance,
                      heureDebutSeance: _heureDebutSeance!,
                      locked: _locked,
                      onTreatmentUpdate: widget.onTreatmentUpdate)
                  : Container(),

              // heure de début de séance
              MonitoringUtils.isMonitoringSession(widget.suivi)
                  ? SessionMonitoringDetailsStartTimeSection(
                      suivi: widget.suivi,
                      heureDebutSeance: _heureDebutSeance!,
                      locked: _locked,
                      onTimeSelected: widget.onTimeSelected)
                  : Container(),

              // informations
              z0Section,

              // débt de séance
              z1Section,

              // pendant la séance
              z2Section,

              // fin de séance
              z4Section,

              // suivi
              z5Section,

              // valeurs à renseigner
              zSection
            ] +
            _dynamiqueSectionList // zones créées par l'utilisateur HMD
            +
            _samplingSectionList +
            [
              // saisie de commentaires
              SessionMonitoringDetailsCommentSectionBuilder(
                  seance: widget.seance,
                  textField: widget.commentTextfield,
                  suivi: widget.suivi,
                  onDatabaseFilesFetched: widget.onDbFilesFetched),

              // niveau de douleur
              _painFieldDescription != null
                  ? SessionMonitoringDetailsPainSectionBuilder(
                      painValue: _painValue,
                      fieldDescription: _painFieldDescription!,
                      normalite: widget.normalite,
                      locked: _locked,
                      onPainValueChanged: widget.onValueSelected)
                  : Container(),

              // espace fin formulaire
              SizedBox(
                height: 150.0,
              )
            ]);
  }
}
