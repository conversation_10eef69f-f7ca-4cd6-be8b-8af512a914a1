import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';

/// Element du formulaire

class FormFieldElement extends FormField<dynamic> {
  final Widget child;
  final dynamic value;

  FormFieldElement({
    FormFieldSetter<dynamic>? onSaved,
    required FormFieldValidator<dynamic> validator,
    required this.child,
    this.value,
  }) : super(
          onSaved: onSaved,
          validator: validator,
          initialValue: value,
          builder: (FormFieldState<dynamic> state) {
            return Container(
              padding: EdgeInsets.all(5),
              child: Column(
                children: <Widget>[
                  child,
                  SizedBox(height: state.hasError ? 5.0 : 0.0),
                  state.hasError
                      ? Divider(color: Colors.transparent)
                      : Container(),
                  Text(
                    state.hasError ? state.errorText! : "",
                    style:
                        TextStyle(color: AppTheme.tertiaryColor, fontSize: 14),
                  ),
                ],
              ),
            );
          },
        );
}
