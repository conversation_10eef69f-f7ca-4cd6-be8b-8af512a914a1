import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:theradom/blocs/qrcode/QRcodeBlocProvider.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/consumable_details.dart';
import 'package:theradom/models/drug_details.dart';
import 'package:theradom/models/enum_status.dart';
import 'package:theradom/models/gs1code128data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/regexp_utils.dart';
import 'package:theradom/utils/session_details/textfield_validators.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/qrcode/ViewQRcode.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';
import 'package:theradom/widgets/icons/status_indicator.dart';
import 'package:theradom/widgets/MyDatePicker.dart';
import 'package:theradom/widgets/buttons/large_button.dart';
import 'package:theradom/widgets/custom_scrollbar.dart';
import 'package:theradom/widgets/expand_text.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';
import 'package:theradom/extensions/timeofday_extension.dart';

/// Widget du détails d'un traitement / produit en séance et définition de heure de prise, nombre, dose etc

class DetailsTreatment extends StatefulWidget {
  final String nomTrait;
  final String posologie;
  final String id;
  final Status initialStatus;
  final Status currentStatus;
  final double number;
  final String numLot;
  final double numberPresc;
  final double? dosePresc;
  final double? dose;
  final String motif;
  final TimeOfDay? timeTaken;
  final bool anticoagulant;
  final TimeOfDay heureArret;
  final bool isLocked;
  final DateTime dateDebutSeance;
  final TimeOfDay? heureDebutSeance;
  final String importantMessage; // message à propos de la valeur de la dose

  DetailsTreatment(
      {required this.nomTrait,
      required this.posologie,
      required this.id,
      required this.initialStatus,
      required this.currentStatus,
      required this.number,
      required this.numLot,
      required this.numberPresc,
      this.dose, // dose == null => produit, sinon traitement
      this.dosePresc,
      required this.motif,
      required this.timeTaken,
      required this.anticoagulant,
      required this.heureArret,
      required this.isLocked,
      required this.dateDebutSeance,
      required this.heureDebutSeance,
      this.importantMessage = ""});

  _DetailsTreatmentState createState() => _DetailsTreatmentState();
}

class _DetailsTreatmentState extends State<DetailsTreatment> {
  final _takenFormKey = GlobalKey<FormState>();
  final _notTakenFormKey = GlobalKey<FormState>();

  late Status _status;
  late bool _taken;
  late DateTime _dateTaken;
  late TimeOfDay? _timeTaken;
  late TimeOfDay _heureArret;
  final TextEditingController _batchNumberController = TextEditingController();
  final TextEditingController _numberTfController = TextEditingController();
  final TextEditingController _motifNomAdminTfController =
      TextEditingController();
  final TextEditingController _doseTfController = TextEditingController();
  final List<String> _raisonNonAdminList =
      Config.basicUserInfos!.enumMotifNonAdmin.values.toList();
  String? _raisonNonAdmin;
  late final DateTime _minimumDateTime;
  late final DateTime _maximumDateTime;
  late final BoxDecoration _decoration;
  late final int _differenceBetweenSessionStartTimeAndNowInHours;
  String? _batchNumberError;

  @override
  void initState() {
    super.initState();
    _decoration = BoxDecoration(
        color: widget.isLocked
            ? AppTheme.primaryColorDarker
            : AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(20.0),
        boxShadow: [
          BoxShadow(
              spreadRadius: 0.0,
              blurRadius: 10.0,
              color: AppTheme.primaryBlurColor.withOpacity(0.3))
        ]);
    DateTime now = DateTime.now();
    DateTime dateDebutSeance = widget.dateDebutSeance;
    TimeOfDay heureDebutSeance = widget.heureDebutSeance != null
        ? widget.heureDebutSeance!
        : TimeOfDay.now();
    _minimumDateTime = DateTime(dateDebutSeance.year, dateDebutSeance.month,
        dateDebutSeance.day, heureDebutSeance.hour, heureDebutSeance.minute);
    _differenceBetweenSessionStartTimeAndNowInHours =
        DateTime(now.year, now.month, now.day)
            .difference(_minimumDateTime)
            .inHours;
    _dateTaken = _minimumDateTime;
    _heureArret = widget.heureArret;
    _maximumDateTime = now.isAfter(_minimumDateTime.add(Duration(days: 1)))
        ? _minimumDateTime.add(Duration(days: 1))
        : now;
    _status = widget.initialStatus;
    _taken = _status == Status.taken ? true : false;
    _timeTaken = widget.timeTaken;
    _batchNumberController.text = widget.numLot;
    _numberTfController.text = widget.number.toString();
    _doseTfController.text =
        widget.importantMessage.isNotEmpty ? "" : widget.dose.toString();
    _motifNomAdminTfController.text =
        widget.initialStatus == widget.currentStatus ? widget.motif : "";
    _timeTaken = widget.timeTaken == null ? TimeOfDay.now() : widget.timeTaken;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppTheme.backgroundColor,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
        child: Material(
          child: Container(
            child: Stack(
              children: [
                // colonne des éléments
                Padding(
                  padding: EdgeInsets.only(top: 50.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.0),
                        child: ExpandText(
                          widget.posologie,
                          maxLines: 1,
                          icon: ChevIcon(
                            assetPath: "assets/images/icons/btChevBlancDw.svg",
                          ),
                          style: TextStyle(
                              color: AppTheme.primaryColor,
                              backgroundColor: AppTheme.backgroundColor,
                              fontWeight: FontWeight.w600,
                              fontSize:
                                  FontSizeController.of(context).fontSize),
                        ),
                      ),

                      SizedBox(
                        height: 20.0,
                      ),

                      // switcher
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 20.0),
                        decoration: _decoration,
                        child: ListTile(
                          contentPadding: EdgeInsets.only(
                              top: 5.0, bottom: 5.0, left: 20.0, right: 20.0),
                          leading: StatusIndicator(status: _status),
                          title: Text(
                            widget.dose == null
                                ? allTranslations
                                    .text(_taken ? 'used' : 'unused')
                                : allTranslations
                                    .text(_taken ? 'taken' : 'not_taken'),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: TextStyle(
                              color: widget.isLocked
                                  ? AppTheme.backgroundColor
                                  : AppTheme.primaryColor,
                              fontSize: FontSizeController.of(context).fontSize,
                            ),
                          ),
                          trailing: CupertinoSwitch(
                              value: _taken,
                              onChanged: widget.isLocked ? null : _changeStatus,
                              activeColor: AppTheme.secondaryColor,
                              trackColor: AppTheme.tertiaryColor),
                        ),
                      ),

                      SizedBox(height: 10.0),

                      // colonne
                      _taken ? _buildTakenColumn() : _buildNotTakenColumn(),

                      // boutons retour et valider
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: LargeButton(
                                title: allTranslations.text('back'),
                                titleColor: AppTheme.primaryColor,
                                backgroundColor: AppTheme.backgroundColor,
                                onTap: () => Navigator.of(context).pop()),
                          ),
                          widget.isLocked
                              ? Container()
                              : Expanded(
                                  child: LargeButton(
                                      title:
                                          allTranslations.text('to_validate'),
                                      titleColor: AppTheme.primaryColor,
                                      backgroundColor: _canValidate()
                                          ? AppTheme.secondaryColor
                                          : AppTheme.greyColor,
                                      onTap: _validateDetails),
                                )
                        ],
                      ),

                      SizedBox(
                        height: 20.0,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTakenColumn() {
    return Form(
        key: _takenFormKey,
        child: Expanded(
          child: SingleChildScrollView(
              child: Column(
            children: [
              SizedBox(
                height: 10.0,
              ),

              // nombre et dose
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // nombre
                  Expanded(
                    child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 20.0),
                        decoration: _decoration,
                        padding: EdgeInsets.only(bottom: 10.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(left: 20.0, top: 10.0),
                              child: Text(
                                allTranslations.text('quantity'),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    fontSize:
                                        FontSizeController.of(context).fontSize,
                                    color: widget.isLocked
                                        ? AppTheme.backgroundColor
                                        : AppTheme.primaryColor,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            TextFormField(
                              enabled: !widget.isLocked,
                              cursorColor: AppTheme.primaryColor,
                              controller: _numberTfController,
                              validator:
                                  TextFieldValidators.instance.forDecimalNumber,
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                    RegexpUtils.decimalNotSigned),
                                LengthLimitingTextInputFormatter(8)
                              ],
                              onChanged: (String value) {
                                // permet de mettre à jour le widget
                                setState(() {});
                              },
                              keyboardType: TextInputType.numberWithOptions(
                                  signed: false, decimal: true),
                              maxLines: 1,
                              decoration: InputDecoration(
                                  hintText: "${widget.numberPresc}"
                                      .replaceAll(".", ","),
                                  hintMaxLines: 1,
                                  hintStyle: TextStyle(
                                      color: AppTheme.primaryColor
                                          .withOpacity(0.3),
                                      fontSize: FontSizeController.of(context)
                                          .fontSize),
                                  contentPadding: EdgeInsets.only(left: 20.0),
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(20.0),
                                      borderSide: BorderSide.none),
                                  errorStyle: TextStyle(
                                    color: AppTheme.tertiaryColor,
                                    fontSize: 14,
                                  )),
                              style: TextStyle(
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                  color: widget.isLocked
                                      ? AppTheme.backgroundColor
                                      : AppTheme.primaryColor),
                            )
                          ],
                        )),
                  ),

                  widget.dose != null
                      ? SizedBox(
                          width: 20.0,
                        )
                      : Container(),

                  // dose
                  widget.dose != null
                      ? Expanded(
                          child: Container(
                              margin: EdgeInsets.symmetric(horizontal: 20.0),
                              padding: EdgeInsets.only(bottom: 10.0),
                              decoration: _decoration,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding:
                                        EdgeInsets.only(left: 20.0, top: 10.0),
                                    child: Text(
                                      allTranslations.text('dose'),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          fontSize:
                                              FontSizeController.of(context)
                                                  .fontSize,
                                          color: widget.isLocked
                                              ? AppTheme.backgroundColor
                                              : AppTheme.primaryColor,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                  TextFormField(
                                    enabled: !widget.isLocked,
                                    cursorColor: AppTheme.primaryColor,
                                    controller: _doseTfController,
                                    validator: TextFieldValidators
                                        .instance.forDecimalNumber,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegexpUtils.decimalNotSigned),
                                      LengthLimitingTextInputFormatter(8)
                                    ],
                                    onChanged: (String value) {
                                      // permet de mettre à jour le widget
                                      setState(() {});
                                    },
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            signed: false, decimal: true),
                                    maxLines: 1,
                                    decoration: InputDecoration(
                                        hintText: "${widget.dosePresc}"
                                            .replaceAll(",", "."),
                                        hintMaxLines: 1,
                                        hintStyle: TextStyle(
                                            color: AppTheme.primaryColor
                                                .withOpacity(0.3),
                                            fontSize:
                                                FontSizeController.of(context)
                                                    .fontSize),
                                        contentPadding:
                                            EdgeInsets.only(left: 20.0),
                                        border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(20.0),
                                            borderSide: BorderSide.none),
                                        errorStyle: TextStyle(
                                          color: AppTheme.tertiaryColor,
                                          fontSize: 14,
                                        )),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize,
                                        color: widget.isLocked
                                            ? AppTheme.backgroundColor
                                            : AppTheme.primaryColor),
                                  )
                                ],
                              )),
                        )
                      : Container(),
                ],
              ),

              SizedBox(height: 20.0),

              widget.importantMessage.isNotEmpty
                  ? AutoSizeText(
                      widget.importantMessage,
                      maxLines: 1,
                      style: TextStyle(
                        color: AppTheme.tertiaryColor,
                        fontSize: 16,
                      ),
                    )
                  : Container(),

              widget.importantMessage.isNotEmpty
                  ? SizedBox(
                      height: 20.0,
                    )
                  : Container(),

              // numéro de lot
              Container(
                  margin: EdgeInsets.symmetric(horizontal: 20.0),
                  decoration: _decoration,
                  padding: EdgeInsets.only(bottom: 10.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 20.0, top: 10.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              allTranslations.text('batch_number'),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                  color: widget.isLocked
                                      ? AppTheme.backgroundColor
                                      : AppTheme.primaryColor,
                                  fontWeight: FontWeight.w600),
                            ),
                            widget.isLocked
                                ? Container()
                                : Padding(
                                    padding: EdgeInsets.only(right: 20.0),
                                    child: SvgButton(
                                        asset:
                                            "assets/images/icons/icQRcode.svg",
                                        onTap: _pushToQRcodeView),
                                  )
                          ],
                        ),
                      ),
                      TextFormField(
                        enabled: !widget.isLocked,
                        cursorColor: AppTheme.primaryColor,
                        controller: _batchNumberController,
                        onChanged: (String value) {
                          // permet de mettre à jour le widget
                          setState(() {});
                        },
                        keyboardType: TextInputType.text,
                        maxLines: 1,
                        decoration: InputDecoration(
                            contentPadding: EdgeInsets.only(left: 20.0),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20.0),
                                borderSide: BorderSide.none),
                            errorText: _batchNumberError,
                            errorStyle: TextStyle(
                                fontSize: 14, color: AppTheme.tertiaryColor)),
                        style: TextStyle(
                            fontSize: FontSizeController.of(context).fontSize,
                            color: widget.isLocked
                                ? AppTheme.backgroundColor
                                : AppTheme.primaryColor),
                      )
                    ],
                  )),

              SizedBox(height: 20.0),

              // heure d'aaret de la dose d'entretien
              (widget.anticoagulant)
                  ? Container(
                      margin: EdgeInsets.symmetric(horizontal: 20.0),
                      padding: EdgeInsets.only(bottom: 10.0),
                      decoration: _decoration,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 20.0, top: 10.0),
                            child: AutoSizeText(
                              allTranslations
                                  .text('maintenance_dose_stop_time'),
                              maxLines: 1,
                              maxFontSize:
                                  FontSizeController.of(context).fontSize,
                              style: TextStyle(
                                  fontSize:
                                      FontSizeController.of(context).fontSize,
                                  color: widget.isLocked
                                      ? AppTheme.backgroundColor
                                      : AppTheme.primaryColor,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                          GestureDetector(
                            onTap: () async {
                              if (!widget.isLocked) {
                                TimeOfDay? timePicked = await MyDatePicker(
                                        context: context,
                                        timeHint: allTranslations.text(
                                            'select_maintenance_dose_stop_time'),
                                        initialTime: _heureArret)
                                    .pickTime();
                                if (timePicked != null) {
                                  setState(() {
                                    _heureArret = timePicked;
                                  });
                                }
                              }
                            },
                            child: Center(
                              child: Text(
                                _heureArret.toLanguageString(),
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: widget.isLocked
                                        ? AppTheme.backgroundColor
                                        : _heureArret.isMidnight()
                                            ? AppTheme.tertiaryColor
                                            : AppTheme.primaryColor,
                                    fontSize: 30),
                              ),
                            ),
                          )
                        ],
                      ))
                  : Container(),

              (widget.anticoagulant)
                  ? SizedBox(height: 20.0)
                  : SizedBox(height: 0.0),

              // commentaire
              (widget.dose == null)
                  // cas d'un produit
                  ? (widget.numberPresc !=
                              double.parse(_numberTfController.text == ""
                                  ? "-1"
                                  : _numberTfController.text
                                      .replaceAll(",", "."))) ||
                          _motifNomAdminTfController.text.isNotEmpty
                      ? _buildCommentWidget()
                      : Container()
                  // cas d'un traitement
                  : ((widget.numberPresc !=
                                  double.parse(_numberTfController.text == ""
                                      ? "-1"
                                      : _numberTfController.text
                                          .replaceAll(",", "."))) ||
                              (widget.dosePresc !=
                                  double.parse(_doseTfController.text == ""
                                      ? "-1"
                                      : _doseTfController.text
                                          .replaceAll(",", ".")))) ||
                          _motifNomAdminTfController.text.isNotEmpty
                      ? widget.importantMessage.isNotEmpty
                          ? (widget.numberPresc !=
                                  double.parse(_numberTfController.text == ""
                                      ? "-1"
                                      : _numberTfController.text
                                          .replaceAll(",", ".")))
                              ? _buildCommentWidget()
                              : Container()
                          : _buildCommentWidget()
                      : Container(),

              (widget.dose == null)
                  // cas d'un produit
                  ? (widget.numberPresc !=
                              double.parse(_numberTfController.text == ""
                                  ? "-1"
                                  : _numberTfController.text
                                      .replaceAll(",", "."))) ||
                          _motifNomAdminTfController.text.isNotEmpty
                      ? SizedBox(height: 20)
                      : Container()
                  // cas d'un traitement
                  : ((widget.numberPresc !=
                                  double.parse(_numberTfController.text == ""
                                      ? "-1"
                                      : _numberTfController.text
                                          .replaceAll(",", "."))) ||
                              (widget.dosePresc !=
                                  double.parse(_doseTfController.text == ""
                                      ? "-1"
                                      : _doseTfController.text
                                          .replaceAll(",", ".")))) ||
                          _motifNomAdminTfController.text.isNotEmpty
                      ? widget.importantMessage.isNotEmpty
                          ? (widget.numberPresc !=
                                  double.parse(_numberTfController.text == ""
                                      ? "-1"
                                      : _numberTfController.text
                                          .replaceAll(",", ".")))
                              ? SizedBox(height: 20.0)
                              : Container()
                          : SizedBox(height: 20.0)
                      : Container(),

              // heure de prise
              GestureDetector(
                onTap: () async {
                  if (!widget.isLocked) {
                    if (_differenceBetweenSessionStartTimeAndNowInHours > 24) {
                      List list = await MyDatePicker(
                              context: context,
                              dateHint: widget.dose == null
                                  ? allTranslations
                                      .text('select_consumable_date')
                                  : allTranslations
                                      .text('select_treatment_date'),
                              timeHint: widget.dose == null
                                  ? allTranslations
                                      .text('select_consumable_time')
                                  : allTranslations
                                      .text('select_treatment_time'),
                              initialDate: _dateTaken,
                              minDate: _minimumDateTime,
                              maxDate: _maximumDateTime,
                              initialTime: _timeTaken)
                          .pickDateAndTime();
                      if ((list[0] != null) && (list[1] != null)) {
                        setState(() {
                          _dateTaken = list[0];
                          _timeTaken = list[1];
                        });
                      }
                    } else {
                      TimeOfDay? timePicked = await MyDatePicker(
                              context: context,
                              timeHint: widget.dose == null
                                  ? allTranslations
                                      .text('select_consumable_time')
                                  : allTranslations
                                      .text('select_treatment_time'),
                              initialTime: _timeTaken)
                          .pickTime();
                      if (timePicked != null) {
                        setState(() {
                          _dateTaken = _minimumDateTime;
                          _timeTaken = timePicked;
                        });
                      }
                    }
                  }
                },
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 20.0),
                  padding: EdgeInsets.all(10.0),
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(16.0)),
                      boxShadow: [
                        BoxShadow(
                            spreadRadius: 0.0,
                            blurRadius: 10.0,
                            color: AppTheme.primaryBlurColor.withOpacity(0.3))
                      ],
                      color: widget.isLocked
                          ? AppTheme.primaryColorDarker
                          : AppTheme.backgroundColor),
                  child: Center(
                    child: Text(
                      _timeTaken!.toLanguageString(),
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: widget.isLocked
                              ? AppTheme.backgroundColor
                              : _timeTaken!.isMidnight()
                                  ? AppTheme.tertiaryColor
                                  : AppTheme.primaryColor,
                          fontSize: 30),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 20.0)
            ],
          )),
        ));
  }

  Widget _buildNotTakenColumn() {
    return Form(
        key: _notTakenFormKey,
        child: Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: 10.0,
                ),
                // dropdown btn
                _buildCommentSelector(),
                SizedBox(
                  height: 20.0,
                ),
                // commentaire
                _buildCommentWidget(),
                SizedBox(
                  height: 20.0,
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildCommentSelector() {
    // build dropdown button pour le choix de la raison
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.0),
      padding: EdgeInsets.all(20.0),
      decoration: _decoration,
      height: 65,
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          hint: Text(
            allTranslations.text('choose_reason'),
            style: TextStyle(
                color: widget.isLocked
                    ? AppTheme.backgroundColor
                    : AppTheme.primaryColor,
                fontSize: FontSizeController.of(context).fontSize),
          ),
          isExpanded: true,
          value: _raisonNonAdmin,
          icon: ChevIcon(
            assetPath: "assets/images/icons/btChevBlancDw.svg",
          ),
          style: TextStyle(
              fontSize: FontSizeController.of(context).fontSize,
              color: AppTheme.primaryColor),
          onTap: () {
            if (TextFieldValidators.instance
                    .forText(_motifNomAdminTfController.text) ==
                null) {
              AlertBox(
                      context: context,
                      title: allTranslations.text('warning'),
                      description:
                          allTranslations.text('not_taken_warning_message'))
                  .showInfo();
            }
          },
          onChanged: (String? value) {
            setState(() {
              _raisonNonAdmin = value!;
            });
            _motifNomAdminTfController.clear();
            _motifNomAdminTfController.text = value!;
          },
          items: widget.isLocked
              ? null
              : List.generate(
                  _raisonNonAdminList.length,
                  (index) => DropdownMenuItem(
                        value: _raisonNonAdminList[index],
                        child: Text(
                          _raisonNonAdminList[index],
                          style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize:
                                  FontSizeController.of(context).fontSize),
                        ),
                      )),
        ),
      ),
    );
  }

  Widget _buildCommentWidget() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.0),
      padding: EdgeInsets.only(top: 15.0, bottom: 15.0),
      decoration: _decoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 20.0, right: 5.0),
            child: Text(
              allTranslations.text('comment'),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  color: widget.isLocked
                      ? AppTheme.backgroundColor
                      : AppTheme.primaryColor,
                  fontWeight: FontWeight.w600),
            ),
          ),
          CustomScrollbar(
            scrollbarColor: AppTheme.backgroundColor,
            child: TextFormField(
              enabled: !widget.isLocked,
              validator: TextFieldValidators.instance.forText,
              keyboardType: TextInputType.multiline,
              textInputAction: TextInputAction.newline,
              maxLines: 10,
              cursorColor: AppTheme.primaryColorDarker,
              controller: _motifNomAdminTfController,
              decoration: InputDecoration(
                  contentPadding: EdgeInsets.all(20.0),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.0),
                      borderSide: BorderSide.none)),
              onChanged: (String val) {
                setState(() {});
              },
              style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  color: widget.isLocked
                      ? AppTheme.backgroundColor
                      : AppTheme.primaryColor),
            ),
          )
        ],
      ),
    );
  }

  Future<void> _pushToQRcodeView() async {
    GS1Code128Data data = await Navigator.push(
        context,
        CupertinoPageRoute(
            builder: (context) => QrCodeBlocProvider(
                  action: 1,
                  child: ViewQRcode(),
                )));
    _handleGS1scanResponse(data);
  }

  void _handleGS1scanResponse(GS1Code128Data? data) {
    if (data != null) {
      if (data.getBatchNumber() != null) {
        setState(() {
          _batchNumberError = null;
        });
        _batchNumberController.clear();
        _batchNumberController.text = data.getBatchNumber()!;
      } else {
        setState(() {
          _batchNumberError = allTranslations.text('not_available');
        });
        _batchNumberController.clear();
      }
    }
  }

  void _changeStatus(bool treatmentTaken) {
    // sur changement du status du traitement

    if (widget.initialStatus == widget.currentStatus) {
      if (widget.initialStatus == Status.taken) {
        if (treatmentTaken) {
          _motifNomAdminTfController.text = widget.motif;
        } else {
          _motifNomAdminTfController.clear();
        }
      } else {
        if (treatmentTaken) {
          _motifNomAdminTfController.clear();
        } else {
          _motifNomAdminTfController.text = widget.motif;
        }
      }
    } else {
      if (widget.initialStatus == Status.taken) {
        if (treatmentTaken) {
          _motifNomAdminTfController.clear();
        } else {
          _motifNomAdminTfController.text = widget.motif;
        }
      } else {
        if (treatmentTaken) {
          _motifNomAdminTfController.text = widget.motif;
        } else {
          _motifNomAdminTfController.clear();
        }
      }
    }

    setState(() {
      _taken = treatmentTaken;
      _status = treatmentTaken ? Status.taken : Status.not_taken;
    });
  }

  bool _validateForm() {
    return _taken
        ? _takenFormKey.currentState!.validate()
        : _notTakenFormKey.currentState!.validate();
  }

  void _validateDetails() {
    // sur validation
    bool isFormValidated = _validateForm();
    if (isFormValidated && _canValidate()) {
      _popContextWithTreatmentOrConsumable();
    } else {
      HapticFeedback.vibrate();
    }
  }

  void _popContextWithTreatmentOrConsumable() {
    Navigator.of(context).pop(widget.dose == null
        ? ConsumableDetails(
            nom: widget.nomTrait,
            administre: _taken,
            nombre: _numberTfController.text.isNotEmpty
                ? double.parse(_numberTfController.text.replaceAll(",", "."))
                : 0,
            heure: _timeTaken!,
            numLot: _batchNumberController.text,
            motifNonAdmin: _motifNomAdminTfController.text,
            consumableID: widget.id,
            date: _dateTaken)
        : DrugDetails(
            nom: widget.nomTrait,
            administre: _taken,
            heureArret: _heureArret,
            nombre: _numberTfController.text.isNotEmpty
                ? double.parse(_numberTfController.text.replaceAll(",", "."))
                : 0,
            dose: _doseTfController.text.isNotEmpty
                ? double.parse(_doseTfController.text.replaceAll(",", "."))
                : 0,
            heure: _timeTaken!,
            numLot: _batchNumberController.text,
            motifNonAdmin: _motifNomAdminTfController.text,
            drugID: widget.id,
            date: _dateTaken));
  }

  bool _canValidate() {
    bool condition1 =
        // condition sur la saisie des nombres et doses et du message justificatidf si nécessaire
        widget.importantMessage.isEmpty
            ?
            // pas de message important
            widget.dose != null
                ?
                // cas d'un traitement
                (widget.numberPresc !=
                            double.parse(_numberTfController.text == ""
                                ? "-1"
                                : _numberTfController.text
                                    .replaceAll(",", ".")) ||
                        widget.dosePresc !=
                            double.parse(_doseTfController.text == ""
                                ? "-1"
                                : _doseTfController.text.replaceAll(",", ".")))
                    ?
                    // la dose OU le nombre ne correspondent pas au valeurs prescrites
                    _motifNomAdminTfController.text.isNotEmpty
                    :
                    // la dose et le nombre correspondent aux valeurs prescrites
                    true
                :
                // cas d'un produit
                (widget.numberPresc !=
                        double.parse(_numberTfController.text == ""
                            ? "-1"
                            : _numberTfController.text.replaceAll(",", ".")))
                    ?
                    // le nombre ne correspond pas à la valeur prescrite
                    _motifNomAdminTfController.text.isNotEmpty
                    :
                    // la dose correspond à la valeur prescrite
                    true
            :
            // message important
            widget.numberPresc !=
                    double.parse(_numberTfController.text == ""
                        ? "-1"
                        : _numberTfController.text.replaceAll(",", "."))
                ?
                // nombre incohérent avec la valeur prescrite
                _motifNomAdminTfController.text.isNotEmpty
                    ?
                    // un message justificatif a été saisi
                    _doseTfController.text.isNotEmpty
                    :
                    // pas de message justificatif
                    false
                :
                // nombre cohérent avec la valeur prescrite
                _doseTfController.text.isNotEmpty;

    bool condition2 =
        // condition sur les heures saisies
        widget.anticoagulant
            ?
            // le traitement est un anticoagulant
            !_heureArret.isMidnight() && !_timeTaken!.isMidnight()
            :
            // le traitement n'est pas un anticoagulant
            !_timeTaken!.isMidnight();

    bool canValidate = _taken
        ? condition1 == true && condition2 == true
        : _motifNomAdminTfController.text.isNotEmpty;

    return canValidate;
  }
}
