import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/FiledCalculation.dart';
import 'package:theradom/models/area_structure.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/SessionMonitoringDetailsFieldBuilder.dart';

class SessionCalculationWeighToLose extends StatefulWidget {
  //final String poidsSec;
  final FieldCalculation fieldCalculation;
  final AreaStructure poidsDebutAS;
  final AreaStructure? apportsRealisesAS;
  final bool normalite;
  final bool locked;
  final void Function(String, String, dynamic) onValueSelected;

  SessionCalculationWeighToLose(
      {
      //required this.poidsSec,
      required this.fieldCalculation,
      required this.poidsDebutAS,
      required this.apportsRealisesAS,
      required this.normalite,
      required this.locked,
      required this.onValueSelected});

  _SessionCalculationWeighToLoseState createState() =>
      _SessionCalculationWeighToLoseState();
}

class _SessionCalculationWeighToLoseState
    extends State<SessionCalculationWeighToLose> {
  late double _poidsAperdre;
  late final String _calculatedFieldLabel;

  @override
  void initState() {
    super.initState();
    _poidsAperdre = _calculPoidsAperdre();
    _calculatedFieldLabel = widget.poidsDebutAS.fieldID.unite.isEmpty
        ? allTranslations.text('weight_to_lose')
        : "${allTranslations.text('weight_to_lose')} (${widget.poidsDebutAS.fieldID.unite})";
    widget.fieldCalculation.addListener(_updatePoidsAperdre);
  }

  @override
  void dispose() {
    widget.fieldCalculation.removeListener(_updatePoidsAperdre);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      // poids de début
      SessionMonitoringDetailsFieldBuilder(
          valeur: widget.fieldCalculation.getPoidsDebut
              .toString()
              .replaceAll(".", ","),
          normalite: widget.normalite,
          locked: widget.locked,
          fieldDescription: widget.poidsDebutAS.fieldID,
          onValueSelected: _updateCalculationAndChangeSelectedValue),

      // apports réalisés
      widget.apportsRealisesAS != null
          ? SessionMonitoringDetailsFieldBuilder(
              valeur: widget.fieldCalculation.getApportsRealises
                  .toString()
                  .replaceAll(".", ","),
              normalite: widget.normalite,
              locked: widget.locked,
              fieldDescription: widget.apportsRealisesAS!.fieldID,
              onValueSelected: _updateCalculationAndChangeSelectedValue)
          : Container(),

      // poids de début
      Container(
        margin: EdgeInsets.only(top: 5.0, bottom: 5.0),
        decoration: BoxDecoration(
            color: AppTheme.secondaryColor,
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(color: AppTheme.primaryColor)),
        child: ListTile(
          title: Text(
            _calculatedFieldLabel,
            style: TextStyle(
                fontSize: FontSizeController.of(context).fontSize,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor),
          ),
          subtitle: Text(
            _poidsAperdre.toString().replaceAll(".", ","),
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontSize: FontSizeController.of(context).fontSize,
            ),
          ),
        ),
      )
    ]);
  }

  void _updateCalculationAndChangeSelectedValue(
      String idBd, String libelle, dynamic value) {
    _updateVar(idBd, value);
    widget.onValueSelected(idBd, libelle, value);
    _updatePoidsAperdre();
  }

  void _updateVar(String idBd, dynamic value) {
    switch (idBd) {
      case Config.idPoidsSec:
        {
          // poids sec
          widget.fieldCalculation.setPoidsSec =
              double.parse(value.toString().replaceAll(",", "."));
        }
        break;
      case Config.idPoidsDebut:
        {
          // poids de début
          widget.fieldCalculation.setPoidsDebut =
              double.parse(value.toString().replaceAll(",", "."));
        }
        break;
      case Config.idApportsRealises:
        {
          // apports réalisés
          widget.fieldCalculation.setApportsRealises =
              double.parse(value.toString().replaceAll(",", "."));
        }
        break;
    }
  }

  double _calculPoidsAperdre() {
    double poidsPerdre = widget.fieldCalculation.getPoidsDebut -
        widget.fieldCalculation.getPoidsSec +
        widget.fieldCalculation.getApportsRealises;
    poidsPerdre = double.parse(poidsPerdre.toStringAsFixed(1));
    return poidsPerdre;
  }

  void _updatePoidsAperdre() {
    setState(() {
      _poidsAperdre = _calculPoidsAperdre();
    });
  }
}
