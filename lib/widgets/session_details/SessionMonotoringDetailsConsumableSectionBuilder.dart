import 'package:flutter/material.dart';
import 'package:theradom/models/consumable.dart';
import 'package:theradom/models/consumable_details.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/error_text.dart';
import 'package:theradom/widgets/session_details/SessionSection.dart';
import 'package:theradom/widgets/session_details/treatment_container.dart';

class SessionMonitoringDetailsConsumableSectionBuilder extends StatelessWidget {
  final Map<String, Consumable>? produits;
  final DateTime dateDebutSeance;
  final TimeOfDay heureDebutSeance;
  final bool locked;
  final void Function(ConsumableDetails, bool) onConsumableUpdate;

  SessionMonitoringDetailsConsumableSectionBuilder(
      {required this.produits,
      required this.dateDebutSeance,
      required this.heureDebutSeance,
      required this.locked,
      required this.onConsumableUpdate});

  @override
  Widget build(BuildContext context) {
    List<Widget> produitsFieldList = [];

    if (produits != null) {
      produits!.forEach((k, produit) {
        produitsFieldList.add(TreatmentContainer(
            produit: produit,
            onConsumableUpdate: onConsumableUpdate,
            locked: locked,
            dateDebutSeance: dateDebutSeance,
            heureDebutSeance: heureDebutSeance));
      });
    } else {
      produitsFieldList.add(ErrorText(
        message: allTranslations.text('error_loading_consumables'),
      ));
    }

    Widget treatmentSection;
    if (produitsFieldList.length > 0) {
      treatmentSection = SessionSection(
          title: allTranslations.text('session_consumables'),
          childrenList: produitsFieldList,
          locked: locked);
    } else {
      treatmentSection = Container();
    }

    return treatmentSection;
  }
}
