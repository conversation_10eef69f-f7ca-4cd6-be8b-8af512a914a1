import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:theradom/models/FieldDescription.dart';
import 'package:theradom/models/session_input_data.dart';
import 'package:theradom/utils/html_helper.dart';
import 'package:theradom/widgets/session_details/custom_checkbox.dart';
import 'package:theradom/widgets/session_details/enum_formfield.dart';
import 'package:theradom/widgets/session_details/textfield_formfield.dart';

class SessionMonitoringDetailsFieldBuilder extends StatefulWidget {
  final String valeur;
  final FieldDescription fieldDescription;
  final bool normalite;
  final bool locked;
  final void Function(String, String, dynamic) onValueSelected;

  SessionMonitoringDetailsFieldBuilder(
      {required this.valeur,
      required this.fieldDescription,
      required this.normalite,
      required this.locked,
      required this.onValueSelected});

  _SessionMonitoringDetailsFieldBuilderState createState() =>
      _SessionMonitoringDetailsFieldBuilderState();
}

class _SessionMonitoringDetailsFieldBuilderState
    extends State<SessionMonitoringDetailsFieldBuilder> {
  late final String _format;
  late final FieldDescription _fieldDescription;
  late final SessionInputData _sessionInputData;

  @override
  void initState() {
    super.initState();
    _fieldDescription = widget.fieldDescription;
    _format = _fieldDescription.format;
    _sessionInputData = SessionInputData(
        fieldDescription: widget.fieldDescription, normalite: widget.normalite);
  }

  @override
  Widget build(BuildContext context) {
    Widget monitoringField;

    // le cas "heure" peut être géré comme un string avec la condition -> || (_format == "\"heure\"")
    if ((_format == "\"float\"") ||
        (_format == "\"booleen\"") ||
        (_format == "\"alphanumerique\"") ||
        (_format == "\"entier\"") ||
        (_format == "\"\"")) {
      // cas où format est un string

      // récupération des paramètres keyboardType, inputFormatter et valeur à partir de "format
      dynamic value = _sessionInputData.getValueWithType(widget.valeur);
      _sessionInputData.init();

      if (value is bool) {
        // si la valeur est un booléen
        monitoringField = CustomCheckbox(
            initialValue: value,
            onValueChanged: widget.onValueSelected,
            locked: widget.locked,
            sessionInputData: _sessionInputData);
        return monitoringField;
      } else {
        // cas d'une saisie
        HtmlHelper htmlHelper = HtmlHelper();
        monitoringField = TextfieldFormfield(
            value: htmlHelper.convert(widget.valeur),
            locked: widget.locked,
            sessionInputData: _sessionInputData,
            onValueUpdated: widget.onValueSelected);
      }
    } else {
      // cas où format est un ListValues dans la doc
      List<String> listDropdownValues = [];
      var listValues = json.decode(_format);
      listValues.forEach((key, value) {
        listDropdownValues.add(value.toString());
      });
      String? val = widget.valeur;
      if (listDropdownValues.contains(val) == false) {
        val = null;
      }
      monitoringField = EnumFormfield(
          dataList: listDropdownValues,
          onValueChanged: widget.onValueSelected,
          sessionInputData: _sessionInputData,
          valeur: val,
          locked: widget.locked);
    }

    return monitoringField;
  }
}
