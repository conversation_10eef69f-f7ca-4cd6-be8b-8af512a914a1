import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class PlatformCircularIndicator extends StatelessWidget {
  final bool darkIndicator;

  PlatformCircularIndicator({required this.darkIndicator});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Platform.isIOS
          ? Theme(
              data: ThemeData(
                cupertinoOverrideTheme: CupertinoThemeData(
                    brightness:
                        darkIndicator ? Brightness.light : Brightness.dark),
              ),
              child: CupertinoActivityIndicator(),
            )
          : SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                  strokeWidth: 1.0,
                  valueColor: darkIndicator
                      ? AlwaysStoppedAnimation<Color>(AppTheme.primaryColor)
                      : AlwaysStoppedAnimation<Color>(
                          AppTheme.backgroundColor)),
            ),
    );
  }
}
