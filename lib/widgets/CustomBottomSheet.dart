import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/buttons/large_button.dart';

class CustomBottomSheet {
  final BuildContext context;
  final List<String> labels;

  CustomBottomSheet({required this.context, required this.labels});

  Future<dynamic> show() async {
    //retourne l'index de l'action, null en cas d'annulation

    List<Widget> customButtons = [];

    // création des boutons custom
    labels.asMap().forEach((index, label) {
      customButtons.add(LargeButton(
        title: label,
        titleColor: AppTheme.primaryColor,
        backgroundColor: AppTheme.secondaryColor,
        showShadow: false,
        onTap: () => Navigator.of(context).pop(index),
      ));
    });

    // ajout du bouton pour annuler
    customButtons.add(LargeButton(
        title: allTranslations.text('cancel'),
        titleColor: AppTheme.backgroundColor,
        backgroundColor: AppTheme.primaryColor,
        showShadow: false,
        onTap: () => Navigator.of(context).pop()));

    return showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext ctxt) {
          return Container(
            padding: EdgeInsets.only(top: 10.0),
            height: 74 * customButtons.length.toDouble() + 20,
            decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius:
                    BorderRadius.vertical(top: Radius.circular(20.0))),
            child: Column(children: customButtons),
          );
        });
  }
}
