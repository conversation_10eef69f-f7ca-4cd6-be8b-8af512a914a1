import 'package:flutter/material.dart';
import 'package:theradom/models/stats_field_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/buttons/cac_button.dart';

class StatsDataTile extends StatefulWidget {
  final StatsFieldData statsFieldData;
  final void Function(StatsFieldData) onSelection;

  StatsDataTile({required this.statsFieldData, required this.onSelection});

  _StatsDataTileState createState() => _StatsDataTileState();
}

class _StatsDataTileState extends State<StatsDataTile> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
        contentPadding:
            EdgeInsets.only(top: 5.0, bottom: 5.0, left: 21.0, right: 10.0),
        selected: widget.statsFieldData.selected,
        selectedTileColor: AppTheme.primaryBlurColor.withOpacity(0.3),
        title: Text(
          widget.statsFieldData.name,
          maxLines: 5,
          style: TextStyle(
              fontSize: FontSizeController.of(context).fontSize,
              color: AppTheme.primaryColor),
        ),
        leading: CacButton(checked: widget.statsFieldData.selected),
        onTap: _changeFieldSelection);
  }

  void _changeFieldSelection() {
    // modifie la sélection de la listTile et appelle le callback pour sélectionner/déselectionner la valeur
    setState(() {
      widget.statsFieldData.selected = !widget.statsFieldData.selected;
    });
    _callbackOnSelection();
  }

  void _callbackOnSelection() {
    widget.onSelection(widget.statsFieldData);
  }
}
