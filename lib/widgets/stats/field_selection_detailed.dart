import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/stat_field_data_group.dart';
import 'package:theradom/models/stats_field_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/stats/field_selection_tile.dart';
import 'package:theradom/widgets/buttons/cac_button.dart';
import 'package:theradom/widgets/buttons/custom_close_button.dart';

class FieldSelectionDetailed extends StatefulWidget {
  final StatsFieldDataGroup statsFieldDataGroup;
  final void Function(StatsFieldDataGroup) onModification;

  FieldSelectionDetailed(
      {required this.statsFieldDataGroup, required this.onModification})
      : super();

  _FieldSelectionDetailedState createState() => _FieldSelectionDetailedState();
}

class _FieldSelectionDetailedState extends State<FieldSelectionDetailed> {
  late final StatsFieldDataGroup _statsFieldDataGroup;
  late int _selectedItemCount;

  @override
  void initState() {
    super.initState();
    _statsFieldDataGroup = widget.statsFieldDataGroup;
    _selectedItemCount = _statsFieldDataGroup.selectedFieldCount();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height -
          Config.appBarHeight -
          (MediaQuery.of(context).size.shortestSide < 600 ? 25 : 10),
      decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(25.0))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            height: 63.5,
            padding: EdgeInsets.only(top: 5.0, bottom: 5.0, right: 21.0),
            decoration: BoxDecoration(
                border: Border(
                    bottom:
                        BorderSide(width: 0.5, color: AppTheme.dividerColor))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () => _selectedItemCount ==
                          _statsFieldDataGroup.statsFieldDataList.length
                      ? _deselectAllFieldsAndUpdateSelectedCount()
                      : _selectAllFieldsAndUpdateSelectedCount(),
                  child: Container(
                    padding: EdgeInsets.only(left: 21.0),
                    child: CacButton(
                      checked: _selectedItemCount ==
                              _statsFieldDataGroup.statsFieldDataList.length
                          ? true
                          : false,
                    ),
                  ),
                ),
                Text(
                  _statsFieldDataGroup.name.toUpperCase(),
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontSize: FontSizeController.of(context).fontSize,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold),
                ),
                CustomCloseButton(
                    hasBackgroundColor: false,
                    onTap: () => Navigator.of(context).pop())
              ],
            ),
          ),
          Expanded(
              child: ListView(
            shrinkWrap: true,
            padding: EdgeInsets.only(),
            children: ListTile.divideTiles(
              color: AppTheme.primaryColor,
              context: context,
              tiles: List.generate(
                  _statsFieldDataGroup.statsFieldDataList.length,
                  (index) => StatsDataTile(
                      statsFieldData:
                          _statsFieldDataGroup.statsFieldDataList[index],
                      onSelection: _onDataTileSelection)),
            ).toList(),
          ))
        ],
      ),
    );
  }

  void _selectAllFieldsAndUpdateSelectedCount() {
    _statsFieldDataGroup.selectAll();
    _updateSelectedCount();
    _callbackToUpdateGroupTile();
  }

  void _deselectAllFieldsAndUpdateSelectedCount() {
    _statsFieldDataGroup.deselectAll();
    _updateSelectedCount();
    _callbackToUpdateGroupTile();
  }

  void _updateSelectedCount() {
    setState(() {
      _selectedItemCount = _statsFieldDataGroup.selectedFieldCount();
    });
  }

  void _onDataTileSelection(StatsFieldData updatedStatsFieldData) {
    _updateStatsFieldDataList(updatedStatsFieldData);
    _updateSelectedCount();
    _callbackToUpdateGroupTile();
  }

  void _updateStatsFieldDataList(StatsFieldData updatedStatsFieldData) {
    int index = _statsFieldDataGroup.statsFieldDataList.indexWhere(
        (statsFieldData) => statsFieldData.id == updatedStatsFieldData.id);
    _statsFieldDataGroup.statsFieldDataList[index] = updatedStatsFieldData;
  }

  void _callbackToUpdateGroupTile() {
    widget.onModification(_statsFieldDataGroup);
  }
}
