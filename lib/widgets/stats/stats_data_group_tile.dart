import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/models/stat_field_data_group.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/stats/field_selection_detailed.dart';
import 'package:theradom/widgets/buttons/cac_button.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class StatsDataGroupTile extends StatefulWidget {
  final StatsFieldDataGroup dataGroup;
  final void Function(StatsFieldDataGroup) onGroupModification;

  StatsDataGroupTile(
      {required this.dataGroup, required this.onGroupModification});

  _StatsDataGroupTileState createState() => _StatsDataGroupTileState();
}

class _StatsDataGroupTileState extends State<StatsDataGroupTile> {
  late StatsFieldDataGroup _dataGroup;
  late int _count;

  @override
  void initState() {
    super.initState();
    _dataGroup = widget.dataGroup;
    _count = _dataGroup.selectedFieldCount();
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
        selected: _count > 0 ? true : false,
        selectedTileColor: AppTheme.backgroundColor.withOpacity(0.2),
        leading: CacButton(
          checked: _count > 0 ? true : false,
        ),
        title: Text(
          _dataGroup.name,
          style: TextStyle(
              color: AppTheme.backgroundColor,
              fontWeight: FontWeight.w600,
              fontSize: FontSizeController.of(context).fontSize),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: _count > 0
              ? [
                  Text(
                    "$_count",
                    style: TextStyle(
                        color: AppTheme.backgroundColor, fontSize: 16),
                  ),
                  SizedBox(
                    width: 10.0,
                  ),
                  SvgPicture.asset(
                    "assets/images/icons/icEdit.svg",
                  )
                ]
              : [],
        ),
        onTap: () async {
          await _openFieldSelectionSheet();
        });
  }

  Future<void> _openFieldSelectionSheet() async {
    await showMaterialModalBottomSheet(
        expand: false,
        enableDrag: false,
        isDismissible: false,
        elevation: 0,
        duration: Duration(milliseconds: 500),
        context: context,
        backgroundColor: Colors.transparent,
        builder: (_) => FieldSelectionDetailed(
            statsFieldDataGroup: _dataGroup,
            onModification: _onGroupSelectionModification));
  }

  void _onGroupSelectionModification(
      StatsFieldDataGroup updatedStatsFieldDataGroup) {
    _dataGroup = updatedStatsFieldDataGroup;
    setState(() {
      _count = _dataGroup.selectedFieldCount();
    });
    widget.onGroupModification(_dataGroup);
  }
}
