import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';

/// Widget qui permet de sélectionner deux dates prédéfinies afin de recharger les courbes avec un nouvel intervalle de dates

class StatsShortcutButtonsRow extends StatefulWidget {
  final void Function(DateTime, DateTime) onDatesSelected;

  StatsShortcutButtonsRow({required this.onDatesSelected});

  @override
  _StatsShortcutButtonsRowState createState() =>
      _StatsShortcutButtonsRowState();
}

class _StatsShortcutButtonsRowState extends State<StatsShortcutButtonsRow> {
  final DateTime _dateNow = DateTime.now();
  late final DateTime _dateOneWeekAgo;
  late final DateTime _dateTwoWeeksAgo;
  late final DateTime _dateOneMonthAgo;
  late final DateTime _dateThreeMonthsAgo;
  late final DateTime _dateSixMonthsAgo;
  late final DateTime _dateOneYearAgo;
  late final DateTime _dateTwoYearsAgo;
  double _fontSize = 18.0;

  @override
  void initState() {
    super.initState();
    _dateOneWeekAgo = _dateNow.subtract(Duration(days: 7));
    _dateTwoWeeksAgo = _dateNow.subtract(Duration(days: 14));
    _dateOneMonthAgo =
        DateTime(_dateNow.year, _dateNow.month - 1, _dateNow.day);
    _dateThreeMonthsAgo =
        DateTime(_dateNow.year, _dateNow.month - 3, _dateNow.day);
    _dateSixMonthsAgo =
        DateTime(_dateNow.year, _dateNow.month - 6, _dateNow.day);
    _dateOneYearAgo = DateTime(_dateNow.year - 1, _dateNow.month, _dateNow.day);
    _dateTwoYearsAgo =
        DateTime(_dateNow.year - 2, _dateNow.month, _dateNow.day);
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          // Absorber les taps qui ne touchent pas les boutons
        },
        child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildButton("1S", _dateOneWeekAgo),
                SizedBox(width: 10.0),
                _buildButton("2S", _dateTwoWeeksAgo),
                SizedBox(width: 10.0),
                _buildButton("1M", _dateOneMonthAgo),
                SizedBox(width: 10.0),
                _buildButton("3M", _dateThreeMonthsAgo),
                SizedBox(width: 10.0),
                _buildButton("6M", _dateSixMonthsAgo),
                SizedBox(width: 10.0),
                _buildButton("1A", _dateOneYearAgo),
                SizedBox(width: 10.0),
                _buildButton("2A", _dateTwoYearsAgo),
              ],
            )),
      ),
    );
  }

  Widget _buildButton(String label, DateTime dateFrom) {
    return InkWell(
      onTap: () {
        print("🔍 BUTTON TRACE 1: Début onTap pour bouton $label");
        print("🔍 BUTTON TRACE 2: dateFrom: $dateFrom, dateTo: $_dateNow");

        try {
          print("🔍 BUTTON TRACE 3: Avant appel widget.onDatesSelected");
          widget.onDatesSelected(dateFrom, _dateNow);
          print(
              "🔍 BUTTON TRACE 4: Après appel widget.onDatesSelected - SUCCÈS");
        } catch (e) {
          print("🔍 BUTTON TRACE ERROR: Erreur dans onDatesSelected: $e");
          print("🔍 BUTTON TRACE ERROR: Stack trace: ${StackTrace.current}");
        }

        print("🔍 BUTTON TRACE 5: Fin onTap pour bouton $label");
      },
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Container(
        height: 30.0,
        width: 30.0,
        decoration: BoxDecoration(
            color: AppTheme.primaryColorDarker,
            borderRadius: BorderRadius.circular(4.0)),
        child: Center(
          child: Text(
            label,
            style:
                TextStyle(color: AppTheme.backgroundColor, fontSize: _fontSize),
          ),
        ),
      ),
    );
  }
}
