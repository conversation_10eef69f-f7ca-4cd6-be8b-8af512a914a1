import 'package:flutter/material.dart';
import 'package:theradom/utils/charts/chart_marker.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/charts/XAxisValueFormatter.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:mp_chart/mp/chart/line_chart.dart';
import 'package:mp_chart/mp/controller/line_chart_controller.dart';
import 'package:mp_chart/mp/core/adapter_android_mp.dart';
import 'package:mp_chart/mp/core/common_interfaces.dart';
import 'package:mp_chart/mp/core/data/line_data.dart';
import 'package:mp_chart/mp/core/data_interfaces/i_line_data_set.dart';
import 'package:mp_chart/mp/core/entry/entry.dart';
import 'package:mp_chart/mp/core/enums/legend_form.dart';
import 'package:mp_chart/mp/core/enums/legend_horizontal_alignment.dart';
import 'package:mp_chart/mp/core/enums/legend_vertical_alignment.dart';
import 'package:mp_chart/mp/core/enums/x_axis_position.dart';
import 'package:mp_chart/mp/core/highlight/highlight.dart';
import 'package:mp_chart/mp/core/legend/legend_entry.dart';
import 'package:mp_chart/mp/core/limit_line.dart';

/// Widget pour paraméter l'aperçu d'une courbe

class MultilineChart extends StatefulWidget {
  final List<ILineDataSet> iLineDataSetList;
  final List<LimitLine?> limitLineList;
  final bool enableGestures;

  MultilineChart(
      {required this.iLineDataSetList,
      required this.limitLineList,
      required this.enableGestures});

  @override
  State<StatefulWidget> createState() => _MultilineChartState();
}

class _MultilineChartState extends State<MultilineChart>
    implements OnChartValueSelectedListener, OnChartGestureListener {
  late final LineChartController _controller;
  int _selectedTimestamp = -1;
  List<double> _selectedMeasureList = [];
  LegendForm _legendForm = LegendForm.CIRCLE;
  double _formSize = 5.0;
  double _formLineWidth = 1.0;
  DashPathEffect _formLineDashEffect = DashPathEffect(0, 0, 0);
  List<Color> _colorList = AppTheme.chartColorList;
  double _fontSize = 16.0;

  void onChartDoubleTapped(double positionX, double positionY) {
    print("Chart double-tapped.");
  }

  void onChartScale(
      double positionX, double positionY, double scaleX, double scaleY) {
    print("ScaleX: $scaleX, ScaleY: $scaleY");
  }

  void onChartSingleTapped(double positionX, double positionY) {
    print("Chart single-tapped.");
  }

  void onChartTranslate(
      double positionX, double positionY, double dX, double dY) {
    print("dX: $dX, dY: $dY");
  }

  void onNothingSelected() {}

  void onValueSelected(Entry? e, Highlight? h) {
    // sur sélection d'un point
    //print("VAL SELECTED Value: ${e?.y}, xIndex: ${e?.x}, DataSet index: ${h?.dataIndex}");

    if (widget.enableGestures) {
      // mesures
      for (int i = 0; i < widget.iLineDataSetList.length; i++) {
        double? measure =
            widget.iLineDataSetList[i].getEntryForXValue2(e?.x, e?.y)?.y;
        if (measure != null) _selectedMeasureList.add(measure);
      }

      // timestamp
      setState(() {
        _selectedTimestamp = (e?.x)!.round();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _initController();
    _initLineData();
  }

  @override
  Widget build(BuildContext context) {
    return LineChart(_controller);
  }

  void _initController() {
    _controller = LineChartController(
        noDataText: allTranslations.text('no_data'),
        marker: ChartMarker(
            backColor: AppTheme.primaryColor,
            textColor: AppTheme.backgroundColor,
            fontSize: _fontSize),
        axisLeftSettingFunction: (axisLeft, controller) {
          axisLeft?.enabled = true;
          axisLeft?.spacePercentBottom = 0;
          axisLeft?.spacePercentTop = 0;
          axisLeft?.gridColor = AppTheme.primaryColor.withOpacity(0.3);
          axisLeft?.textColor = AppTheme.primaryColorDarker;
          axisLeft?.textSize = _fontSize;
          axisLeft?.setLabelCount2(5, true);
          widget.limitLineList.forEach((limitLine) {
            if (limitLine != null) {
              axisLeft?.addLimitLine(limitLine);
              axisLeft?.limitLines!.forEach((element) {
                element.enableDashedLine(20.0, 5.0, 2.0);
              });
            }
          });
        },
        axisRightSettingFunction: (axisRight, controller) {
          axisRight?.enabled = false;
        },
        legendSettingFunction: (legend, controller) {
          legend
            ?..enabled = true
            ..setCustom(_getLegendEntryList())
            ..horizontalAlignment = widget.enableGestures
                ? LegendHorizontalAlignment.CENTER
                : LegendHorizontalAlignment.LEFT
            ..verticalAlignment = widget.enableGestures
                ? LegendVerticalAlignment.BOTTOM
                : LegendVerticalAlignment.TOP
            ..xOffset = 1.0
            ..wordWrapEnabled = true
            ..textSize = _fontSize
            ..drawInside = false;
        },
        xAxisSettingFunction: (xAxis, controller) {
          xAxis
            ?..position = XAxisPosition.BOTTOM
            ..gridColor = AppTheme.primaryColor.withOpacity(0.3)
            ..textColor = AppTheme.primaryColorDarker
            ..setLabelCount2(3, true)
            ..enabled = true
            ..drawAxisLine = (true)
            ..drawGridLines = (true)
            ..textSize = _fontSize
            ..setValueFormatter(XAxisValueFormatter());
        },
        drawMarkers: false,
        selectionListener: this,
        doubleTapToZoomEnabled: false,
        resolveGestureHorizontalConflict: widget.enableGestures,
        resolveGestureVerticalConflict: widget.enableGestures,
        drawGridBackground: false,
        dragXEnabled: true,
        dragYEnabled: true,
        scaleXEnabled: widget.enableGestures,
        scaleYEnabled: widget.enableGestures,
        pinchZoomEnabled: false,
        drawBorders: false,
        backgroundColor: Colors.transparent);
  }

  void _initLineData() {
    _controller.data = LineData.fromList(widget.iLineDataSetList);
    _controller.data?.setHighlightEnabled(true);
    _controller.data?.setDrawValues(false);
    _controller.drawMarkers = widget.enableGestures;
    _controller.initMarker();
  }

  List<LegendEntry> _getLegendEntryList() {
    // créé et retourne la liste des légendes des courbes
    List<LegendEntry> legendEntryList = [];
    String? inf;
    String? sup;
    widget.iLineDataSetList.asMap().forEach((index, iLineDataSet) {
      String label = iLineDataSet.getLabel();
      if ((label != "*inf*") && (label != "*sup*")) {
        // courbes
        if (_selectedTimestamp != -1)
          label = label + ": ${_selectedMeasureList[index]}";
        legendEntryList.add(LegendEntry(label, _legendForm, _formSize,
            _formLineWidth, _formLineDashEffect, _colorList[index]));
      } else {
        // courbes normales inf et sup
        if (label == "*inf*") {
          inf = iLineDataSet.getYMax().toString();
        }
        if (label == "*sup*") {
          sup = iLineDataSet.getYMax().toString();
        }
      }
    });

    if ((inf != null) && (sup != null)) {
      legendEntryList.add(LegendEntry(
          "${allTranslations.text('normality')}: $inf-$sup",
          LegendForm.SQUARE,
          _formSize,
          _formLineWidth,
          _formLineDashEffect,
          AppTheme.chartNormalColor));
    }

    return legendEntryList;
  }
}
