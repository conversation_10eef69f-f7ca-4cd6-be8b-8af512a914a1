import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class BasicLineChart extends StatelessWidget {
  final List<LineChartBarData> lineDataList;

  const BasicLineChart({
    Key? key,
    required this.lineDataList,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (lineDataList.isEmpty) {
      return Center(
        child: Text(
          'Aucune donnée à afficher',
          style: TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 16,
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: CustomPaint(
        painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(lineDataList),
        child: Container(
          width: double.infinity,
          height: double.infinity,
        ),
      ),
    );
  }
}

class LineChartPainter extends CustomPainter {
  final List<LineChartBarData> lineDataList;

  LineChartPainter(this.lineDataList);

  @override
  void paint(Canvas canvas, Size size) {
    if (lineDataList.isEmpty) return;

    // Trouver les valeurs min/max pour l'échelle
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (var lineData in lineDataList) {
      for (var spot in lineData.spots) {
        minX = minX < spot.x ? minX : spot.x;
        maxX = maxX > spot.x ? maxX : spot.x;
        minY = minY < spot.y ? minY : spot.y;
        maxY = maxY > spot.y ? maxY : spot.y;
      }
    }

    // Ajouter une marge
    double marginX = (maxX - minX) * 0.1;
    double marginY = (maxY - minY) * 0.1;
    minX -= marginX;
    maxX += marginX;
    minY -= marginY;
    maxY += marginY;

    // Dessiner les axes
    Paint axisPaint = Paint()
      ..color = AppTheme.primaryColor.withOpacity(0.3)
      ..strokeWidth = 1;

    // Axe X
    canvas.drawLine(
      Offset(0, size.height),
      Offset(size.width, size.height),
      axisPaint,
    );

    // Axe Y
    canvas.drawLine(
      Offset(0, 0),
      Offset(0, size.height),
      axisPaint,
    );

    // Dessiner les lignes et points
    for (int i = 0; i < lineDataList.length; i++) {
      var lineData = lineDataList[i];
      Color lineColor = lineData.color ??
          AppTheme.chartColorList[i % AppTheme.chartColorList.length];

      Paint linePaint = Paint()
        ..color = lineColor
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      Paint pointPaint = Paint()
        ..color = lineColor
        ..style = PaintingStyle.fill;

      Path path = Path();
      bool firstPoint = true;

      for (var spot in lineData.spots) {
        double x = ((spot.x - minX) / (maxX - minX)) * size.width;
        double y =
            size.height - ((spot.y - minY) / (maxY - minY)) * size.height;

        if (firstPoint) {
          path.moveTo(x, y);
          firstPoint = false;
        } else {
          path.lineTo(x, y);
        }

        // Dessiner le point
        canvas.drawCircle(Offset(x, y), 3, pointPaint);
        canvas.drawCircle(
            Offset(x, y),
            3,
            Paint()
              ..color = Colors.white
              ..strokeWidth = 1
              ..style = PaintingStyle.stroke);
      }

      // Dessiner la ligne
      canvas.drawPath(path, linePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
