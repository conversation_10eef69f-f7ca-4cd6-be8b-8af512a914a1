import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/UserAccountInfos.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/views/settings/ViewChangeEmail.dart';
import 'package:theradom/views/settings/ViewChangePassword.dart';
import 'package:theradom/widgets/expandable_container.dart';
import 'package:theradom/widgets/icons/chev_icon.dart';
import 'package:theradom/extensions/string_extension.dart';
import 'package:settings_ui/settings_ui.dart';

/// Expandable container du profil utilisateur

class ProfileExpandableContainer extends StatefulWidget {
  _ProfileExpandableContainerState createState() =>
      _ProfileExpandableContainerState();
}

class _ProfileExpandableContainerState extends State<ProfileExpandableContainer>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  bool _expandFlag = false;
  UserAccountInfos _userAccountInfos = Config.userAccountInfos!;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
        duration: const Duration(milliseconds: 500),
        vsync: this,
        value: 1,
        lowerBound: 0,
        upperBound: 1);
    _animation = CurvedAnimation(
        parent: _animationController, curve: Curves.fastOutSlowIn);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ExpandableContainer(
      collapsedHeight: MediaQuery.of(context).size.height / 2.9,
      expandedHeight: MediaQuery.of(context).size.height,
      expanded: _expandFlag,
      child: Container(
        color: AppTheme.primaryColor,
        child: Column(
          children: [
            // appbar
            AppBar(
              elevation: 0,
              leadingWidth: 50,
              leading: GestureDetector(
                onTap: () => _onLeadingTap(),
                child: Container(
                    margin: EdgeInsets.only(left: 21.0),
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppTheme.backgroundColor),
                    child: Stack(
                      children: [
                        AnimatedOpacity(
                          opacity: _expandFlag ? 1.0 : 0.0,
                          duration: Duration(milliseconds: 500),
                          child: Container(
                            padding: EdgeInsets.only(right: 1, bottom: 1),
                            child: Center(
                              child: Icon(
                                FontAwesomeIcons.chevronLeft,
                                color: AppTheme.primaryColor,
                                size: 15,
                              ),
                            ),
                          ),
                        ),
                        AnimatedOpacity(
                          opacity: _expandFlag ? 0.0 : 1.0,
                          duration: Duration(milliseconds: 500),
                          child: Center(
                            child: SvgPicture.asset(
                                "assets/images/icons/icCross.svg"),
                          ),
                        )
                      ],
                    )),
              ),
            ),

            // Photo et button
            Container(
              width: double.maxFinite,
              child: Stack(
                children: [
                  Center(
                    child: CircleAvatar(
                        radius: 70,
                        backgroundColor: AppTheme.backgroundColor,
                        backgroundImage: (Config.userAccountInfos == null ||
                                Config.userAccountInfos!.photo == null)
                            ? null
                            : Config.userAccountInfos!.photo!.image,
                        child: Config.userAccountInfos == null
                            ? SvgPicture.asset(
                                "assets/images/icons/icProfile.svg",
                                height: 130)
                            : null),
                  ),
                  Positioned(
                    top: 60.0,
                    right: 43.0,
                    child: FadeTransition(
                        opacity: _animation,
                        child: GestureDetector(
                          onTap: () {
                            if (_animation.value == 1) {
                              _changeProfileContainerExpansion(!_expandFlag);
                            }
                          },
                          child: ChevIcon(
                              assetPath: "assets/images/icons/btChevBleuR.svg"),
                        )),
                  )
                ],
              ),
            ),

            // nom
            SizedBox(
              height: 8.0,
            ),

            Text(
              "${_userAccountInfos.prenom} ${_userAccountInfos.nom}",
              style: TextStyle(
                  fontSize: FontSizeController.of(context).fontSize,
                  color: AppTheme.backgroundColor,
                  fontWeight: FontWeight.w600),
            ),

            SizedBox(
              height: 15.0,
            ),

            Expanded(
                child: SingleChildScrollView(
                    child: Column(
              children: [
                // infos
                Center(
                  child: Container(
                    margin:
                        EdgeInsets.only(bottom: 28.0, left: 20.0, right: 20.0),
                    width: double.maxFinite,
                    decoration: BoxDecoration(
                        color: AppTheme.primaryColorDarker,
                        borderRadius: BorderRadius.circular(20.0)),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 20.0,
                        ),
                        Text(
                          "${allTranslations.text('birth_name')}: ${_userAccountInfos.nomN}",
                          style: TextStyle(
                              fontSize: FontSizeController.of(context).fontSize,
                              color: AppTheme.backgroundColor),
                        ),
                        SizedBox(
                          height: 12.0,
                        ),
                        Text(
                          "${allTranslations.text('born_${_userAccountInfos.genre}')} ${_userAccountInfos.dateN.formatYYYYMMDD2Language()}",
                          style: TextStyle(
                              fontSize: FontSizeController.of(context).fontSize,
                              color: AppTheme.backgroundColor),
                        ),
                        SizedBox(
                          height: 12.0,
                        ),
                        Text(
                          "${allTranslations.text('gender')}: ${_userAccountInfos.genre}",
                          style: TextStyle(
                              fontSize: FontSizeController.of(context).fontSize,
                              color: AppTheme.backgroundColor),
                        ),
                        SizedBox(
                          height: 20.0,
                        )
                      ],
                    ),
                  ),
                ),

                // settings
                Column(
                  children: [
                    Container(
                      padding: EdgeInsets.only(left: 15.0, bottom: 20.0),
                      width: double.maxFinite,
                      child: Text(
                        allTranslations.text('safety').toUpperCase(),
                        textAlign: TextAlign.start,
                        style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.backgroundColor,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    SizedBox(
                      height: 10.0,
                    ),
                    SettingsList(
                      shrinkWrap: true,
                      lightTheme: const SettingsThemeData(
                          settingsListBackground: Colors.white),
                      sections: [
                        SettingsSection(
                          tiles: [
                            SettingsTile(
                                title: Text(
                                    allTranslations.text('changePassword'),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize,
                                        color: AppTheme.primaryColor)),
                                leading: SvgPicture.asset(
                                    "assets/images/icons/icLock.svg"),
                                onPressed: (_) => _onChangePwd()),
                            SettingsTile(
                                title: Text(allTranslations.text("changeEmail"),
                                    style: TextStyle(
                                        fontSize: FontSizeController.of(context)
                                            .fontSize,
                                        color: AppTheme.primaryColor)),
                                leading: SvgPicture.asset(
                                    "assets/images/icons/icMessagerie.svg"),
                                onPressed: (_) => _onChangeEmail()),
                          ],
                        )
                      ],
                    )
                  ],
                )
              ],
            )))
          ],
        ),
      ),
    );
  }

  void _onLeadingTap() {
    // sur tap du bouton leading de l'appbar
    if (_expandFlag) {
      _changeProfileContainerExpansion(!_expandFlag);
    } else {
      Navigator.of(context).pop();
    }
  }

  void _changeProfileContainerExpansion(bool expand) {
    // changement de l'expansion du container
    if (expand) {
      _animationController.animateBack(0);
    } else {
      _animationController.forward();
    }
    setState(() {
      _expandFlag = !_expandFlag;
    });
  }

  void _onChangePwd() async {
    // push vers la vue de modification du password
    String? message = await Navigator.push(context,
        CupertinoPageRoute(builder: (context) => ViewChangePassword()));
    if (message != null) _showFlushbar(true, null, message);
  }

  void _onChangeEmail() async {
    // push vers la vue de modification de l'email
    String? message = await Navigator.push(
        context, CupertinoPageRoute(builder: (context) => ViewChangeEmail()));
    if (message != null) _showFlushbar(true, null, message);
  }

  void _showFlushbar(bool success, String? title, String description) {
    AlertBox(context: context, title: title ?? "", description: description)
        .showFlushbar(warning: !success);
  }
}
