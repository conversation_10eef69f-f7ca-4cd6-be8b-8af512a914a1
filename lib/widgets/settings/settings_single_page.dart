import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';

class SettingsSinglePage extends StatelessWidget {
  final Widget iconWidget;
  final bool isUserProfile;
  final String settingsTitle;
  final Widget child;

  SettingsSinglePage(
      {required this.iconWidget,
      required this.isUserProfile,
      required this.settingsTitle,
      required this.child});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
      child: Stack(
        children: [
          // page princiaple
          Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: AppBar(
              backgroundColor: isUserProfile
                  ? AppTheme.backgroundColor
                  : AppTheme.primaryColor,
              systemOverlayStyle: SystemUiOverlayStyle(
                statusBarBrightness:
                    isUserProfile ? Brightness.light : Brightness.dark,
              ),
              leading: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: SvgPicture.asset(isUserProfile
                    ? "assets/images/icons/btChevBleuL.svg"
                    : "assets/images/icons/btChevBlancL.svg"),
              ),
            ),
            body: Container(
              color: isUserProfile
                  ? AppTheme.primaryColor
                  : AppTheme.backgroundColor,
              padding: EdgeInsets.only(
                top: 70.0,
              ),
              child: Column(
                children: [
                  Container(
                    width: double.maxFinite,
                    padding:
                        EdgeInsets.only(bottom: 20.0, left: 20.0, right: 20.0),
                    decoration: BoxDecoration(
                        color: isUserProfile
                            ? AppTheme.primaryColor
                            : AppTheme.backgroundColor,
                        border: Border(
                            bottom: BorderSide(
                                color: Platform.isIOS
                                    ? Colors.transparent
                                    : AppTheme.dividerColor.withOpacity(0.1)))),
                    child: Text(
                      settingsTitle,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: isUserProfile
                              ? AppTheme.backgroundColor
                              : AppTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                          fontSize: FontSizeController.of(context).fontSize),
                    ),
                  ),
                  Expanded(child: child),
                ],
              ),
            ),
          ),

          // icon
          Padding(
            padding: EdgeInsets.only(top: 45.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    height: 70,
                    width: 70,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppTheme.backgroundColor,
                        boxShadow: [
                          BoxShadow(
                              color: AppTheme.primaryBlurColor,
                              blurRadius: 20.0,
                              spreadRadius: 0,
                              offset: Offset(0, 10))
                        ]),
                    child: Center(
                      child: Container(
                          height: 25.0, width: 25.0, child: iconWidget),
                    ))
              ],
            ),
          ),
        ],
      ),
    );
  }
}
