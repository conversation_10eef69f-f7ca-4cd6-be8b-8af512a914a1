import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/CustomBottomSheet.dart';
import 'package:open_filex/open_filex.dart';

/// Thumbnail d'une image, qui peut être supprimée par l'utilisateur

class ThumbnailWidget extends StatelessWidget {
  final Image? image;
  final File file;
  final bool isAudio;
  final void Function(File)? onRemoval;

  ThumbnailWidget(
      {required this.image,
      required this.file,
      required this.isAudio,
      required this.onRemoval});

  @override
  Widget build(BuildContext context) {
    Widget fileIcon = _initIconWidget(file.path.split(".").last);

    return Padding(
        padding: EdgeInsets.only(right: 10, left: 10, bottom: 10, top: 10),
        child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () => _onTap(context),
            onDoubleTap: () => _openFile(),
            child: isAudio
                ? fileIcon
                : SizedBox(
                    height: 50,
                    width: 50,
                    child: image == null ? fileIcon : image)));
  }

  Widget _initIconWidget(String extension) {
    Widget iconWidget;
    switch (extension) {
      case "pdf":
        {
          iconWidget =
              SvgPicture.asset("assets/images/icons/icFilePdf.svg", height: 50);
        }
        break;
      case 'aac':
        {
          iconWidget = Stack(
            children: [
              SvgPicture.asset("assets/images/icons/icFileEmpty.svg",
                  height: 50),
              Positioned(
                right: 4,
                top: 14,
                child: Icon(
                  Icons.audiotrack,
                  color: AppTheme.primaryColor,
                  size: 30,
                ),
              )
            ],
          );
        }
        break;
      case 'm4a':
        {
          iconWidget = Stack(
            children: [
              SvgPicture.asset("assets/images/icons/icFileEmpty.svg",
                  height: 50),
              Positioned(
                right: 4,
                top: 14,
                child: Icon(
                  Icons.audiotrack,
                  color: AppTheme.primaryColor,
                  size: 30,
                ),
              )
            ],
          );
        }
        break;
      case 'mp3':
        {
          iconWidget = Stack(
            children: [
              SvgPicture.asset("assets/images/icons/icFileEmpty.svg",
                  height: 50),
              Positioned(
                right: 4,
                top: 14,
                child: Icon(
                  Icons.audiotrack,
                  color: AppTheme.primaryColor,
                  size: 30,
                ),
              )
            ],
          );
        }
        break;
      default:
        {
          iconWidget =
              SvgPicture.asset("assets/images/icons/icFile.svg", height: 50);
        }
        break;
    }
    return iconWidget;
  }

  Future<void> _onTap(BuildContext context) async {
    // ouverture bottom sheet
    if (onRemoval == null) {
      // ne peut pas être supprimé
      _openFile();
    } else {
      int? choice = await CustomBottomSheet(
        context: context,
        labels: [
          allTranslations.text('open'),
          allTranslations.text('to_delete')
        ],
      ).show();
      if (choice != null) {
        _onChoice(choice);
      }
    }
  }

  void _onChoice(int i) {
    switch (i) {
      case 0:
        {
          _openFile();
        }
        break;
      case 1:
        {
          _deleteFile();
        }
    }
  }

  void _openFile() {
    // ouverture file
    OpenFilex.open(file.path);
  }

  void _deleteFile() {
    // callback
    onRemoval!(file);
  }
}
