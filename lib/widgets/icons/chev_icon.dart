import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class ChevIcon extends StatelessWidget {
  final String assetPath;

  ChevIcon({required this.assetPath});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 22,
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.transparent,
          boxShadow: [
            BoxShadow(
                color: AppTheme.primaryBlurColor,
                spreadRadius: 0,
                blurRadius: 5.0)
          ]),
      child: SvgPicture.asset(assetPath, fit: BoxFit.cover),
    );
  }
}
