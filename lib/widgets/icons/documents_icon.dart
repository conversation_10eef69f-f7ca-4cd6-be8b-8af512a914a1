import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class DocumentsIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
        child: Stack(children: [
      Padding(
        padding: EdgeInsets.only(
          top: 11.0,
        ),
        child: Container(
          height: 28.1,
          width: 33.1,
          decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              border: Border.all(color: AppTheme.primaryColor, width: 2),
              borderRadius: BorderRadius.all(Radius.circular(2.6))),
        ),
      ),
      Padding(
        padding: EdgeInsets.only(
          left: 5.0,
        ),
        child: SvgPicture.asset("assets/images/icons/icFile.svg",
            height: 31.4, width: 23.2),
      ),
      Padding(
        padding: EdgeInsets.only(
          top: 20.5,
        ),
        child: Container(
          child: Transform(
            alignment: FractionalOffset.bottomLeft,
            transform: Matrix4.skewX(-0.4),
            child: Container(
              height: 18.4,
              width: 36.6,
              decoration: BoxDecoration(
                  color: AppTheme.secondaryColor,
                  border: Border.all(color: AppTheme.primaryColor, width: 2),
                  borderRadius: BorderRadius.all(Radius.circular(2.6))),
            ),
          ),
        ),
      )
    ]));
  }
}
