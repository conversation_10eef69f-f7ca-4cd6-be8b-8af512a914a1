import 'package:flutter/material.dart';
import 'package:theradom/models/enum_status.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class StatusIndicator extends StatelessWidget {
  final Status status;

  StatusIndicator({required this.status});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40.0,
      height: 40.0,
      decoration: BoxDecoration(
          color: _getStatusColor(),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
                color: AppTheme.primaryBlurColor.withOpacity(0.6),
                blurRadius: 10.0,
                spreadRadius: 0.0)
          ],
          border: Border.all(color: AppTheme.primaryColor, width: 2)),
      child: _getStatusIcon(),
    );
  }

  Color _getStatusColor() {
    Color color;
    switch (status) {
      case Status.not_specified:
        {
          color = AppTheme.lightGrey;
        }
        break;
      case Status.taken:
        {
          color = AppTheme.secondaryColor;
        }
        break;
      case Status.not_taken:
        {
          color = AppTheme.tertiaryColor;
        }
        break;
    }
    return color;
  }

  Widget _getStatusIcon() {
    Widget icon;
    switch (status) {
      case Status.not_specified:
        {
          icon = Container();
        }
        break;
      case Status.taken:
        {
          icon = Icon(Icons.check, color: AppTheme.primaryColor);
        }
        break;
      case Status.not_taken:
        {
          icon = Icon(Icons.close, color: AppTheme.primaryColor);
        }
        break;
    }
    return icon;
  }
}
