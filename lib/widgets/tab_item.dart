import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/utils/font_size_controller.dart';

class TabItem extends StatelessWidget {
  final String title;
  final AutoSizeGroup group;

  TabItem({required this.title, required this.group});

  @override
  Widget build(BuildContext context) {
    return Tab(
        child: AutoSizeText(
      title,
      maxLines: 1,
      minFontSize: 12,
      maxFontSize: FontSizeController.of(context).fontSize,
      group: group,
      style: TextStyle(
          fontSize: FontSizeController.of(context).fontSize,
          fontWeight: FontWeight.w600),
    ));
  }
}
