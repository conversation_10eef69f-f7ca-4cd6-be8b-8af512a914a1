import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/blocs/authentication/AuthenticationBlocProvider.dart';
import 'package:theradom/blocs/settings/SettingsBlocProvider.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/views/settings/view_settings_main.dart';
import 'package:theradom/widgets/buttons/svg_button.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Size preferredSize;
  final bool backBtnEnabled;
  final void Function()? callback;

  CustomAppBar(
      {required this.backBtnEnabled,
      this.callback,
      this.preferredSize = const Size.fromHeight(56.0)});

  @override
  Widget build(BuildContext context) {
    return Hero(
        tag: "custom_app_bar",
        child: AppBar(
          elevation: 5,
          backgroundColor: AppTheme.backgroundColor,
          shadowColor: AppTheme.primaryBlurColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(30.0),
            ),
          ),
          centerTitle: true,
          leadingWidth: 45,
          leading: backBtnEnabled
              ? Padding(
                  padding: EdgeInsets.only(
                    left: 20.0,
                  ),
                  child: SvgButton(
                    asset: "assets/images/icons/icBack.svg",
                    onTap: () => Navigator.of(context).pop(),
                  ),
                )
              : null,
          title: AutoSizeText(
            Config.userAccountInfos!.prenom +
                " " +
                Config.userAccountInfos!.nom,
            maxFontSize: FontSizeController.of(context).fontSize,
            maxLines: 1,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: FontSizeController.of(context).fontSize,
                fontWeight: FontWeight.w600,
                fontFamily: "OpenSans",
                color: AppTheme.primaryColor),
          ),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 20.0),
              child: GestureDetector(
                onTap: () => _pushToSettingsScreen(context),
                child: SvgPicture.asset("assets/images/icons/icSettings.svg"),
              ),
            )
          ],
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ));
  }

  void _pushToSettingsScreen(BuildContext context) async {
    await Navigator.push(
        context,
        CupertinoPageRoute(
            builder: (context) => AuthenticationBlocProvider(
                  child: SettingsBlocProvider(child: ViewSettingsGeneral()),
                )));
    if (callback != null) callback!();
  }
}
