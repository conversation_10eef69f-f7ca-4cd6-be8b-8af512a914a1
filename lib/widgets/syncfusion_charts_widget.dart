import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:theradom/models/syncfusion_chart_data.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class SyncfusionChartsWidget extends StatelessWidget {
  final List<SyncfusionChartSeries> chartSeriesList;

  const SyncfusionChartsWidget({
    Key? key,
    required this.chartSeriesList,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (chartSeriesList.isEmpty) {
      return Center(
        child: Text(
          'Aucune donnée à afficher',
          style: TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 16,
          ),
        ),
      );
    }

    // Créer les séries Syncfusion directement
    List<LineSeries<SyncfusionChartData, DateTime>> seriesList =
        _createSyncfusionSeries();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SfCartesianChart(
        title: ChartTitle(
          text: 'Données statistiques',
          textStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        legend: Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 12,
          ),
        ),
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x : point.y',
          textStyle: TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
        zoomPanBehavior: ZoomPanBehavior(
          enablePinching: true,
          enablePanning: true,
          enableDoubleTapZooming: true,
        ),
        primaryXAxis: DateTimeAxis(
          labelStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 10,
          ),
          majorGridLines: MajorGridLines(
            color: AppTheme.primaryColor.withOpacity(0.2),
          ),
          axisLine: AxisLine(
            color: AppTheme.primaryColor.withOpacity(0.5),
          ),
        ),
        primaryYAxis: NumericAxis(
          labelStyle: TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 10,
          ),
          majorGridLines: MajorGridLines(
            color: AppTheme.primaryColor.withOpacity(0.2),
          ),
          axisLine: AxisLine(
            color: AppTheme.primaryColor.withOpacity(0.5),
          ),
        ),
        series: seriesList,
      ),
    );
  }

  List<LineSeries<SyncfusionChartData, DateTime>> _createSyncfusionSeries() {
    List<LineSeries<SyncfusionChartData, DateTime>> seriesList = [];

    for (int i = 0; i < chartSeriesList.length; i++) {
      SyncfusionChartSeries chartSeries = chartSeriesList[i];
      Color lineColor = AppTheme.chartColorList[
          chartSeries.colorIndex % AppTheme.chartColorList.length];

      seriesList.add(
        LineSeries<SyncfusionChartData, DateTime>(
          name: chartSeries.name,
          dataSource: chartSeries.data,
          xValueMapper: (SyncfusionChartData data, _) => data.x,
          yValueMapper: (SyncfusionChartData data, _) => data.y,
          color: lineColor,
          width: 2,
          markerSettings: MarkerSettings(
            isVisible: true,
            shape: DataMarkerType.circle,
            width: 6,
            height: 6,
            color: lineColor,
            borderColor: Colors.white,
            borderWidth: 2,
          ),
          enableTooltip: true,
          animationDuration: 1000,
        ),
      );
    }

    return seriesList;
  }
}
