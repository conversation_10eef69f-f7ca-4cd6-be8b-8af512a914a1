import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/session_details/start_time_session_selector.dart';
import 'package:theradom/widgets/date_selector_widget.dart';
import 'package:theradom/widgets/buttons/dialog_button.dart';

/// contenu du dialog de saisie d'une date et d'une heure

class DateAndTimeSelector extends StatefulWidget {
  final String title;
  final DateTime minimumDate;
  final DateTime maximumDate;
  final DateTime initialDate;
  final TimeOfDay initialTime;

  DateAndTimeSelector(
      {required this.title,
      required this.initialDate,
      required this.initialTime,
      required this.minimumDate,
      required this.maximumDate});

  _DateAndTimeSelectorState createState() => _DateAndTimeSelectorState();
}

class _DateAndTimeSelectorState extends State<DateAndTimeSelector> {
  double _dialogFontSize = 18.0;
  late DateTime _date;
  late TimeOfDay _time;
  final AutoSizeGroup _autoSizeGroup = AutoSizeGroup();

  @override
  void initState() {
    super.initState();
    _date = widget.initialDate;
    _time = widget.initialTime;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // page princiaple
        Scaffold(
          appBar: AppBar(
            toolbarHeight: 100,
            backgroundColor: AppTheme.primaryColor,
            leading: Container(),
            systemOverlayStyle: SystemUiOverlayStyle.light,
          ),
          body: Container(
            padding: EdgeInsets.only(
              top: 80.0,
            ),
            child: Column(
              children: [
                Padding(
                    padding: EdgeInsets.all(21.0),
                    child: Text(
                      widget.title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: _dialogFontSize,
                          fontWeight: FontWeight.w600),
                    )),
                Expanded(
                  child: Padding(
                    padding:
                        EdgeInsets.only(left: 60.0, right: 60.0, bottom: 30.0),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 50.0,
                        ),
                        DateSelectorWidget(
                          dateHint: allTranslations.text('select_date'),
                          locked: false,
                          initialDateTime: _date,
                          minimumDateTime: widget.minimumDate,
                          maximumDateTime: widget.maximumDate,
                          onValidate: (DateTime selectedDate) {
                            _date = selectedDate;
                          },
                        ),
                        SizedBox(
                          height: 20.0,
                        ),
                        StartTimeSessionSelector(
                          startTime: widget.initialTime,
                          locked: false,
                          onValidate: (TimeOfDay selectedTime) {
                            _time = selectedTime;
                          },
                        ),
                        Spacer(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            DialogButton(
                                label: allTranslations.text('cancel'),
                                labelColor: AppTheme.primaryColor,
                                backgroundColor: AppTheme.backgroundColor,
                                onPressed: () => Navigator.of(context).pop(),
                                group: _autoSizeGroup),
                            Spacer(),
                            DialogButton(
                                label: allTranslations.text('to_validate'),
                                labelColor: AppTheme.primaryColor,
                                backgroundColor: AppTheme.secondaryColor,
                                onPressed: () =>
                                    Navigator.of(context).pop([_date, _time]),
                                group: _autoSizeGroup)
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // icon
        Padding(
          padding: EdgeInsets.only(top: 60.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 70.0,
                width: 70.0,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppTheme.backgroundColor,
                    boxShadow: [
                      BoxShadow(
                          color: AppTheme.primaryBlurColor,
                          blurRadius: 20.0,
                          spreadRadius: 0.0,
                          offset: Offset(0, 10))
                    ]),
                child: Center(
                  child: Container(
                    height: 25.0,
                    width: 25.0,
                    child: SvgPicture.asset("assets/images/icons/icClock.svg"),
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
