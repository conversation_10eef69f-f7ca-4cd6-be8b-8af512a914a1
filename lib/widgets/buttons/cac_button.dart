import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/utils/theme/app_theme.dart';

/// ui d'une fake cac

class CacButton extends StatelessWidget {
  final bool checked;

  CacButton({required this.checked});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.all(4.0),
        width: 24.0,
        height: 24.0,
        decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(4.0),
            border: Border.all(
                color: AppTheme.borderColor.withOpacity(0.3), width: 1),
            boxShadow: [
              BoxShadow(
                  spreadRadius: 0.0,
                  blurRadius: 10.0,
                  color: AppTheme.primaryBlurColor.withOpacity(0.3))
            ]),
        child: Center(
          child: Icon(FontAwesomeIcons.check,
              size: 15,
              color: checked ? AppTheme.primaryColor : Colors.transparent),
        ));
  }
}
