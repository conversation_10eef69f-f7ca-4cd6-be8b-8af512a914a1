import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/buttons/fab.dart';
import 'package:theradom/widgets/slider.dart';

class SessionValidationButton extends StatefulWidget {
  final ScrollController scrollController;
  final bool isSession; // false si suivi vaut "6" ou "7", true sinon
  final void Function() onSlideAction;
  final void Function() onPressed;

  SessionValidationButton(
      {required this.scrollController,
      required this.isSession,
      required this.onSlideAction,
      required this.onPressed});

  _SessionValidationButtonState createState() =>
      _SessionValidationButtonState();
}

class _SessionValidationButtonState extends State<SessionValidationButton>
    with SingleTickerProviderStateMixin {
  bool _isSaveBtnActive = true;
  late final Animation<double> _animation;
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);
    _controller = AnimationController(
        upperBound: 0.9,
        lowerBound: 0,
        duration: const Duration(milliseconds: 400),
        vsync: this);
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.fastOutSlowIn,
    );
  }

  @override
  Widget build(BuildContext context) {
    return !widget.isSession
        ? _buildFab()
        : Stack(
            children: [
              Align(
                alignment: Alignment.bottomRight,
                child: AnimatedOpacity(
                    opacity: _isSaveBtnActive ? 1.0 : 0.0,
                    duration: _isSaveBtnActive
                        ? Duration(milliseconds: 400)
                        : Duration(milliseconds: 100),
                    child: _buildFab()),
              ),
              Positioned.fill(
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: SizeTransition(
                    sizeFactor: _animation,
                    axis: Axis.horizontal,
                    axisAlignment: -1,
                    child: Center(
                        // utilise le slider button custom avec Dismissible UniqueKey()
                        child: SliderButton(
                      dismissible: false,
                      dismissThresholds: 0.9,
                      backgroundColor: AppTheme.secondaryColor,
                      baseColor: AppTheme.primaryColor,
                      highlightedColor: AppTheme.backgroundColor,
                      vibrationFlag: true,
                      height: 70,
                      buttonSize: 60,
                      width: 330,
                      action: widget.onSlideAction,
                      icon: Icon(
                        FontAwesomeIcons.check,
                        size: 32,
                      ),
                      label: Text(
                        allTranslations.text('validate_session'),
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: FontSizeController.of(context)
                                .fontSizeMap[FontSize.large],
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.bold),
                      ),
                    )),
                  ),
                ),
              )
            ],
          );
  }

  Widget _buildFab() {
    return Align(
      alignment: Alignment.centerRight,
      child: Fab(
          svgPicture: SvgPicture.asset("assets/images/icons/icSaveCoulBig.svg"),
          onPressed: widget.onPressed),
    );
  }

  void _onScroll() {
    if ((widget.scrollController.position.pixels + 200) >
        (widget.scrollController.position.maxScrollExtent)) {
      if (_isSaveBtnActive) {
        _hideSaveBtnAndShowSlider();
      }
    } else {
      if (!_isSaveBtnActive) {
        _showSaveBtnAndHideSlider();
      }
    }
  }

  void _hideSaveBtnAndShowSlider() {
    _hideSaveBtn();
    _showSlider();
  }

  void _hideSaveBtn() {
    setState(() {
      _isSaveBtnActive = false;
    });
  }

  void _showSlider() {
    _controller.forward();
  }

  void _showSaveBtnAndHideSlider() {
    _showSaveBtn();
    _hideSlider();
  }

  void _showSaveBtn() {
    setState(() {
      _isSaveBtnActive = true;
    });
  }

  void _hideSlider() {
    _controller.reverse();
  }
}
