import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';

/// Floating Action Button custom

class Fab extends StatelessWidget {
  final SvgPicture svgPicture;
  final void Function() onPressed;
  final double _height = 60.0;

  Fab({required this.svgPicture, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 0.0, right: 4.0),
      child: Container(
        height: _height,
        width: _height,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
                color: AppTheme.primaryColor.withOpacity(0.3), width: 0.3)),
        child: FloatingActionButton(
          backgroundColor: AppTheme.backgroundColor,
          heroTag: svgPicture.key,
          child: Container(
              margin: EdgeInsets.all(12.0),
              height: 40.0,
              width: 40.0,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(100.0))),
              child: svgPicture),
          onPressed: () => onPressed(),
        ),
      ),
    );
  }
}
