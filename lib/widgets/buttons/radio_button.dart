import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';

/// fake radio button

class RadioButton extends StatelessWidget {
  final bool picked;

  RadioButton({required this.picked});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.all(4.0),
        width: 24.0,
        height: 24.0,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppTheme.backgroundColor,
            border: Border.all(
                color: picked
                    ? AppTheme.primaryColor
                    : AppTheme.borderColor.withOpacity(0.3),
                width: 2),
            boxShadow: [
              BoxShadow(
                  spreadRadius: 0.0,
                  blurRadius: 10.0,
                  color: AppTheme.primaryBlurColor.withOpacity(0.3))
            ]),
        child: Container(
          decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: picked ? AppTheme.primaryColor : Colors.transparent),
        ));
  }
}
