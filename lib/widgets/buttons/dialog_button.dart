import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

/// Bouton pour les dialogs

class DialogButton extends StatelessWidget {
  final Color backgroundColor;
  final String label;
  final Color labelColor;
  final AutoSizeGroup group;
  final void Function() onPressed;

  DialogButton(
      {required this.backgroundColor,
      required this.label,
      required this.labelColor,
      required this.group,
      required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Container(
          height: 50,
          width: 100000,
          child: Container(
              height: 50,
              child: TextButton(
                onPressed: onPressed,
                style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.resolveWith(
                        (states) => backgroundColor)),
                child: Center(
                  child: AutoSizeText(
                    label,
                    maxLines: 1,
                    maxFontSize: 18.0,
                    group: group,
                    style: TextStyle(
                        color: labelColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 18.0),
                  ),
                ),
              ))),
    );
  }
}
