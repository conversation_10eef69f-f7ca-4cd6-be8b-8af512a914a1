import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class VisibleMenuButton extends StatelessWidget {
  final String title;
  final String iconPath;
  final int nbNotif;
  final void Function() onTap;
  final bool selected;

  VisibleMenuButton(
      {required this.title,
      required this.iconPath,
      required this.nbNotif,
      required this.onTap,
      required this.selected});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Stack(
            children: [
              Container(
                  height: 36.0,
                  width: 36.0,
                  decoration: BoxDecoration(
                      color: selected
                          ? AppTheme.primaryColor
                          : AppTheme.backgroundColor,
                      borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: SvgPicture.asset(
                      iconPath,
                      colorFilter: ColorFilter.mode(
                          selected
                              ? AppTheme.backgroundColor
                              : AppTheme.primaryColor,
                          BlendMode.srcIn),
                    ),
                  )),
              Positioned(
                  top: 0.0,
                  right: 0.0,
                  child: Container(
                      padding: EdgeInsets.all(1),
                      decoration: BoxDecoration(
                          color: nbNotif > 0
                              ? AppTheme.secondaryColor
                              : Colors.transparent,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                                color: nbNotif > 0
                                    ? AppTheme.primaryBlurColor.withOpacity(0.5)
                                    : Colors.transparent,
                                spreadRadius: 0,
                                blurRadius: 10,
                                offset: Offset(0, 0))
                          ]),
                      child: Center(
                        child: Text(
                          nbNotif.toString(),
                          style: TextStyle(
                              color: nbNotif > 0
                                  ? AppTheme.primaryColor
                                  : Colors.transparent,
                              fontSize: 10),
                        ),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 12,
                        minHeight: 12,
                      )))
            ],
          ), // icon
          Padding(
            padding: EdgeInsets.only(top: 2.0),
            child: Text(
              title.toUpperCase(),
              style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                  fontFamily: "OpenSans"),
            ), // tex,
          )
        ],
      ),
    );
  }
}
