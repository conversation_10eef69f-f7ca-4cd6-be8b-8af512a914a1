import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SvgButton extends StatelessWidget {
  final String asset;
  final Color? assetColor;
  final void Function() onTap;

  SvgButton({required this.asset, this.assetColor, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.translucent,
        child: SvgPicture.asset(asset,
            colorFilter: assetColor == null
                ? null
                : ColorFilter.mode(assetColor ?? Theme.of(context).primaryColor,
                    BlendMode.srcIn),
            height: 25));
  }
}
