import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/widgets/spinning_child.dart';

/// Widget pour sélectionner une option d'un suivi

class MonitoringCardButton extends StatelessWidget {
  final String title;
  final Widget iconWidget;
  final AutoSizeGroup group;
  final void Function() onTap;
  final bool spinIcon;

  MonitoringCardButton(
      {required this.title,
      required this.iconWidget,
      required this.group,
      required this.onTap,
      this.spinIcon = false});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(25)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            spinIcon
                ? SpinningChild(
                    height: 80,
                    width: 80,
                    child: Container(
                        height: 80,
                        width: 80,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppTheme.backgroundColor,
                            boxShadow: [
                              BoxShadow(
                                // shadow bottom
                                color: AppTheme.primaryBlurColor,
                                spreadRadius: 0,
                                blurRadius: 20,
                                offset:
                                    Offset(0, 10), // changes position of shadow
                              ),
                            ]),
                        child: IconButton(icon: iconWidget, onPressed: onTap)))
                : Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppTheme.backgroundColor,
                        boxShadow: [
                          BoxShadow(
                            // shadow bottom
                            color: AppTheme.primaryBlurColor,
                            spreadRadius: 0,
                            blurRadius: 20,
                            offset: Offset(0, 10), // changes position of shadow
                          ),
                        ]),
                    child: IconButton(icon: iconWidget, onPressed: onTap)),
            Padding(
              padding: EdgeInsets.only(left: 10.0, right: 10.0, bottom: 10.0),
              child: ListBody(
                children: [
                  AutoSizeText(
                    title,
                    maxLines: 2,
                    group: group,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: FontSizeController.of(context).fontSize,
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'OpenSans'),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
