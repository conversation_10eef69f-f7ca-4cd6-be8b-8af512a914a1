import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class HiddenMenuButton extends StatelessWidget {
  final String title;
  final String iconPath;
  final int nbNotif;
  final bool selected;
  final void Function() onTap;

  HiddenMenuButton(
      {required this.title,
      required this.nbNotif,
      required this.iconPath,
      required this.selected,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: onTap,
              child: Container(
                height: 72,
                width: 72,
                decoration: BoxDecoration(
                    color: selected
                        ? AppTheme.primaryColor
                        : AppTheme.backgroundColor,
                    borderRadius: BorderRadius.all(Radius.circular(16.0)),
                    boxShadow: [
                      BoxShadow(
                          color: AppTheme.primaryBlurColor.withOpacity(0.5),
                          spreadRadius: 0,
                          blurRadius: 10,
                          offset: Offset(0, 0))
                    ]),
                child: Center(
                    child: SvgPicture.asset(iconPath,
                        colorFilter: ColorFilter.mode(
                            selected
                                ? AppTheme.backgroundColor
                                : AppTheme.primaryColor,
                            BlendMode.srcIn))),
              ),
            ),
            Positioned(
              top: 10.0,
              right: 10.0,
              child: Container(
                  padding: EdgeInsets.all(1),
                  decoration: BoxDecoration(
                      color: nbNotif > 0
                          ? AppTheme.secondaryColor
                          : Colors.transparent,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                            color: nbNotif > 0
                                ? AppTheme.primaryBlurColor.withOpacity(0.5)
                                : Colors.transparent,
                            spreadRadius: 0,
                            blurRadius: 10,
                            offset: Offset(0, 0))
                      ]),
                  child: Center(
                    child: Text(
                      nbNotif.toString(),
                      style: TextStyle(
                          color: nbNotif > 0
                              ? AppTheme.primaryColor
                              : Colors.transparent,
                          fontSize: 10),
                    ),
                  ),
                  constraints: BoxConstraints(
                    minWidth: 12,
                    minHeight: 12,
                  )),
            )
          ],
        ),
        Padding(
          padding: EdgeInsets.only(top: 10.0),
          child: Text(
            title.toUpperCase(),
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontFamily: "OpenSans",
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
                fontSize: 14.0),
          ),
        )
      ],
    );
  }
}
