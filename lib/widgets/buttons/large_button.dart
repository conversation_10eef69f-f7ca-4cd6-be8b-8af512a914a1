import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/font_size_controller.dart';

class LargeButton extends StatelessWidget {
  final void Function() onTap;
  final String title;
  final Color backgroundColor;
  final Color titleColor;
  final bool showShadow;

  LargeButton(
      {required this.onTap,
      required this.title,
      required this.backgroundColor,
      required this.titleColor,
      this.showShadow = false});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 64,
        padding: EdgeInsets.symmetric(horizontal: 10.0),
        margin: EdgeInsets.only(top: 10.0, left: 20.0, right: 20.0),
        decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(16.0)),
            boxShadow: [
              BoxShadow(
                  color: showShadow
                      ? AppTheme.primaryBlurColor
                      : Colors.transparent,
                  blurRadius: showShadow ? 20.0 : 0.0,
                  spreadRadius: 0)
            ]),
        child: Center(
          child: AutoSizeText(
            title,
            maxLines: 1,
            style: TextStyle(
                color: titleColor,
                fontSize: FontSizeController.of(context).fontSize,
                fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }
}
