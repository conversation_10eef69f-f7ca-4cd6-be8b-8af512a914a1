import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class CustomCloseButton extends StatelessWidget {
  final void Function() onTap;
  final bool hasBackgroundColor;

  CustomCloseButton({required this.onTap, required this.hasBackgroundColor});

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 30,
        width: 30,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: hasBackgroundColor
                ? AppTheme.backgroundColor
                : AppTheme.primaryColor,
            boxShadow: [
              BoxShadow(
                  blurRadius: 20,
                  spreadRadius: 0,
                  color: AppTheme.primaryBlurColor)
            ]),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(6.0),
            child: SvgPicture.asset("assets/images/icons/icCross.svg",
                colorFilter: ColorFilter.mode(
                    hasBackgroundColor
                        ? AppTheme.primaryColor
                        : AppTheme.backgroundColor,
                    BlendMode.srcIn)),
          ),
        ));
  }
}
