name: mp_chart
description: A Flutter chart package just like AndroidMPChart, apply easy to use chart for Flutter.
version: 1.3.0
homepage: https://github.com/SunPointed/MPFlutterChart

environment:
  sdk: '>=2.19.0 <4.0.0'

dependencies:
  intl: ^0.18.0
  path_provider: ^2.0.15
  screenshot: ^2.1.0
  path_drawing: ^1.0.1
#  image_gallery_saver: ^1.7.1
  optimized_gesture_detector:
    git:
      url: https://github.com/absar/OptimizedGestureDetector.git
      #ref: null-safety
      path: ./
  vector_math: ^2.1.4
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter



# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

# To add assets to your package, add an assets section, like this:
# assets:
#  - images/a_dot_burr.jpeg
#  - images/a_dot_ham.jpeg
#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware.

# To add custom fonts to your package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages
