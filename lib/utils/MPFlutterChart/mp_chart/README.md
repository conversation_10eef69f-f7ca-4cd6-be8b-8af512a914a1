# MPFlutterChart

flutter charts just like [**MPAndroidChart**](https://github.com/PhilJay/MPAndroidChart)

The minimum version currently supported is 1.22.0. If you use flutter with lower version, checkout and use 'localPosition-support' branch, this branch support for flutter version which XXXDetails and PointerXXXEvent not support wiget local postion

Thanks for [**MPAndroidChart**](https://github.com/PhilJay/MPAndroidChart), when translate this library to flutter I learned a lot about matrix use in animation.

## todos
**1.potential bugs fix**

## More Examples
**LineCharts**
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/basic.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/multiple.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/dual_axis.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/invert_axis.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/cubic.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/colorful.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/performance.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/line_charts/filled.png"  width="240" height="480">
</div>
<br/><br/>

**BarCharts**
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/basic.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/basic2.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/multiple.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/horizontal.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/stacked.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/negative.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/stacked2.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/bar_charts/sine.png"  width="240" height="480">
</div>
<br/><br/>

**PieCharts**
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/pie_charts/basic.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/pie_charts/value_lines.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/pie_charts/half_pie.png"  width="240" height="480">
</div>
<br/><br/>

**OtherCharts**
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/other_charts/combined.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/other_charts/scatter.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/other_charts/bubble.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/other_charts/candle.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/other_charts/radar.png"  width="240" height="480">
</div>
<br/><br/>

**ScrollingCharts**
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/scrolling_charts/multiple.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/scrolling_charts/view_pager.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/scrolling_charts/tall.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/scrolling_charts/many.png"  width="240" height="480">
</div>
<br/><br/>

**EvenMoreCharts**
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/even_more_charts/dynamic.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/even_more_charts/realtime.png"  width="240" height="480">
</div>
<br/><br/>
<div align=center>
    <img src="https://raw.githubusercontent.com/SunPointed/MPFlutterChart/master/mp_chart/img/even_more_charts/hourly.png"  width="240" height="480">
</div>
<br/><br/>

<h1 id="license">License </h1>

Copyright 2019 SunPointed

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

> http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.