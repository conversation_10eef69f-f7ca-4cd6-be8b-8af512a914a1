import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/SharedPreferences.dart';

class Translations {
  Locale? _locale;
  late Map<dynamic, dynamic> _localizedValues;

  // Retourne la liste des langages supportés par l'application comme une liste de Locale
  Iterable<Locale> supportedLocales() =>
      Config.langageMap.keys.map<Locale>((lang) => Locale(lang, ''));

  // Retourne la traduction qui correspond à la clé key
  String text(String key,
      [Map<String, String> args = const {}, bool forceLowerCase = false]) {
    // Retourne le texte voulu
    // args en paramètres pour les valeurs dynamiques
    String res = (_localizedValues[key] == null)
        ? '** $key not found'
        : _localizedValues[key];
    args.forEach((key, value) {
      res = res.replaceAll(RegExp(r'\${' + key + '}'), value);
    });
    String strToReturn = "${res[0].toUpperCase()}${res.substring(1)}";
    return forceLowerCase ? strToReturn.toLowerCase() : strToReturn;
  }

  // Retourne le langage courant
  String get currentLanguage => _locale == null ? '' : _locale!.languageCode;

  // Retourne l'objet Locale courant
  Locale? get locale => _locale;

  // Initilisation du langage au lancement de l'application
  Future<void> init() async {
    if (_locale == null) {
      await setNewLanguage();
    }
    return null;
  }

  Future<String> getPreferredLanguage() async {
    // fetch le langage enregistré par l'utilisateur
    return _getApplicationSavedInformation();
  }

  Future<void> setPreferredLanguage(String lang) async {
    // sauvegadre le langage enregistré par l'utiilisateur
    _setApplicationSavedInformation(lang);
  }

  // Routine pour le changement de langue dans l'application
  Future<void> setNewLanguage([String? code]) async {
    String? language = code;
    if (language == null) {
      language = await getPreferredLanguage();
    }

    // Set the locale
    if (language == "") {
      /// language par default
      String defaultLocale =
          Platform.localeName; // Returns locale string in the form 'en_US'
      if (defaultLocale.contains("_"))
        defaultLocale = defaultLocale.split("_")[0];
      if (Config.langageMap.keys.contains(defaultLocale)) {
        language =
            "fr"; // TODO: utiliser 'defaultLocale' le jour où plusieurs langues sont diponibles
      } else {
        language = Config.langageMap.keys.first;
      }
    }
    _locale = Locale(language!, "");

    // maj du LocalType
    switch (language) {
      case "en":
        {
          //_localType = LocaleType.en;
          _locale = Locale("en");
        }
        break;
      case "fr":
        {
          //_localType = LocaleType.fr;
          _locale = Locale("fr");
        }
        break;
      case "ita":
        {
          //_localType = LocaleType.it;
          _locale = Locale("it");
        }
        break;
    }

    // chargement du fichier de traduction correspondant
    String jsonContent = await rootBundle
        .loadString("assets/i18n/${_locale!.languageCode}.json");
    _localizedValues = json.decode(jsonContent);

    // on sauvegarde ce langage pour le proposer par défaut au lancement de l'application
    await setPreferredLanguage(language);

    return null;
  }

  Future<String> _getApplicationSavedInformation() async {
    // fetch langage sauvé
    String? savedInfo =
        await SharedPreferencesUtil.instance.getString(Config.prefLanguageCode);
    return savedInfo ?? '';
  }

  Future<void> _setApplicationSavedInformation(String value) async {
    // sauver le langage choisi par l'utilisateur
    await SharedPreferencesUtil.instance
        .setString(Config.prefLanguageCode, value);
  }

  // Singleton Factory
  static final Translations _translations = Translations._internal();
  factory Translations() {
    return _translations;
  }
  Translations._internal();
}

Translations allTranslations = Translations();
