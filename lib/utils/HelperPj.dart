import 'dart:convert';
import 'dart:io';

import 'package:theradom/config.dart';
import 'package:flutter/services.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

class HelperPj {
  static Future<void> deleteTempFiles() async {
    // supprimer les fichiers du directory

    String tempDirPath = (await getTemporaryDirectory()).path;
    final directory = Directory(tempDirPath);

    try {
      directory.listSync(recursive: true).forEach((file) {
        file.delete(recursive: true);
      });
    } catch (error) {
      // print("ERREUR: $error");
    }
  }

  static Future<String> saveFileInTempDirectory(
      Uint8List uint8list, String fileName) async {
    String path = (await getTemporaryDirectory()).path;
    File file = File("$path/" + fileName);
    await file.writeAsBytes(uint8list);
    return file.path;
  }

  static Future<void> openBase64File(String base64File, String name) async {
    Uint8List pjBytes = base64.decode(base64File);
    String path = await HelperPj.saveFileInTempDirectory(pjBytes, name);
    try {
      OpenFilex.open(path);
    } catch (e) {
      AlertBox(
              context: Config.appKey.currentContext!,
              title: allTranslations.text('error'),
              description: allTranslations.text("cant_charge_file"))
          .showFlushbar(warning: true);
    }
  }

  static Future<String> copyAssetToTempDirectory(String assetPath) async {
    String fileName = assetPath.split("/").last;
    Directory tempDir = await getTemporaryDirectory();
    String tempPath = tempDir.path;
    File tempFile = File('$tempPath/$fileName');
    ByteData bd = await rootBundle.load(assetPath);
    await tempFile.writeAsBytes(bd.buffer.asUint8List(), flush: true);
    return tempFile.path;
  }

  static Future<File> createCsvFile(String content, String fileName) async {
    Directory tempDir = await getTemporaryDirectory();
    String tempPath = tempDir.path;
    File tempFile = File('$tempPath/$fileName');
    await tempFile.writeAsString(content);
    return tempFile;
  }
}
