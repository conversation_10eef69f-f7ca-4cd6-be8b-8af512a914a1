import 'package:flutter/material.dart';

class BottomBarController extends ChangeNotifier {
  BottomBarController({
    required TickerProvider vsync,
    this.dragLength = 100,
    this.snap = false,
  }) : _animationController = AnimationController(
          vsync: vsync,
          duration: Duration(milliseconds: 500),
        );

  final AnimationController _animationController;
  final double dragLength;
  final bool snap;

  Animation<double> get animation => _animationController.view;
  bool get isOpen => _animationController.value == 1.0;
  bool get isClosed => _animationController.value == 0.0;

  void open() {
    _animationController.forward();
  }

  void close() {
    _animationController.reverse();
  }

  void swap() {
    if (isOpen) {
      close();
    } else {
      open();
    }
  }

  void onDrag(DragUpdateDetails details) {
    _animationController.value += details.delta.dy / dragLength;
  }

  void onDragEnd(DragEndDetails details) {
    if (snap) {
      if (_animationController.value > 0.5) {
        open();
      } else {
        close();
      }
    } else {
      if (details.velocity.pixelsPerSecond.dy > 0) {
        open();
      } else {
        close();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
