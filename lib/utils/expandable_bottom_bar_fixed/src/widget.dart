import 'package:flutter/material.dart';
import 'bottom_bar_controller.dart';

class BottomExpandableAppBar extends StatefulWidget {
  const BottomExpandableAppBar({
    Key? key,
    required this.controller,
    required this.expandedBody,
    required this.bottomAppBarBody,
    this.expandedHeight = 300,
    this.appBarHeight = 56,
    this.horizontalMargin = 16,
    this.bottomOffset = 10,
    this.shape,
    this.expandedBackColor,
    this.expandedDecoration,
    this.bottomAppBarColor,
    this.appBarDecoration,
  }) : super(key: key);

  final BottomBarController controller;
  final Widget expandedBody;
  final Widget bottomAppBarBody;
  final double expandedHeight;
  final double appBarHeight;
  final double horizontalMargin;
  final double bottomOffset;
  final NotchedShape? shape;
  final Color? expandedBackColor;
  final Decoration? expandedDecoration;
  final Color? bottomAppBarColor;
  final Decoration? appBarDecoration;

  @override
  _BottomExpandableAppBarState createState() => _BottomExpandableAppBarState();
}

class _BottomExpandableAppBarState extends State<BottomExpandableAppBar> {
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller.animation,
      builder: (context, child) {
        final double animationValue = widget.controller.animation.value;
        final double expandedHeight = widget.expandedHeight * animationValue;
        
        return Container(
          height: widget.appBarHeight + expandedHeight,
          child: Stack(
            children: [
              // Expanded body
              if (animationValue > 0)
                Positioned(
                  left: widget.horizontalMargin,
                  right: widget.horizontalMargin,
                  top: 0,
                  height: expandedHeight,
                  child: Container(
                    decoration: widget.expandedDecoration ??
                        BoxDecoration(
                          color: widget.expandedBackColor ?? 
                                 Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(16),
                          ),
                        ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(16),
                      ),
                      child: Opacity(
                        opacity: animationValue,
                        child: widget.expandedBody,
                      ),
                    ),
                  ),
                ),
              
              // Bottom app bar
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                height: widget.appBarHeight,
                child: Container(
                  decoration: widget.appBarDecoration ??
                      BoxDecoration(
                        color: widget.bottomAppBarColor ?? 
                               Theme.of(context).colorScheme.surface,
                      ),
                  child: widget.bottomAppBarBody,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
