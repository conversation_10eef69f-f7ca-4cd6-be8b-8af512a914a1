import 'package:theradom/config.dart';
import 'package:theradom/utils/Translations.dart';

class MonitoringUtils {
  static final Map<String, String> _monitoringIds = {
    "1": allTranslations.text('monitoring_type_1'),
    "2": allTranslations.text('monitoring_type_2'),
    "3": allTranslations.text('monitoring_type_3'),
    "4": allTranslations.text('monitoring_type_4'),
    "5": allTranslations.text('monitoring_type_5'),
    "6": allTranslations.text('monitoring_type_6'),
    "7": allTranslations.text('monitoring_type_7')
  };

  static String getNumTypeLabel(String numType) {
    // tranforme le numType
    String numTypeTxt;
    switch (numType) {
      case "1":
        {
          numTypeTxt = allTranslations.text("before_dialysis_session");
        }
        break;
      case "2":
        {
          numTypeTxt = allTranslations.text("during_dialysis_session");
        }
        break;
      case "3":
        {
          numTypeTxt = allTranslations.text("after_dialysis_session");
        }
        break;
      default:
        {
          numTypeTxt = allTranslations.text("not_in_dialysis_session");
        }
        break;
    }
    return numTypeTxt;
  }

  static String getMonitoringTitle(String id) {
    // retourne le titre du type du suivi
    return _monitoringIds[id] ?? "Unknown";
  }

  static Map<String, String> getMonitoringTypesFromStatut(String statut) {
    List<String> array = statut.split("|");
    Map<String, String> map = Map<String, String>.from(_monitoringIds);
    array.asMap().forEach((index, element) {
      if (element != "1") {
        map.remove('${index + 1}');
      }
    });
    return map;
  }

  static bool isMonitoringSession(String monitoringType) {
    int entier = int.parse(monitoringType);
    if (entier >= 6) {
      return false;
    } else {
      return true;
    }
  }

  static String getCorrespondingTable(String monitoring) {
    String table;
    switch (monitoring) {
      case "6":
        {
          //nephro
          table = Config.tableNephrologyConsultation;
        }
        break;
      case "7":
        {
          // post greffe
          table = Config.tablePostTransplantConsultation;
        }
        break;
      default:
        {
          table = Config.tableDialysisSession;
        }
        break;
    }
    return table;
  }
}
