import 'package:theradom/utils/regexp_utils.dart';
import 'package:theradom/utils/Translations.dart';

class TextFieldValidators {
  static final TextFieldValidators instance = TextFieldValidators._internal();

  TextFieldValidators._internal();

  String? forDecimalNumber(String? value) {
    // validator pour les champs Dose et Nombre
    if (value == null) {
      return _getNotSpecifiedMessage();
    } else {
      if ((value == ".") || (value == ",") || (value == "-")) {
        return _getFormatNotValidMessage();
      } else {
        return null;
      }
    }
  }

  String? forInteger(String? value) {
    // validator pour un nombre entier
    if (value == null) {
      return _getNotSpecifiedMessage();
    } else {
      if (value == "-") {
        return _getFormatNotValidMessage();
      } else {
        return null;
      }
    }
  }

  String? forAlphanumericValue(String? value) {
    // validator pour un alphanumerique
    if (value == null) {
      return _getNotSpecifiedMessage();
    } else {
      if (!RegexpUtils.alphanumeric.hasMatch(value)) {
        return _getFormatNotValidMessage();
      } else {
        return null;
      }
    }
  }

  String? forText(String? value) {
    // validator pour une zone de texte
    if (value == null) {
      return _getFormatNotValidMessage();
    } else {
      String correctedvalue = value.replaceAll(" ", "");
      if (correctedvalue.isEmpty) {
        return _getNotSpecifiedMessage();
      } else {
        return null;
      }
    }
  }

  String? forSpecialField1(String? value) {
    // validator pour une tension XXX
    if (value == null) {
      return _getNotSpecifiedMessage();
    } else {
      if (value.length != 3) {
        return _getFormatNotValidMessage();
      } else {
        return null;
      }
    }
  }

  String? forSpecialField2(String? value) {
    // validator pour une tension XXX/XXX
    if (value == null) {
      return _getNotSpecifiedMessage();
    } else {
      if (((value.length < 6) || (value.length > 7)) ||
          (!value.contains("/"))) {
        return _getFormatNotValidMessage();
      } else {
        return null;
      }
    }
  }

  String _getNotSpecifiedMessage() {
    return allTranslations.text('not_specified');
  }

  String _getFormatNotValidMessage() {
    return allTranslations.text('format_not_valid');
  }
}
