import 'package:theradom/utils/Translations.dart';

/// Classe utilitaire pour la gestion des messages d'erreur.
///
/// Fournit des méthodes pour extraire et formater les messages d'erreur
/// à partir du format standard "code|message".
class HelperMessage {
  /// Récupère le message d'erreur traduit à partir d'un message au format "code|message".
  ///
  /// Si le message contient un séparateur '|', extrait le code d'erreur et retourne
  /// la traduction correspondante avec le préfixe '_Error4D_'.
  /// Sinon, retourne le message original.
  ///
  /// Exemple:
  /// ```dart
  /// String msg = HelperMessage.getErrorMessage("060|Invalid key");
  /// // Retourne la traduction de '_Error4D_060'
  /// ```
  ///
  /// [message] Le message d'erreur à traiter
  /// [return] Le message d'erreur traduit ou le message original
  static String getErrorMessage(String message) {
    if (message.contains('|')) {
      String errorId = message.split("|").first;
      return allTranslations.text('_Error4D_$errorId');
    }
    return message;
  }

  /// Extrait le code d'erreur d'un message au format "code|message".
  ///
  /// Si le message contient un séparateur '|', retourne la partie avant le séparateur.
  /// Sinon, retourne une chaîne vide.
  ///
  /// Exemple:
  /// ```dart
  /// String code = HelperMessage.getCodeFromMessage("060|Invalid key");
  /// // Retourne "060"
  /// ```
  ///
  /// [message] Le message d'erreur à traiter
  /// [return] Le code d'erreur ou une chaîne vide
  static String getCodeFromMessage(String message) {
    if (message.contains('|')) {
      return message.split("|").first;
    }
    return message = "";
  }
}
