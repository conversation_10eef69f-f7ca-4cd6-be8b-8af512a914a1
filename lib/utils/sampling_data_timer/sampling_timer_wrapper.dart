import 'dart:async';

import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/SamplingTimerInfos.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/send_notification_repository.dart';
import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/sampling_data_timer/sampling_timer_stream.dart';
import 'package:theradom/utils/Translations.dart';
// import 'package:vibration/vibration.dart'; // Temporarily commented out

class SamplingTimerWrapper extends StatefulWidget {
  final Widget child;

  SamplingTimerWrapper({required this.child});

  @override
  _SamplingTimerWrapperState createState() => _SamplingTimerWrapperState();
}

class _SamplingTimerWrapperState extends State<SamplingTimerWrapper> {
  SamplingTimerStream _samplingTimerStream = SamplingTimerStream.instance;

  final String _prefSamplingReminderValue = Config.prefSamplingReminderValue;
  final SendNotificationRepository _sendNotificationRepository =
      SendNotificationRepository();

  late Timer _timer;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<dynamic>>(
        initialData: null,
        stream: _samplingTimerStream.infosStream,
        builder: (BuildContext context, AsyncSnapshot<List<dynamic>> snapshot) {
          // snapshot.data = [int a, int b]
          // a -> action sur le timer, -2 -> activation timer, -1 -> arret du timer
          // b -> objet SamplingTimerInfos

          if (snapshot.data != null) {
            // check if this instance is the current route or else message will be displayed on all instances

            SamplingTimerInfos samplingTimerInfos = snapshot.data![1];
            int action = snapshot.data![0];

            WidgetsBinding.instance.addPostFrameCallback((_) async {
              switch (action) {
                case -2:
                  {
                    //activation du timer
                    await _initializeTimer(samplingTimerInfos);
                  }
                  break;
                case -1:
                  {
                    // arret du timer
                    _timer.cancel();
                  }
                  break;
              }
            });
            // adding a null stops the previous message from being displayed again
            //SamplingTimerStream.instance.changeDuration(null);
          }
          return widget.child;
        });
  }

  Future<void> _initializeTimer(SamplingTimerInfos samplingTimerInfos) async {
    if (samplingTimerInfos.alertDate.isBefore(DateTime.now())) {
      await _deleteStorageElementAndShowNotification(samplingTimerInfos);
    } else {
      Duration difference =
          samplingTimerInfos.alertDate.difference(DateTime.now());
      await _createStorageElementAndVibrate(samplingTimerInfos);
      _startTimerWithDuration(difference, samplingTimerInfos);
    }
  }

  Future<void> _createStorageElementAndVibrate(
      SamplingTimerInfos samplingTimerInfos) async {
    await SharedPreferencesUtil.instance.setString(_prefSamplingReminderValue,
        samplingTimerInfosToJson(samplingTimerInfos));
    // Vibration.vibrate(duration: 1); // Temporarily commented out
  }

  void _startTimerWithDuration(
      Duration duration, SamplingTimerInfos samplingTimerInfos) {
    Config.samplingTimerInfos = samplingTimerInfos;
    _timer = Timer.periodic(Duration(minutes: duration.inMinutes),
        (_) async => await _onTimerEnds(samplingTimerInfos));
  }

  Future<void> _onTimerEnds(SamplingTimerInfos samplingTimerInfos) async {
    _samplingTimerStream.addUIevent(samplingTimerInfos);
    _timer.cancel();
    await _deleteStorageElementAndShowNotification(samplingTimerInfos);
  }

  Future<void> _deleteStorageElementAndShowNotification(
      SamplingTimerInfos samplingTimerInfos) async {
    await SharedPreferencesUtil.instance.remove(_prefSamplingReminderValue);
    Config.samplingTimerInfos = null;
    _showFlushbarAndVibrate(samplingTimerInfos);
  }

  Future<void> _showFlushbarAndVibrate(
      SamplingTimerInfos samplingTimerInfos) async {
    // Vibration.vibrate(duration: 1); // Temporarily commented out
    try {
      await _sendNotificationRepository.pushNotification(
          allTranslations.text('it_s_time'),
          allTranslations.text('sampling_reminder_msg'));
    } on WebServiceCallException catch (_) {}
  }
}
