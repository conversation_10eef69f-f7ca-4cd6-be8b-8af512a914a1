import 'package:theradom/models/SamplingTimerInfos.dart';
import 'package:rxdart/rxdart.dart';

/// Stream pour la valeur de la durée avant reprise de l'échantillonage manuel d'une séance

class SamplingTimerStream {
  SamplingTimerStream._internal();

  static final SamplingTimerStream _instance = SamplingTimerStream._internal();

  static SamplingTimerStream get instance {
    return _instance;
  }

  final _infos = BehaviorSubject<List<dynamic>>();

  Stream<List<dynamic>> get infosStream => _infos.stream;

  void addUIevent(SamplingTimerInfos samplingTimerInfos) {
    _infos.add([-3, samplingTimerInfos]);
  }

  void start(SamplingTimerInfos samplingTimerInfos) {
    // ajoute au stream la valeur pour démarrer le timer
    _infos.add([-2, samplingTimerInfos]);
  }
}
