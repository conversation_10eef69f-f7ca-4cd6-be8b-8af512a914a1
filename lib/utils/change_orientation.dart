import 'package:flutter/services.dart';

class OrientationSetter {
  static final OrientationSetter change = OrientationSetter._internal();

  OrientationSetter._internal();

  Future<bool> toPortrait() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    return false;
  }

  Future<bool> toLandscape() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    return false;
  }
}
