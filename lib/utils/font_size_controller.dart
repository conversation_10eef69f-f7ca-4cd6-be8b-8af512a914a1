import 'package:flutter/material.dart';
import 'package:theradom/utils/Translations.dart';

enum FontSize { small, medium, large }

class FontSizeController extends ChangeNotifier {
  late double _fontSize;
  final Map<FontSize, double> fontSizeMap = {
    FontSize.small: 16.0,
    FontSize.medium: 20.0,
    FontSize.large: 24.0
  };
  final List<String> fontSizeStrList = [
    allTranslations.text('small'),
    allTranslations.text('medium'),
    allTranslations.text('large')
  ];

  // getters
  double get fontSize => _fontSize;
  FontSize get fontSizeObj {
    int index = fontSizeMap.values.toList().indexOf(_fontSize);
    return fontSizeMap.keys.toList()[index];
  }

  String get fontSizeInfoStr {
    String str;
    switch (fontSizeObj) {
      case FontSize.small:
        {
          str = allTranslations.text("small");
        }
        break;
      case FontSize.medium:
        {
          str = allTranslations.text("medium");
        }
        break;
      case FontSize.large:
        {
          str = allTranslations.text("large");
        }
        break;
    }
    return str;
  }

  void init() {
    // initialisation
    double taillePolice = fontSizeMap[FontSize.medium]!;
    var greater = fontSizeMap.values
        .where((value) => value >= taillePolice)
        .toList()
      ..sort(); // List des valeurs supérieures ou égales à taillePolice
    _fontSize = greater.first;
  }

  void setFontSize(FontSize value) {
    _fontSize = fontSizeMap[value]!;
    notifyListeners();
  }

  static FontSizeController of(BuildContext context) {
    // get le controller depuis n'importe quelle écran de l'appli
    final provider = context
        .dependOnInheritedWidgetOfExactType<FontSizeControllerProvider>();
    return provider!.controller;
  }
}

// fourni le controller à tous les écrans de l'appli
class FontSizeControllerProvider extends InheritedWidget {
  const FontSizeControllerProvider(
      {required this.controller, required Widget child})
      : super(child: child);

  final FontSizeController controller;

  @override
  bool updateShouldNotify(FontSizeControllerProvider old) =>
      controller != old.controller;
}
