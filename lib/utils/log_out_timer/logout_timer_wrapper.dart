import 'dart:async';
import 'package:flutter/material.dart';
import 'package:theradom/blocs/authentication/BlocAuthentication.dart';
import 'package:theradom/blocs/authentication/EventAuthentication.dart';
import 'package:theradom/config.dart';

import 'package:theradom/utils/SharedPreferences.dart';
import 'package:theradom/utils/log_out_timer/logout_timer_stream.dart';

class LogOutTimerWrapper extends StatefulWidget {
  final Widget child;
  final BlocAuthentication blocAuthentication;

  LogOutTimerWrapper({required this.blocAuthentication, required this.child});

  @override
  _LogOutTimerWrapperState createState() => _LogOutTimerWrapperState();
}

class _LogOutTimerWrapperState extends State<LogOutTimerWrapper> {
  LogOutTimerStream _logOutTimerStream = LogOutTimerStream.instance;

  Timer? _timer;
  late int _timerDuration;

  @override
  void initState() {
    super.initState();
    _timerDuration = Config.durationBeforeLogout;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _handleUserInteraction,
        onVerticalDragDown: (_) => _handleUserInteraction(),
        onHorizontalDragEnd: (_) => _handleUserInteraction(),

        // ... from here it's just your normal app,
        // Remember that any GestureDetector within your app must have
        //   HitTestBehavior.translucent
        child: StreamBuilder<int?>(
            initialData: null,
            stream: _logOutTimerStream.durationStream,
            builder: (BuildContext context, AsyncSnapshot<int?> snapshot) {
              int? duration = snapshot.data; // -1 delog -2 log
              if (duration != null) {
                // check if this instance is the current route or else message will be displayed on all instances
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  switch (duration) {
                    case -1:
                      {
                        // stop
                        _timer?.cancel();
                      }
                      break;
                    case -2:
                      {
                        // start
                        _initializeTimer();
                      }
                      break;
                    default:
                      {
                        _timer?.cancel();
                        Config.durationBeforeLogout = duration;
                        await SharedPreferencesUtil.instance
                            .setInt(Config.prefDurBeforeLogOut, duration);
                        setState(() {
                          _timerDuration = duration;
                        });

                        int? noDuration = LogOutTimerStream.mapTimeLimitInt()[
                            TimeLimit.never];
                        if (duration != noDuration) {
                          // duration à fixer
                          _initializeTimer();
                        }
                      }
                  }
                });
                // adding a null stops the previous message from being displayed again
                LogOutTimerStream.instance.changeDuration(null);
              }
              return widget.child;
            }));
  }

  void _initializeTimer() {
    _timer =
        Timer.periodic(Duration(minutes: _timerDuration), (_) => _logOutUser());
  }

  void _logOutUser() {
    widget.blocAuthentication.add(EventLoggedOut());
    _timer?.cancel();
    Navigator.of(Config.appKey.currentContext!)
        .popUntil((route) => route.isFirst);
  }

  void _handleUserInteraction() {
    if (_timer != null) {
      if (!_timer!.isActive) {
        return;
      } else {
        _timer?.cancel();
        _initializeTimer();
      }
    }
  }
}
