import 'package:theradom/utils/Translations.dart';
import 'package:rxdart/rxdart.dart';

enum TimeLimit { five, ten, twenty, thirty, never }

/// Stream pour la valeur de la durée d'inactivité avant une déconnexion
class LogOutTimerStream {
  LogOutTimerStream._internal();

  static final LogOutTimerStream _instance = LogOutTimerStream._internal();

  static LogOutTimerStream get instance {
    return _instance;
  }

  static Map<TimeLimit, int> mapTimeLimitInt() {
    return {
      TimeLimit.five: 5,
      TimeLimit.ten: 10,
      TimeLimit.twenty: 20,
      TimeLimit.thirty: 30,
      TimeLimit.never: 1000000000
    };
  }

  static Map<int, String> mapIntString() {
    return {
      5: allTranslations.text('five_minutes'),
      10: allTranslations.text('ten_minutes'),
      20: allTranslations.text('twenty_minutes'),
      30: allTranslations.text('thirty_minutes'),
      1000000000: allTranslations.text('never')
    };
  }

  final BehaviorSubject<int?> _duration = BehaviorSubject<int?>();

  ValueStream<int?> get durationStream => _duration.stream;

  void changeDuration(int? duration) {
    // change le temps choisi
    _duration.add(duration);
    return;
  }

  void start() {
    // ajoute au stream la valeur pour démarrer le timer
    _duration.add(-2);
  }

  void stop() {
    // ajoute au stream la valeur pour arrêter le timer
    _duration.add(-1);
  }
}
