import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

/// Classe utilitaire fournissant des méthodes d'aide pour les opérations courantes.
/// Contient des méthodes pour la manipulation de chaînes, la gestion JSON et les informations sur l'appareil.
class Helper {
  /// Convertit une chaîne de nombre avec séparateur décimal virgule en double.
  /// 
  /// Prend une représentation en chaîne d'un nombre qui utilise une virgule comme séparateur décimal
  /// et la convertit pour utiliser un point, puis l'analyse en double.
  /// 
  /// [doubleWithComa] La chaîne de nombre avec séparateur décimal virgule (ex: "1,23")
  /// 
  /// Retourne la valeur double analysée (ex: 1.23)
  static double replaceComaWithDot(String doubleWithComa) {
    String doubleWithDot = doubleWithComa.replaceAll(",", ".");
    double res = double.parse(doubleWithDot);
    return res;
  }

  /// Instance de décodeur JSON pour analyser les chaînes JSON
  static JsonDecoder decoder = JsonDecoder();

  /// Instance d'encodeur JSON pour convertir les objets en chaînes JSON formatées
  /// Configuré pour utiliser une indentation de 2 espaces
  static JsonEncoder encoder = JsonEncoder.withIndent('  ');

  /// Affiche une chaîne JSON de manière formatée et lisible.
  /// 
  /// Prend une chaîne JSON et l'affiche avec une indentation appropriée et des sauts de ligne.
  /// Chaque ligne du JSON formaté est affichée séparément.
  /// 
  /// [input] La chaîne JSON à formater et afficher
  static void prettyPrintJson(String input) {
    var object = decoder.convert(input);
    var prettyString = encoder.convert(object);
    prettyString.split('\n').forEach((element) => print(element));
  }

  /// Récupère le nom/modèle de l'appareil actuel.
  /// 
  /// Utilise le plugin device_info_plus pour obtenir des informations spécifiques à la plateforme.
  /// Retourne le nom du modèle pour les appareils Android et le nom de l'appareil pour les appareils iOS.
  /// 
  /// Retourne un Future qui se termine avec :
  /// - Le modèle/nom de l'appareil sous forme de String pour les appareils Android et iOS
  /// - """" pour les autres plateformes
  static Future<String> getDeviceName() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.name;
    }

    return "";
  }
}
