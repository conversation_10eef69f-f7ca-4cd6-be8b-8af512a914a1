import 'package:theradom/models/SavedMessageData.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/SharedPreferences.dart';

class MessageDataUtils {
  static Future<bool> dataExists() async {
    String? str =
        await SharedPreferencesUtil.instance.getString(Config.prefMessageData);
    bool res = str == null ? false : true;
    return res;
  }

  static saveMessageData(
    String destinataireAffichage,
    String destinataireMessage,
    String objet,
    String contenu,
  ) async {
    // sauver les données reçues
    int i = 1;

    await SharedPreferencesUtil.instance.setInt(Config.prefFakeAssetsNumber, i);

    SavedMessageData savedMessageData = SavedMessageData(
      destinataireAffichage: destinataireAffichage,
      destinataireMessage: destinataireMessage,
      objet: objet,
      contenu: contenu,
    );

    String savedMessageDataJson = savedMessageDataToJson(savedMessageData);

    await SharedPreferencesUtil.instance
        .setString(Config.prefMessageData, savedMessageDataJson);
  }

  static Future<SavedMessageData?> getMessageData() async {
    // cherche et retourne les données sauvées

    String? json =
        await SharedPreferencesUtil.instance.getString(Config.prefMessageData);
    if (json != null) {
      SavedMessageData savedMessageData = savedMessageDataFromJson(json);
      return savedMessageData;
    } else {
      return null;
    }
  }

  static Future<void> deleteMessageData() async {
    await SharedPreferencesUtil.instance.remove(Config.prefMessageData);
    await SharedPreferencesUtil.instance.remove(Config.prefFakeAssetsNumber);
  }
}
