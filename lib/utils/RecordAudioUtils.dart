import 'dart:io';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/widgets/audio_recorder.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/buttons/dialog_button.dart';

class RecordAudioUtils {
  static late bool isRecorded;
  static late File? audioFile;

  static Future<List> showAlertAudioRecorder(BuildContext context) async {
    final AudioRecorder audioRecorder = AudioRecorder();
    final double _fontSize = Config.basicUserInfos!.taillePolice;
    final AutoSizeGroup group = AutoSizeGroup();

    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Dialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0)),
                child: Padding(
                  padding: EdgeInsets.only(
                      left: 15.0, right: 15.0, bottom: 15.0, top: 30.0),
                  child: IntrinsicHeight(
                      child: SingleChildScrollView(
                          child: Column(
                    children: [
                      Text(
                        allTranslations.text("audio_recorder"),
                        style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                            fontSize: _fontSize),
                      ),
                      SizedBox(
                        height: 30.0,
                      ),
                      audioRecorder,
                      SizedBox(
                        height: 30.0,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          DialogButton(
                              label: allTranslations.text('back'),
                              labelColor: AppTheme.primaryColor,
                              backgroundColor: AppTheme.backgroundColor,
                              group: group,
                              onPressed: () {
                                if ((audioRecorder.recordingStatus ==
                                        RecordingStatus.Stopped) ||
                                    (audioRecorder.recordingStatus ==
                                        RecordingStatus.Initialized)) {
                                  isRecorded = false;
                                  Navigator.pop(context);
                                }
                              }),
                          SizedBox(
                            width: 30.0,
                          ),
                          DialogButton(
                              label: allTranslations.text('to_validate'),
                              labelColor: AppTheme.primaryColor,
                              backgroundColor: AppTheme.secondaryColor,
                              group: group,
                              onPressed: () async {
                                if (audioRecorder.recordingStatus ==
                                    RecordingStatus.Stopped) {
                                  isRecorded = true;
                                  audioFile = audioRecorder.audioFile!;
                                  Navigator.pop(context);
                                }
                              })
                        ],
                      )
                    ],
                  ))),
                ),
              );
            },
          );
        });

    return [isRecorded, audioFile];
  }

  static Future<bool> recordAnotherAudioFile(
      BuildContext cntxt, File? audioFile) async {
    bool continuer = false;
    if (audioFile != null) {
      bool? recordAnotherFile = await AlertBox(
              context: cntxt,
              title: allTranslations.text('warning'),
              description:
                  allTranslations.text('audio_file_already_exists_msg'),
              confirmBtnLabel: allTranslations.text('to_delete'))
          .showConfirmation(warning: true);
      if (recordAnotherFile != null) {
        if (recordAnotherFile) {
          continuer = true;
        }
      }
    } else {
      continuer = true;
    }
    return continuer;
  }
}
