import 'package:flutter/material.dart';
import 'package:theradom/config.dart';

class DefaultScreenController extends ChangeNotifier {
  late int _screenIndex;

  //get l'index de l'écran par défaut
  int get screenIndex => _screenIndex;

  void init() {
    _screenIndex = Config.defaultScreen; // par défaut
  }

  void setScreenIndex(int screenIndex) {
    _screenIndex = screenIndex;
    // notifie l'appli que l'index a changé
    notifyListeners();
  }

  void setScreenIndexOnLaunch(int screenIndex) {
    // quand j'appelle depuis onLauch quand j'ai recu une notification
    _screenIndex = screenIndex;
  }

  static DefaultScreenController of(BuildContext context) {
    // get le controller depuis n'importe quelle écran de l'appli
    final provider =
        context.dependOnInheritedWidgetOfExactType<DefaultScreenProvider>();
    return provider!.controller;
  }
}

// fourni le controller à tous les écrans de l'appli
class DefaultScreenProvider extends InheritedWidget {
  const DefaultScreenProvider({required this.controller, required Widget child})
      : super(child: child);

  final DefaultScreenController controller;

  @override
  bool updateShouldNotify(DefaultScreenProvider old) =>
      controller != old.controller;
}
