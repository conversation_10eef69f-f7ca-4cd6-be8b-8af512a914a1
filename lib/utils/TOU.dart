import 'package:flutter/material.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/TOURequest.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_parametres_repository.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:theradom/widgets/alert_box.dart';
import 'package:theradom/widgets/TermsOfUseElement.dart';

class TOU {
  late final MobileParametresRepository _mobileParametresRepository;

  TOU({MobileParametresRepository? mobileParametresRepository}) : super() {
    _mobileParametresRepository =
        mobileParametresRepository ?? MobileParametresRepository();
  }

  Future<void> checkTOU(BuildContext context, bool accordCgu) async {
    if (accordCgu == false) {
      await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)),
              child: IntrinsicHeight(
                child: IntrinsicWidth(
                    child: TermsOfUseElement(
                        onAgreement: () => _agreeTermsOfUse(context))),
              ),
            );
          });
    }
  }

  Future<void> _agreeTermsOfUse(BuildContext context) async {
    try {
      TOURequest touRequest =
          TOURequest(numpat: Config.basicUserInfos!.numpat, accordCGU: true);
      String touRequestJson = touRequestToJson(touRequest);
      await _mobileParametresRepository
          .changeTermsOfUseAgreement(touRequestJson);
      Config.basicUserInfos!.accordCgu = true;
      Navigator.of(context).pop();
    } on WebServiceCallException catch (e) {
      _showFlushbar(context, e.toString());
    }
  }

  static void _showFlushbar(BuildContext context, String message) {
    AlertBox(
            context: context,
            title: allTranslations.text('error'),
            description: message)
        .showFlushbar(warning: true);
  }
}
