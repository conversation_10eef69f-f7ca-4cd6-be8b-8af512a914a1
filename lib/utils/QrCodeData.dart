import 'package:theradom/models/QRcodeMessage.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/SharedPreferences.dart';

class QrCodeData {
  // fonction de stockage sur disque des données des qr codes

  static Future<void> saveQrCode(String qrCodeJson) async {
    // sauver un qr code
    List<QRcodeMessage> qrCodeMessageList = await getQrCodes();
    int length = qrCodeMessageList.length;
    await SharedPreferencesUtil.instance
        .setString(Config.prefSavedQrCode + length.toString(), qrCodeJson);
  }

  static Future<void> saveQrCodeJsonList(List<String> qrCodeJsonList) async {
    int i = 0;
    Future.forEach(qrCodeJsonList, (String qrCodeJson) async {
      await SharedPreferencesUtil.instance
          .setString(Config.prefSavedQrCode + i.toString(), qrCodeJson);
      i++;
    });
  }

  static Future<List<QRcodeMessage>> getQrCodes() async {
    // cherche et retourne tous les qr codes sauvées
    List<QRcodeMessage> qrCodeMessageList = [];
    int i = 0;
    bool stop = false;
    while (stop == false) {
      String? qrCodeMessageJson = await SharedPreferencesUtil.instance
          .getString(Config.prefSavedQrCode + i.toString());
      if (qrCodeMessageJson == null) {
        stop = true;
      } else {
        // je le récupère
        QRcodeMessage qRcodeMessage = qrCodeMessagefromJson(qrCodeMessageJson);
        qrCodeMessageList.add(qRcodeMessage);
        i++;
      }
    }
    return qrCodeMessageList;
  }

  static Future<void> deleteQrCode(QRcodeMessage qrCodeMessage) async {
    // supprimer un qr code
    List<String> qrCodeJsonList = [];
    List<QRcodeMessage> qrCodeMessageList = await getQrCodes();
    late int index;
    for (QRcodeMessage qrCodeMessageSaved in qrCodeMessageList) {
      if (qrCodeMessage.numpat == qrCodeMessageSaved.numpat) {
        index = qrCodeMessageList.indexOf(qrCodeMessageSaved);
        break;
      }
    }
    // suppression
    qrCodeMessageList.removeAt(index);
    await SharedPreferencesUtil.instance
        .remove(Config.prefSavedQrCode + index.toString());

    // je remplace les qr code sauvés par les nouveaux (sans celui supprimé)
    qrCodeMessageList.forEach((qrCodeMessage) {
      qrCodeJsonList.add(qrCodeMessageToJson(qrCodeMessage));
    });
    await saveQrCodeJsonList(qrCodeJsonList);

    // je supprime le dernier qr code qui fait doublon sinon
    int length = qrCodeMessageList.length;
    await SharedPreferencesUtil.instance
        .remove(Config.prefSavedQrCode + length.toString());
  }

  static Future<void> deleteAllQrCodes() async {
    // supprime tous les qr codes
    List<QRcodeMessage> qrCodeMessageList = await getQrCodes();
    Future.forEach(qrCodeMessageList, (QRcodeMessage qrCodeMessage) async {
      await deleteQrCode(qrCodeMessage);
    });
  }
}
