class RegexpUtils {
  static RegExp integer = RegExp(r'^-?(\d+)?');

  static RegExp decimal = RegExp(
      r'^-?(\d+)?[\,\.]?\d{0,2}'); // nombre decimal au format -*.** ou *,**

  static RegExp decimalNotSigned =
      RegExp(r'^([0-9]+[\.\,]?\d{0,2})'); // nombre decimal au format *,**

  static RegExp alphanumeric = RegExp(r'^[a-zA-Z0-9 ]+|\s');

  static RegExp emailAdress =
      RegExp(r'^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$');
}
