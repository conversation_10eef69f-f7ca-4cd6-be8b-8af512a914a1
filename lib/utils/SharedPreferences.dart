import 'dart:io';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:theradom/utils/QrCodeData.dart';
import 'package:path_provider/path_provider.dart';

class SharedPreferencesUtil {
  // Create storage
  static final storage = FlutterSecureStorage();

  static final SharedPreferencesUtil instance =
      SharedPreferencesUtil._internal();

  SharedPreferencesUtil._internal();

  Future<int?> getInt(String key) async {
    String? stringRes = await storage.read(key: key);
    int? res = stringRes != null ? int.parse(stringRes) : null;
    return res;
  }

  Future<void> setInt(String key, int value) async {
    await storage.write(key: key, value: value.toString());
  }

  Future<String?> getString(String key) async {
    return await storage.read(key: key);
  }

  Future<void> setString(String key, String value) async {
    await storage.write(key: key, value: value);
  }

  Future<bool> getBool(String key) async {
    String? stringRes = await storage.read(key: key);
    bool res = stringRes == "true" ? true : false;
    return res;
  }

  Future<void> setBool(String key, bool value) async {
    await storage.write(key: key, value: value.toString());
  }

  Future<void> remove(String key) async {
    await storage.delete(key: key);
  }

  Future<void> clearSecureStorageOnReinstall() async {
    // pour ios, lancer une appli avec seulement la ligne suivante si pb
    // await storage.deleteAll();

    final directory = await getApplicationDocumentsDirectory();
    final String fileName = "first_run.txt";
    File file = File("${directory.path}/$fileName");
    bool firstRun = !file.existsSync();
    if (firstRun) {
      // supprimer le storage quand on lance l'application pour la première fois
      // utile uniquement pour iOS
      await storage.deleteAll();
      await QrCodeData.deleteAllQrCodes();
      //await storage.delete(key: Config.prefUsers); // a supprimer a terme
      //await storage.delete(key: "url"); // a supprimer a terme
      file.writeAsString("first run");
    }
  }
}
