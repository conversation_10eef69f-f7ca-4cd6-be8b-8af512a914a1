import 'package:flutter/services.dart';
import 'package:theradom/utils/Translations.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';

class Biometric {
  static LocalAuthentication _localAuthentication = LocalAuthentication();

  static final Biometric authentication = Biometric._internal();

  Biometric._internal();

  Future<bool> canCheck() async {
    /// can use biometrics on device
    bool isDeviceSupported = await _localAuthentication.isDeviceSupported();
    bool canCheckBiometrics = await _localAuthentication.canCheckBiometrics;
    return (isDeviceSupported && canCheckBiometrics) ? true : false;
  }

  Future<bool> start() async {
    /// start biometric authentication
    bool isAuthenticated = false;
    try {
      isAuthenticated = await _localAuthentication.authenticate(
          localizedReason: allTranslations.text('txt_authenticate'),
          authMessages: [
            AndroidAuthMessages(
              cancelButton: allTranslations.text('cancel'),
              goToSettingsButton: allTranslations.text('txt_parameters'),
              goToSettingsDescription:
                  allTranslations.text('pls_activate_biometrics'),
              biometricNotRecognized:
                  allTranslations.text('unrecognized_fingerprint'),
              biometricSuccess: allTranslations.text("fingerprint_success"),
              biometricHint: allTranslations.text("fingerprint_hint"),
              biometricRequiredTitle: "",
              signInTitle: allTranslations.text("biometrics"),
            )
          ],
          options: AuthenticationOptions(
            stickyAuth: true,
            biometricOnly: true,
          ));
    } on PlatformException catch (e) {
      if (e.code != "auth_in_progress") throw e;
    }
    return isAuthenticated;
  }
}
