import 'dart:io';
import 'dart:typed_data';
import 'package:theradom/utils/HelperPj.dart';
import 'package:pdf/widgets.dart' as pdfWidgets;

class PdfBuilder {
  static final PdfBuilder create = PdfBuilder._internal();

  PdfBuilder._internal();

  Future<String> withFiles(
      {required String title, List<File> fileList = const []}) async {
    // créé un pdf à partir d'une liste d'images
    final pdfWidgets.Document pdf = pdfWidgets.Document();
    int i = 0;
    while (i < fileList.length) {
      Uint8List uint8list = fileList[i].readAsBytesSync();
      pdf.addPage(pdfWidgets.Page(build: (pdfWidgets.Context context) {
        return pdfWidgets.Center(
          child: pdfWidgets.Image(pdfWidgets.MemoryImage(uint8list)),
        ); // Center
      })); // Page

      i++;
    }
    String path = await HelperPj.saveFileInTempDirectory(
        await pdf.save(), title + ".pdf");
    return path;
  }
}
