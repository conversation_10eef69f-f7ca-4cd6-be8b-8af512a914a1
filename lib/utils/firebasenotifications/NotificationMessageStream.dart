import 'package:theradom/models/FirebaseNotification.dart';
import 'package:rxdart/rxdart.dart';

class NotificationMessageStream {
  NotificationMessageStream._internal();

  static final NotificationMessageStream _instance =
      NotificationMessageStream._internal();

  static NotificationMessageStream get instance {
    return _instance;
  }

  final _message = BehaviorSubject<FirebaseNotification>();

  Stream<FirebaseNotification> get messageStream => _message.stream;

  void addMessage(FirebaseNotification? msg) {
    if (msg != null) _message.add(msg);
    return;
  }

  void dispose() {
    _message.close();
  }
}
