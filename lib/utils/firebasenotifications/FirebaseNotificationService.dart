import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:theradom/models/FirebaseNotification.dart';
import 'package:theradom/utils/firebasenotifications/NotificationMessageStream.dart';

class FirebaseNotificationService {
  static final FirebaseNotificationService _instance =
      FirebaseNotificationService._internal();

  FirebaseNotificationService._internal() {
    // save the client so that it can be used else where
    // setup listeners
    _firebaseCloudMessagingListeners();
  }

  static FirebaseNotificationService get instance {
    return _instance;
  }

  // get the message stream
  final NotificationMessageStream _notificationMessageStream =
      NotificationMessageStream.instance;

  // method for getting the messaging token
  Future<String> getDeviceToken() async {
    String? appToken = await FirebaseMessaging.instance.getToken();
    return appToken!;
  }

  void _firebaseCloudMessagingListeners() {
    if (Platform.isIOS) _getPermissions();

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // print('on message ${message.toMap().toString()}');
      _onMessage(message.toMap());
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      // print('on launch ${message.toMap().toString()}');
      _onLaunch(message.toMap());
    });

    //FirebaseMessaging.onBackgroundMessage((message) async => _onResume(message))
  }

  void _onMessage(Map<String, dynamic> message) {
    FirebaseNotification firebaseNotification =
        firebaseNotificationFromJson(0, message);
    // add notification to stream
    _notificationMessageStream.addMessage(firebaseNotification);
  }

  void _onLaunch(Map<String, dynamic> message) {
    FirebaseNotification firebaseNotification =
        firebaseNotificationFromJson(2, message);
    _notificationMessageStream.addMessage(firebaseNotification);
  }

  Future<void> _getPermissions() async {
    await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true);
  }
}
