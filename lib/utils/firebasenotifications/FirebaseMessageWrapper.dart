import 'package:flutter/material.dart';

import 'package:theradom/models/FirebaseNotification.dart';
import 'package:theradom/utils/DefaultScreenController.dart';
import 'package:theradom/utils/UrlLauncherUtils.dart';
import 'package:theradom/utils/firebasenotifications/NotificationMessageStream.dart';
import 'package:theradom/widgets/alert_box.dart';
// import 'package:vibration/vibration.dart'; // Temporarily commented out
import 'package:theradom/config.dart';

class FirebaseMessageWrapper extends StatefulWidget {
  final Widget child;

  FirebaseMessageWrapper(this.child);
  @override
  _FirebaseMessageWrapperState createState() => _FirebaseMessageWrapperState();
}

class _FirebaseMessageWrapperState extends State<FirebaseMessageWrapper> {
  NotificationMessageStream _messageStream = NotificationMessageStream.instance;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        initialData: null,
        stream: _messageStream.messageStream,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          FirebaseNotification? firebaseNotification = snapshot.data;
          if (firebaseNotification != null) {
            // check if this instance is the current route or else message will be displayed on all instances
            WidgetsBinding.instance.addPostFrameCallback((_) {
              switch (firebaseNotification.event) {
                case 0:
                  {
                    // onMessage
                    if (Config.basicUserInfos != null)
                      _showMessage(
                          firebaseNotification); // affichage seulement si le user est connecté
                  }
                  break;
                case 1:
                  {
                    // onResume
                    _launchUrlIfValid(firebaseNotification);
                  }
                  break;
                case 2:
                  {
                    // onLaunch
                    _launchUrlIfValid(firebaseNotification);
                    if (firebaseNotification.actionType != null) {
                      DefaultScreenController.of(context)
                          .setScreenIndexOnLaunch(
                              firebaseNotification.actionType!);
                    }
                  }
                  break;
              }
            });
            // adding a null stops the previous message from being displayed again
            NotificationMessageStream.instance.addMessage(null);
          }
          return widget.child;
        });
  }

  void _launchUrlIfValid(FirebaseNotification firebaseNotification) {
    if (firebaseNotification.url != null) {
      UrlLauncherUtils.launchURL(firebaseNotification.url!);
    }
  }

  void _showMessage(FirebaseNotification firebaseNotification) {
    // affiche la notification à l'écran si le user est connecté
    if (Config.basicUserInfos?.numpat != null) {
      AlertBox(
              context: Config.appKey.currentContext!,
              title: firebaseNotification.title,
              description: firebaseNotification.body)
          .showFlushbar();
      // Vibration.vibrate(duration: 1); // Temporarily commented out
    }
  }
}
