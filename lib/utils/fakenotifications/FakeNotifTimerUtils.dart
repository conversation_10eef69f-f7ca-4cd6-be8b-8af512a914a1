import 'dart:async';

import 'package:theradom/models/NotificationResponse.dart';
import 'package:theradom/config.dart';
import 'package:theradom/models/UserID.dart';
import 'package:theradom/models/finished_list.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/services/repositories/mobile_notification_repository.dart';
import 'package:theradom/views/home/<USER>';

class FakeNotifTimerUtils {
  //Singleton

  static final FakeNotifTimerUtils _singleton = FakeNotifTimerUtils._internal();

  factory FakeNotifTimerUtils() {
    return _singleton;
  }

  FakeNotifTimerUtils._internal();

  static Timer? timer;

  static Future init() async {
    if (Config.basicUserInfos?.numpat != null) {
      try {
        UserID userID = UserID(numpat: Config.basicUserInfos!.numpat);
        String userIDjson = userIdToJson(userID);
        MobileNotificationRepository mobileNotificationRepository =
            MobileNotificationRepository();
        Config.notificationResponse = await mobileNotificationRepository
            .fetchFakeNotifications(userIDjson);
        Config.notificationResponseInit = Config.notificationResponse;
      } on WebServiceCallException catch (_) {
        Config.notificationResponse = NotificationResponse(
            notifRdvAujourdhui: 0,
            notifMail: 0,
            notifRdVnew: 0,
            notifRdVsoon: 0,
            seanceNonFinie: {},
            endSeance: FinishedList(empty: true),
            teleconsultation: "",
            valide: "OK",
            erreur: null);
        Config.notificationResponseInit = Config.notificationResponse;
      }
    }
  }

  static Future startTimer() async {
    await init();
    timer = Timer.periodic(Duration(seconds: 60), (Timer t) async {
      try {
        UserID userID = UserID(numpat: Config.basicUserInfos!.numpat);
        String userIDjson = userIdToJson(userID);
        MobileNotificationRepository mobileNotificationRepository =
            MobileNotificationRepository();
        NotificationResponse notificationResponse =
            await mobileNotificationRepository
                .fetchFakeNotifications(userIDjson);
        if (homeGlobalKey.currentContext != null) {
          Home.of(homeGlobalKey.currentContext!)!
              .updateFakeNotifications(notificationResponse);
        }
      } on WebServiceCallException catch (_) {}
    });
  }

  static void stopTimer() {
    timer?.cancel();
  }
}
