import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class MultiImagePickerUtils {
  //Singleton

  static final MultiImagePickerUtils _singleton =
      MultiImagePickerUtils._internal();

  factory MultiImagePickerUtils() {
    return _singleton;
  }

  static int quality = 15;

  MultiImagePickerUtils._internal();

  static Future<List> pickImagesFromGallery(
      BuildContext context, int maxImages) async {
    List<File> fileList = [];
    List<Image> thumbnailList = [];

    try {
      final List<AssetEntity>? assetList = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfig(
            requestType: RequestType.image,
            themeColor: AppTheme.backgroundColor,
            previewThumbnailSize: ThumbnailSize.square(100),
            maxAssets: maxImages),
      );

      fileList = await _getLowQualityImages(assetList);
      thumbnailList = await _getThumbnailImages(assetList);
    } catch (e) {}
    return [thumbnailList, fileList];
  }

  static Future<List<File>> _getLowQualityImages(
      List<AssetEntity>? assetEntities) async {
    // retourne des File des images choisies
    List<File> lowQualityFileList = [];
    if (assetEntities != null) {
      for (AssetEntity assetEntity in assetEntities) {
        File? file = await assetEntity.file;
        if (file != null) {
          lowQualityFileList.add(file);
        }
      }
    }
    return lowQualityFileList;
  }

  static _getThumbnailImages(List<AssetEntity>? assetEntities) async {
    // retourne des Image des images choisies
    List<Image> thumbnailList = [];
    if (assetEntities != null) {
      for (AssetEntity assetEntity in assetEntities) {
        Uint8List? byteData = await assetEntity
            .thumbnailDataWithSize(ThumbnailSize.square(100), quality: quality);
        if (byteData != null) {
          thumbnailList.add(Image.memory(byteData));
        }
      }
    }
    return thumbnailList;
  }
}
