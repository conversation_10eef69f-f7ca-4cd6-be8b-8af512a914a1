import 'package:flutter/material.dart';

class AppTheme {
  static final primaryColor = Color(0xFF332F5D);
  static final primaryColorDarker = Color(0xFF262345);
  static final backgroundColor = Color(0xFFFFFFFF);
  static final secondaryColor = Color(0xFF70D8DF);
  static final secondaryColorDarker = Color(0xFF6BCDDB);
  static final tertiaryColor = Color(0xFFD85C74);
  static final disabledColor = Color(0xFF9E9E9E);
  static final borderColor = Color.fromRGBO(52, 46, 96, 1);
  static final primaryBlurColor = Color.fromRGBO(66, 66, 148, 0.5);
  static final secondaryBlurColor = Color.fromRGBO(126, 142, 255, 0.5);
  static final dividerColor = Color.fromRGBO(66, 59, 122, 1);
  static final greyColor = primaryColor.withOpacity(0.3);
  static final lightGrey = Color.fromRGBO(214, 213, 222, 1);

  static final painColorList = [
    Color.fromRGBO(122, 218, 80, 1), // vert
    Color.fromRGBO(200, 225, 89, 1), // vert clair
    Color.fromRGBO(215, 179, 68, 1), // jaune
    Color.fromRGBO(218, 136, 52, 1), // orange
    Color.fromRGBO(215, 99, 44, 1), // orange foncé
    Color.fromRGBO(213, 59, 37, 1), // rouge
  ];

  static final chartColorList = [
    Color(0xFFda92d1),
    Color(0xFF70D8DF),
    Color(0xFF8ae6a1),
    Color(0xFFffdb7f),
    Color.fromRGBO(218, 136, 52, 1),
    Color(0xFF828282),
    Color(0xFF253b3c),
    Color.fromRGBO(213, 59, 37, 1) // rouge
  ];

  static final Color chartNormalColor = Color(0xFFb0d8bd);
  static final Color chartLimitColor = Color(0xFFD85C74);
}
