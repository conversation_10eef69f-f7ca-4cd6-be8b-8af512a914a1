import 'dart:convert';

import 'package:theradom/config.dart';
import 'package:crypto/crypto.dart' as crypto;
import 'package:encrypt/encrypt.dart' as encrypt;

class EncryptionUtils {
  final String input;

  final encrypt.Key _key = encrypt.Key.fromUtf8(Config.aesSecretKey);
  final encrypt.IV _iv = encrypt.IV.fromLength(16);

  EncryptionUtils(this.input);

  String toMd5() {
    var md5 = crypto.md5;
    return md5.convert(utf8.encode(input)).toString();
  }

  String? aesDecrypt() {
    String? decryptedString;
    final encrypt.Encrypter encrypter =
        encrypt.Encrypter(encrypt.AES(_key, mode: encrypt.AESMode.ecb));
    try {
      encrypt.Encrypted encryptedFromBase64 =
          encrypt.Encrypted.fromBase64(input);
      decryptedString = encrypter.decrypt(encryptedFromBase64, iv: _iv);
    } catch (error) {
      decryptedString = null;
    }
    return decryptedString;
  }

  String? aesEncrypt(String strToEncode) {
    // encrypte un string et le retourne en base 64 ou retourne null
    String? encodedString;
    final encrypt.Encrypter encrypter =
        encrypt.Encrypter(encrypt.AES(_key, mode: encrypt.AESMode.ecb));
    try {
      encodedString = encrypter.encrypt(strToEncode, iv: _iv).base64;
    } catch (error) {
      encodedString = null;
    }
    return encodedString;
  }
}
