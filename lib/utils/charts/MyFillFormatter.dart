import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

// This class is a replacement for the mp_chart fill formatter
// In fl_chart, fill formatting is handled directly in LineChartBarData.belowBarData
// This class is kept for backward compatibility but is not used directly

class MyFillFormatterMin {
  final double minValue;

  MyFillFormatterMin({this.minValue = 0});
}

class MyFillFormatterMax {
  final double normaleInf;

  MyFillFormatterMax({required this.normaleInf});
}
