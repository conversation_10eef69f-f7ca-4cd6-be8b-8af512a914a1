import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:theradom/extensions/int_extension.dart';

// This class is a replacement for the mp_chart ChartMarker
// In fl_chart, markers are handled directly in the LineTouchTooltipData
// This class is kept for backward compatibility but is not used directly

class ChartMarker {
  final Color textColor;
  final Color backColor;
  final double fontSize;

  ChartMarker({
    required this.textColor,
    required this.backColor,
    required this.fontSize,
  });

  // These methods are stubs to maintain compatibility with the old API
  void draw(Canvas canvas, double? posX, double? posY) {}

  Offset calculatePos(double posX, double posY, double textW, double textH) {
    return Offset(posX - textW / 2, textH);
  }

  MPPointF getOffset() {
    return MPPointF(0, 0);
  }

  MPPointF getOffsetForDrawingAtPoint(double posX, double posY) {
    return getOffset();
  }

  void refreshContent(dynamic e, dynamic highlight) {}
}

// Simple class to maintain compatibility with the old API
class MPPointF {
  final double x;
  final double y;

  MPPointF(this.x, this.y);
}
