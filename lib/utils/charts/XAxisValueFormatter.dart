import 'package:theradom/extensions/int_extension.dart';
import 'package:theradom/extensions/datetime_extension.dart';

// This class is a replacement for the mp_chart XAxisValueFormatter
// In fl_chart, formatting is handled directly in the getTitlesWidget function
// This class is kept for backward compatibility but is not used directly

class XAxisValueFormatter {
  XAxisValueFormatter();

  String getFormattedValue1(double? value) {
    int timestamp = value == null ? 0 : value.toInt();
    DateTime dateTime = timestamp.timestamp2DateTime();
    String dateStr = dateTime.toLanguageStringShort();
    return dateStr;
  }
}
