import 'package:flutter/foundation.dart';
import 'package:theradom/config.dart';

/// Observable pour gérer les notifications de messages
class NotificationMailObservable {
  static final NotificationMailObservable _instance =
      NotificationMailObservable._internal();
  late final ValueNotifier<int> notifMailNotifier;

  /// Constructeur factory pour le singleton
  factory NotificationMailObservable() {
    return _instance;
  }

  NotificationMailObservable._internal() {
    // Initialise avec la valeur actuelle ou 0 si null
    notifMailNotifier =
        ValueNotifier(Config.notificationResponse?.notifMail ?? 0);
  }

  // Getter pour la valeur actuelle
  /// @return Le nombre actuel de notifications
  int get value => notifMailNotifier.value;

  // Setter qui met à jour à la fois Config et le notifier
  /// @param newValue - La nouvelle valeur à définir pour le nombre de notifications
  set value(int newValue) {
    if (Config.notificationResponse != null) {
      final oldValue = Config.notificationResponseInit!.notifMail;

      // Déclenche le callback d'incrémentation si la valeur a augmenté
      if (newValue > oldValue) {
        onIncrement?.call(newValue);
      }
      Config.notificationResponse!.notifMail = newValue;
      notifMailNotifier.value = newValue;

      Config.notificationResponseInit!.notifMail =
          Config.notificationResponse!.notifMail;
    }
  }

  // Callback pour les événements d'incrémentation
  void Function(int)? onIncrement;

  // Méthode pour ajouter un listener d'incrémentation
  /// @param listener - Fonction appelée lorsque la valeur est incrémentée
  /// La fonction reçoit la nouvelle valeur en paramètre
  void addIncrementListener(void Function(int) listener) {
    onIncrement = listener;
  }

  // Méthode pour supprimer le listener d'incrémentation
  /// Supprime le listener d'incrémentation actuel
  void removeIncrementListener() {
    onIncrement = null;
  }

  // Méthode pour ajouter un listener de changement de valeur
  /// @param listener - Fonction appelée à chaque changement de valeur
  void addListener(VoidCallback listener) {
    notifMailNotifier.addListener(listener);
  }

  // Méthode pour supprimer le listener de changement de valeur
  /// @param listener - Le listener à supprimer des notifications
  void removeListener(VoidCallback listener) {
    notifMailNotifier.removeListener(listener);
  }
}
