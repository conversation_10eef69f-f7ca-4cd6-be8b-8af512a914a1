import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theradom/config.dart';
import 'package:theradom/utils/DefaultScreenController.dart';
import 'package:theradom/utils/theme/app_theme.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseMessageWrapper.dart';
import 'package:theradom/utils/font_size_controller.dart';
import 'package:theradom/utils/log_out_timer/logout_timer_wrapper.dart';
import 'package:theradom/utils/sampling_data_timer/sampling_timer_wrapper.dart';
import 'package:theradom/views/first_time_launch/view_create_password.dart';
import 'package:theradom/views/first_time_launch/view_welcome.dart';
import 'package:theradom/views/login/ViewLoginForm.dart';
import 'package:theradom/views/login/ViewLoginIndicator.dart';
import 'package:theradom/views/login/view_user_confirmation.dart';
import 'package:theradom/widgets/custom_scroll_behavior.dart';
import 'utils/Translations.dart';
import 'blocs/authentication/BlocAuthentication.dart';
import 'blocs/authentication/StateAuthentication.dart';
import 'views/home/<USER>';
import 'package:flutter_localizations/flutter_localizations.dart';

class App extends StatefulWidget {
  App() : super();

  @override
  _AppState createState() => _AppState();
}

class _AppState extends State<App>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  //ignore: close_sinks
  late BlocAuthentication _blocAuthentication;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark));
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _blocAuthentication = BlocProvider.of<BlocAuthentication>(context);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _animationController.dispose();
    super.dispose();
  }

  @override
  didPopRoute() {
    return Future<bool>.value(Config.blockBackBtnPop);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, _) {
        return LogOutTimerWrapper(
          blocAuthentication: _blocAuthentication,
          child: MaterialApp(
            navigatorKey: Config.appKey,
            debugShowCheckedModeBanner: false,
            theme: _buildMaterialTheme(),
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              DefaultMaterialLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              DefaultWidgetsLocalizations.delegate
            ],
            supportedLocales: allTranslations.supportedLocales(),
            builder: (context, child) => Scaffold(
              body: child,
            ),
            home: ScrollConfiguration(
              behavior: CustomScrollBehavior(),
              child: FirebaseMessageWrapper(
                SamplingTimerWrapper(
                  child: BlocBuilder<BlocAuthentication, StateAuthentication>(
                    bloc: _blocAuthentication,
                    builder: (context, state) {
                      if (state is StateAuthenticationAppFirstState) {
                        return Container(color: AppTheme.backgroundColor);
                      } else if (state is StateAuthenticationAuthenticated) {
                        FontSizeController.of(context).init();
                        return Home(
                            realIndex: DefaultScreenController.of(context)
                                .screenIndex);
                      } else if (state is StateAuthenticationUnauthenticated) {
                        return ViewLoginForm();
                      } else if (state is StateAuthenticationScanQrCode) {
                        return ViewWelcome();
                      } else if (state is StateAuthenticationChangePassword) {
                        return ViewCreatePassword(
                            userIdentity: state.userIdentity);
                      } else if (state is StateAuthenticationConfirmIdentity) {
                        return ViewUserConfirmation(state.newAppId, state.MFA);
                      } else if (state is StateAuthenticationLoading) {
                        return ViewLoginIndicator(message: state.message);
                      } else {
                        return Container(child: Text("Error"));
                      }
                    },
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  ThemeData _buildMaterialTheme() {
    // build le thème de l'application
    ThemeData themeData = ThemeData(
        primaryColor: AppTheme.primaryColor,
        primaryColorDark: AppTheme.primaryColorDarker,
        shadowColor: AppTheme.primaryBlurColor,
        canvasColor: AppTheme.backgroundColor,
        highlightColor: AppTheme.backgroundColor,
        dividerColor: AppTheme.dividerColor.withOpacity(0.3),
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        useMaterial3: false,
        appBarTheme: AppBarTheme(backgroundColor: AppTheme.primaryColor),
        textSelectionTheme: TextSelectionThemeData(
            cursorColor: AppTheme.primaryColor,
            selectionColor: AppTheme.secondaryColor.withOpacity(0.4),
            selectionHandleColor: AppTheme.secondaryColor),
        fontFamily: 'OpenSans',
        textButtonTheme: TextButtonThemeData(
            style: ButtonStyle(
                backgroundColor: MaterialStateProperty.resolveWith(
                    (states) => AppTheme.secondaryColor),
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                        side: BorderSide(color: Colors.transparent))))),
        colorScheme: Theme.of(context).colorScheme.copyWith(
            primary: AppTheme.primaryColor,
            background: AppTheme.backgroundColor,
            secondary: AppTheme.secondaryColor,
            error: AppTheme.tertiaryColor));
    return themeData;
  }
}
