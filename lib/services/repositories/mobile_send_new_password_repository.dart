import 'package:theradom/exceptions/business_exception.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/models/StatusResponse.dart';
import 'dart:convert';

import 'package:theradom/utils/Helper.dart';

class MobileSendNewPasswordRepository {
  late ApiProvider _apiProvider;
  final String _webServiceName = "Mobile_registerIDApp";

  MobileSendNewPasswordRepository() {
    _apiProvider = ApiProvider(); // Initialize _apiProvider here if needed
  }

  Future<dynamic> sendNewPassword(
      String appToken,
      String INS,
      String login,
      String email,
      String OTP,
      bool save_pwd,
      String password,
      String deviceName) async {
    Map<String, dynamic> jsonData = {
      "appToken": appToken,
      "NomAppareil": deviceName,
      "INS": INS,
      "login": login,
      "OTP": OTP,
      "email": email,
      "save_pwd": save_pwd.toString(),
      "password": password,
      "deviceName": deviceName,
    };

    final response = await _apiProvider.post(_webServiceName,
        data: jsonEncode(jsonData), option: 1);
    if (response.statusCode == 200) {
      StatusResponse statusResponse = statusResponseFromJson(response.body);
      if (statusResponse.valide == "OK") {
        return "OK";
      } else {
        throw MobileSendNewPasswordException(statusResponse.erreur!);
      }
    }
  }

  Future<dynamic> ResetPassword(String ins, String login, String otp,
      String appToken, bool savePwd, String password) async {
    String deviceName = await Helper.getDeviceName();
    Map<String, dynamic> jsonData = {
      "appToken": appToken,
      "ins": ins,
      "login": login,
      "OTP": otp,
      "save_pwd": savePwd.toString(),
      "password": password,
      "deviceName": deviceName,
    };

    final response = await _apiProvider.post(_webServiceName,
        data: jsonEncode(jsonData), option: 1);
    if (response.statusCode == 200) {
      StatusResponse statusResponse = statusResponseFromJson(response.body);
      if (statusResponse.valide == "OK") {
        return statusResponse;
      } else {
        throw MobileSendNewPasswordException(statusResponse.erreur!);
      }
    }
  }

  Future<dynamic> sendOTP(
      String ins, String otp, String appToken, String login) async {
    Map<String, dynamic> jsonData = {
      "appToken": appToken,
      "ins": ins,
      "OTP": otp,
      "login": login,
      "deviceName": "test",
    };

    final response = await _apiProvider.post(_webServiceName,
        data: jsonEncode(jsonData), option: 1);
    if (response.statusCode == 200) {
      StatusResponse statusResponse = statusResponseFromJson(response.body);
      if (statusResponse.valide == "OK") {
        return statusResponse;
      } else {
        throw MobileSendNewPasswordException(statusResponse.erreur!);
      }
    }
  }
}
