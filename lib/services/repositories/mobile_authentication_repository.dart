import 'package:theradom/models/BasicUserInfos.dart';
import 'package:theradom/models/BasicUserInfos2.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class MobileAuthenticationRepository {
  late ApiProvider _apiProvider;

  MobileAuthenticationRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_authentification";

  Future<dynamic> authenticate(String data) async {
    /// call web service to authenticate patient with credentials
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        BasicUserInfos basicUserInfos = basicUserInfosFromJson(response.body);
        if (basicUserInfos.valide == "OK") {
          var jsonResponse = json.decode(response.body);
          // print("valeur de jsonResponse['otpRequired'] : " +
          //     jsonResponse['otpRequired'].toString());
          bool otpRequired = jsonResponse['otpRequired'];
          bool otpForced = jsonResponse['otpForced'];
          //fdp = false;
          String ins = jsonResponse['INS'];

          return {
            'basicUserInfos': basicUserInfos,
            'otpRequired': otpRequired,
            'otpForced': otpForced,
            'ins': ins
          };
        } else {
          throw WebServiceCallException(basicUserInfos.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> checkUserEmailExistance(String data) async {
    /// call web service to check if the email belongs to a Theradom patient
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 2);
      if (response.statusCode == 200) {
        BasicUserInfos2 basicUserInfos2 =
            basicUserInfos2FromJson(response.body);
        if (basicUserInfos2.valide == "OK") {
          return basicUserInfos2;
        } else {
          throw WebServiceCallException(basicUserInfos2.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
