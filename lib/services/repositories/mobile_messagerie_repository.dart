import 'package:theradom/models/GetMessagesResponse.dart';
import 'package:theradom/models/SendMessageResponse.dart';
import 'package:theradom/models/StatusResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class MobileMessagerieRepository {
  late ApiProvider _apiProvider;

  MobileMessagerieRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_Messagerie";

  Future<dynamic> fetchMessages(String data) async {
    /// call web service to fetch user messages
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        GetMessagesResponse getMessagesResponse =
            getMessagesResponseFromJson(response.body);
        if (getMessagesResponse.valide == "OK") {
          return getMessagesResponse;
        } else {
          throw WebServiceCallException(getMessagesResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> sendMessage(String data) async {
    /// call web service to ackowledge message reception
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 2);
      if (response.statusCode == 200) {
        SendMessageResponse sendMessageResponse =
            sendMessageResponseFromJson(response.body);
        if (sendMessageResponse.valide == "OK") {
          return "OK";
        } else {
          throw WebServiceCallException(sendMessageResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> acknowledgeMessage(String data) async {
    /// call web service to ackowledge message reception
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 3);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return "OK";
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> deleteMessage(String data) async {
    /// call web service to remove message from mailbox
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 4);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return "OK";
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
