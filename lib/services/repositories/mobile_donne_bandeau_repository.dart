import 'package:theradom/models/UserAccountInfos.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class MobileDonneBandeauRepository {
  late ApiProvider _apiProvider;

  MobileDonneBandeauRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_donne_bandeau";

  Future<dynamic> fetchUserInfos(String data) async {
    /// call web service to fetch user infos
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 2);
      // print('APPEL WS : ' +
      //     _webServiceName +
      //     ' - ' +
      //     data +
      //     " réponse : " +
      //     response.body);
      if (response.statusCode == 200) {
        UserAccountInfos userAccountInfos =
            userAccountInfosFromJson(response.body);
        return userAccountInfos;
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
