import 'package:theradom/models/GetDrugsResponse.dart';
import 'package:theradom/models/StatusResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;
import 'package:theradom/utils/Helper.dart';

class MobileStockRepository {
  late ApiProvider _apiProvider;

  MobileStockRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_Stock";

  Future<dynamic> fetchDrugsAndConsumables(String data) async {
    /// call web service to fetch drugs and consumables
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        // Helper.prettyPrintJson(response.body);
        GetDrugsResponse getDrugsResponse =
            getDrugsResponseFromJson(response.body);
        if (getDrugsResponse.valide == "OK") {
          return getDrugsResponse;
        } else {
          throw WebServiceCallException(getDrugsResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> pushDrugsAndConsumablesStatus(String data) async {
    /// call web service to push drugs and consumable status to database
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 2);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
