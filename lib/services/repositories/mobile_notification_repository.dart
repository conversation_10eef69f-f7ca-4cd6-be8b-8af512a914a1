import 'package:theradom/models/NotificationResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class MobileNotificationRepository {
  late ApiProvider _apiProvider;

  MobileNotificationRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_Notification";

  Future<dynamic> fetchFakeNotifications(String data) async {
    /// call web service to fetch user fake notifications
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        NotificationResponse notificationResponse =
            notificationResponseFromJson(response.body);
        if (notificationResponse.valide == "OK") {
          return notificationResponse;
        } else {
          throw WebServiceCallException(notificationResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
