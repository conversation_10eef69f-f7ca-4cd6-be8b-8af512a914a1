import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class MobileStatusRepository {
  late ApiProvider _apiProvider;

  MobileStatusRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_checkAccountStatus";

  Future<dynamic> checkUserStatus(String ins, String login, [int? mode]) async {
    Map<String, dynamic> jsonData = {
      "ins": ins,
      "login": login,
    };
    try {
      final http.Response response = await _apiProvider.post(_webServiceName,
          data: jsonEncode(jsonData), option: 1);
      Map<String, dynamic> responseData = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (mode == 1) {
          return responseData["new_password_needed"];
        } else {
          return response.body;
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
