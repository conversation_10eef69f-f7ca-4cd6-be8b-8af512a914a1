import 'package:theradom/models/GetAppointmentsResponse.dart';
import 'package:theradom/models/StatusResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class MobileRdvRepository {
  late ApiProvider _apiProvider;

  MobileRdvRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_RDV";

  Future<dynamic> fetchAppointments(String data) async {
    /// call web service to fetch user appointments
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        GetAppointmentsResponse getAppointmentsResponse =
            getAppointmentsResponseFromJson(response.body);
        if (getAppointmentsResponse.valide == "OK") {
          return getAppointmentsResponse;
        } else {
          throw WebServiceCallException(getAppointmentsResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> acknowledgeAppointment(String data) async {
    /// call web service to acknowledge the reading of an appointment
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 2);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> remindAppointment(String data) async {
    /// call web service to enable the reminder of an appoinment
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 4);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
