import 'package:theradom/models/StatusResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class MobileParametresRepository {
  late ApiProvider _apiProvider;

  MobileParametresRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_parametres";

  Future<dynamic> changeUserPassword(String data) async {
    /// call web service to set user password with new value
    await _post(data: data, option: 1);
  }

  Future<dynamic> changeTermsOfUseAgreement(String data) async {
    /// call web service to set user terms of use aggreement
    await _post(data: data, option: 2);
  }

  Future<dynamic> changeUserFontSize(String data) async {
    /// call web service to set user favorite font size with new value
    await _post(data: data, option: 3);
  }

  Future<dynamic> changeUserEmail(String data) async {
    /// call web service to set user email with new value
    await _post(data: data, option: 4);
  }

  Future<dynamic> changeUserInformationAccount(String data) async {
    /// call web service to set user information account data
    await _post(data: data, option: 5);
  }

  Future<dynamic> _post({required String data, required int option}) async {
    /// call [ApiProvider] post method
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: option);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
