import 'dart:async';
import 'dart:convert';

import 'package:theradom/services/api_provider.dart';
import 'package:theradom/models/StatusResponse.dart';

class MobileResetPasswordRepository {
  late ApiProvider _apiProvider;
  final String _webServiceName = "Mobile_resetPassword";

  bool _isCooldownActive = false;

  MobileResetPasswordRepository() {
    _apiProvider = ApiProvider(); // Initialize _apiProvider here if needed
  }

  Future<dynamic> sendResetPassword(
      String INS, String login, String email) async {
    if (_isCooldownActive) {
      return;
    }

    _isCooldownActive = true;
    Timer(Duration(seconds: 30), () {
      _isCooldownActive = false;
    });

    // --- Partie "intéressante" (appel au webservice) ---
    final Map<String, dynamic> jsonData = {
      "INS": INS,
      "login": login,
      "email": email,
    };

    final response = await _apiProvider.post(
      _webServiceName,
      data: jsonEncode(jsonData),
      option: 1,
    );

    if (response.statusCode == 200) {
      StatusResponse statusResponse = statusResponseFromJson(response.body);
      // if (statusResponse.valide == "OK") {
      //   return statusResponse;
      // } else {
      //   return "NOK";
      // }
      return statusResponse;
    }

    // Le return ci-dessous dépend de ce que vous souhaitez renvoyer en cas de code != 200
    return null;
  }

  Future<dynamic> ResetPassword(
    String ins,
    String login,
    String otp,
    String appToken,
    bool savePwd,
    String password,
  ) async {
    final Map<String, dynamic> jsonData = {
      "appToken": appToken,
      "ins": ins,
      "login": login,
      "OTP": otp,
      "save_pwd": savePwd.toString(),
      "password": password,
    };

    final response = await _apiProvider.post(
      _webServiceName,
      data: jsonEncode(jsonData),
      option: 1,
    );

    if (response.statusCode == 200) {
      StatusResponse statusResponse = statusResponseFromJson(response.body);
      if (statusResponse.valide == "OK") {
        return statusResponse;
      } else {
        return "NOK";
      }
    }
    return null;
  }
}
