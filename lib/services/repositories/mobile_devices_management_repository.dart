import 'package:theradom/services/api_provider.dart';
import 'package:theradom/models/StatusResponse.dart';
import 'dart:convert';

class MobileDevicesManagementRepository {
  late ApiProvider _apiProvider;
  final String _webServiceName = "Mobile_getDevicesList";

  MobileDevicesManagementRepository() {
    _apiProvider = ApiProvider(); // Initialize _apiProvider here if needed
  }

  Future<dynamic> getDevicesList(
      String ins, String login, String appToken) async {
    Map<String, dynamic> jsonData = {
      "appToken": appToken,
      "ins": ins,
      "login": login,
    };

    final response = await _apiProvider.post(_webServiceName,
        data: jsonEncode(jsonData), option: 1);
    if (response.statusCode == 200) {
      StatusResponse statusResponse = statusResponseFromJson(response.body);
      if (statusResponse.valide == "OK") {
        return response.body;
      } else {
        // print("failed to ping Webservice (GetDevicesList).");
        return ("NOK");
      }
    }
  }
}
