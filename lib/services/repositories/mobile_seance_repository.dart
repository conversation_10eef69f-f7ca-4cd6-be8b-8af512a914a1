import 'package:theradom/models/GetPrescriptionResponse.dart';
import 'package:theradom/models/GetPreviousDataResponse.dart';
import 'package:theradom/models/PutSessionDataResponse.dart';
import 'package:theradom/models/SessionDetailsResponse.dart';
import 'package:theradom/models/SessionListResponse.dart';
import 'package:theradom/models/get_previous_data_response_2.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class MobileSeanceRepository {
  late ApiProvider _apiProvider;

  MobileSeanceRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_Seance";

  Future<dynamic> fetchSessionData(String data) async {
    /// call web service to fetch all data from session
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        SessionDetailsResponse sessionDetailsResponse =
            sessionDetailsResponseFromJson(response.body);
        if (sessionDetailsResponse.valide == "OK") {
          return sessionDetailsResponse;
        } else {
          throw WebServiceCallException(sessionDetailsResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> pushSessionData(String data) async {
    /// call web service to push all data from session
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 2);
      if (response.statusCode == 200) {
        PutSessionDataResponse putSessionDataResponse =
            putSessionDataResponseFromJson(response.body);
        if (putSessionDataResponse.valide == "OK") {
          return putSessionDataResponse;
        } else {
          throw WebServiceCallException(putSessionDataResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> fetchMonitoringPrecription(String data) async {
    /// call web service to fetch monitoring prescription
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 3);
      if (response.statusCode == 200) {
        GetPrescriptionResponse getPrescriptionResponse =
            getPrescriptionResponseFromJson(response.body);
        if (getPrescriptionResponse.valide == "OK") {
          return getPrescriptionResponse;
        } else {
          throw WebServiceCallException(getPrescriptionResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> fetchLastSessions(String data) async {
    /// call web service to fetch last 50 sessions
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 4);
      if (response.statusCode == 200) {
        SessionListResponse sessionListResponse =
            sessionListResponseFromJson(response.body);
        if (sessionListResponse.valide == "OK") {
          // print("reponselastsessions");
          return sessionListResponse;
        } else {
          throw WebServiceCallException(sessionListResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> fetchAvailableFields(String data) async {
    /// call web service to fetch available monitoring fields
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 6);
      if (response.statusCode == 200) {
        GetPreviousDataResponse getPreviousDataResponse =
            getPreviousDataResponseSimpleFromJson(response.body);
        if (getPreviousDataResponse.valide == "OK") {
          return getPreviousDataResponse;
        } else {
          throw WebServiceCallException(getPreviousDataResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> fetchFieldValues(String data) async {
    /// call web service to push a biologic analysis file in database
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 6);
      if (response.statusCode == 200) {
        String res = response.body
            .replaceAll("true", "\"1\"")
            .replaceAll("false", "\"0\""); // pour les valeurs booléeenes..
        GetPreviousDataResponse2 getPreviousDataResponse2 =
            getPreviousDataResponseTableFromJson(res);
        if (getPreviousDataResponse2.valide == "OK") {
          return getPreviousDataResponse2;
        } else {
          throw WebServiceCallException(getPreviousDataResponse2.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
