import 'package:theradom/models/BiologicResponse.dart';
import 'package:theradom/models/DocumentsResponse.dart';
import 'package:theradom/models/ReportsResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class MobileDocumentsRepository {
  late ApiProvider _apiProvider;

  MobileDocumentsRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_Documents";

  Future<dynamic> fetchBiologicAnalysis(String data) async {
    /// call web service to fetch biologic analysis data
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        BiologicResponse biologicResponse =
            biologicResponseFromJson(response.body);
        if (biologicResponse.valide == "OK") {
          return biologicResponse;
        } else {
          throw WebServiceCallException(biologicResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> fetchReports(String data, {required int docType}) async {
    /// call web service to fetch monitoring reports
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: docType);
      if (response.statusCode == 200) {
        var reports;
        switch (docType) {
          case 2:
            {
              // documents
              reports = documentsResponseFromJson(response.body);
            }
            break;
          case 3:
            {
              // pdf
              reports = reportsResponseFromJson(response.body);
            }
            break;
          default:
            {
              throw (WebServiceCallException("Invalid docType"));
            }
        }
        if (reports.valide == "OK") {
          return reports;
        } else {
          throw (WebServiceCallException(reports.erreur));
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
