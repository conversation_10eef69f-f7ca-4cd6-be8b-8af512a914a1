import 'dart:convert';
import 'package:theradom/models/StatusResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:http/http.dart' as http;

class SendEmailRepository {
  late ApiProvider _apiProvider;
  final String _from = '<EMAIL>';
  final String _webServiceName = "sendEmail";

  SendEmailRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider(targetId: "3");
  }

  /// Envoi d'un email en passant le message dans le body et
  /// éventuellement un fichier CSV (non encodé en base64).
  Future<dynamic> sendEmail({
    required String to,
    required String subject,
    required String message,
    String fileName = "",
    String fileContent = "", // <-- On passe le CSV en clair
  }) async {
    // Construction des données JSON à envoyer
    Map<String, dynamic> jsonData = {
      "from": _from,
      "to": to,
      "subject": subject,
      "body": message + "\n" + fileContent, // Le message est dans le body
    };

    // S’il y a un CSV à joindre
    if (fileName.isNotEmpty && fileContent.isNotEmpty) {
      jsonData["fileName"] = fileName;
      jsonData["fileContent"] = fileContent;
    }

    try {
      final http.Response response = await _apiProvider.post(
        _webServiceName,
        data: jsonEncode(jsonData),
        option: 0,
      );

      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(response.body);
        }
      } else {
        throw WebServiceCallException(
          'Failed to send email: ${response.body}',
        );
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
