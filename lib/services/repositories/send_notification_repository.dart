import 'dart:convert';

import 'package:theradom/models/StatusResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/utils/firebasenotifications/FirebaseNotificationService.dart';
import 'package:http/http.dart' as http;

class SendNotificationRepository {
  late ApiProvider _apiProvider;

  SendNotificationRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider(targetId: "4");
  }

  final String _webServiceName = "sendNotification";

  Future<dynamic> pushNotification(String title, String content) async {
    /// call web service push notification to device
    try {
      final http.Response response = await _apiProvider.post(_webServiceName,
          data: json.encode({
            "title": title,
            "content": content,
            "img": "",
            "appToken":
                await FirebaseNotificationService.instance.getDeviceToken(),
            "target": "1",
          }),
          option: 0);

      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(response.body);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
