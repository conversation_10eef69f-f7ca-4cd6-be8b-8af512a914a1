import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:theradom/config.dart';
import 'package:theradom/models/CreateAnalysisResponse.dart';
import 'package:theradom/models/GetFilesRequest.dart';
import 'package:theradom/models/GetFilesResponse.dart';
import 'package:theradom/models/StatusResponse.dart';
import 'package:theradom/services/api_provider.dart';
import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/utils/HelperPj.dart';
import 'package:http/http.dart' as http;

class MobilePjRepository {
  late ApiProvider _apiProvider;

  MobilePjRepository({ApiProvider? apiProvider}) {
    _apiProvider = apiProvider ?? ApiProvider();
  }

  final String _webServiceName = "Mobile_PJ";

  Future<dynamic> fetchFilesnamesAndContent(
      String numEnreg, String table, bool isSession) async {
    // retourne les noms et data des pj associées à une séance

    List<String> nameList = [];
    List<File> fileList = [];
    try {
      GetFilesRequest getFilesRequestList = GetFilesRequest(
          numpat: Config.basicUserInfos!.numpat,
          table: table,
          numSeanceMail: isSession ? numEnreg : numEnreg.padLeft(12, "0"),
          liste: "liste",
          pjName: "");
      String getFilesRequestListString =
          getFilesRequestToJson(getFilesRequestList);
      GetFilesResponse getFilesResponse =
          await fetchFileNames(getFilesRequestListString);
      getFilesResponse.pj.forEach((key, pj) {
        nameList.add(pj.name);
      });
      int i = 0;
      while (i < nameList.length) {
        try {
          GetFilesRequest getFilesRequestContent = GetFilesRequest(
              numpat: Config.basicUserInfos!.numpat,
              table: table,
              numSeanceMail: isSession ? numEnreg : numEnreg.padLeft(12, "0"),
              liste: "details",
              pjName: nameList[i]);
          String getFilesRequestContentString =
              getFilesRequestToJson(getFilesRequestContent);
          GetFilesResponse getFilesContentResponse =
              await fetchFileContent(getFilesRequestContentString);
          Uint8List uint8list = base64Decode(getFilesContentResponse.content!);
          String path =
              await HelperPj.saveFileInTempDirectory(uint8list, nameList[i]);
          fileList.add(File(path));
        } on WebServiceCallException catch (e) {
          throw WebServiceCallException(e.toString());
        }
        i++;
      }
    } on WebServiceCallException catch (e) {
      throw WebServiceCallException(e.toString());
    }
    return fileList;
  }

  Future<dynamic> fetchFileNames(String data) async {
    /// call web service to fetch all file names available
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        GetFilesResponse getFilesResponse =
            getFilesResponseFromJson(response.body);
        if (getFilesResponse.valide == "OK") {
          return getFilesResponse;
        } else {
          throw WebServiceCallException(getFilesResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> fetchFileContent(String data) async {
    /// call web service to fetch file base64 encoded content
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 1);
      if (response.statusCode == 200) {
        GetFilesResponse getFilesResponse =
            getFilesResponseFromJson(response.body);
        if (getFilesResponse.valide == "OK") {
          return getFilesResponse;
        } else {
          throw WebServiceCallException(getFilesResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> pushFile(String data, Uint8List fileBytes) async {
    /// call web service to push file in database
    try {
      final http.Response response = await _apiProvider.post(_webServiceName,
          fileBytes: fileBytes, data: data, option: 2);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> deleteFile(String data) async {
    /// call web service to delete file in database
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 3);
      if (response.statusCode == 200) {
        StatusResponse statusResponse = statusResponseFromJson(response.body);
        if (statusResponse.valide == "OK") {
          return statusResponse;
        } else {
          throw WebServiceCallException(statusResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }

  Future<dynamic> pushBiologicAnalysisFile(String data) async {
    /// call web service to push a biologic analysis file in database
    try {
      final http.Response response =
          await _apiProvider.post(_webServiceName, data: data, option: 4);
      if (response.statusCode == 200) {
        CreateAnalysisResponse createAnalysisResponse =
            createAnalysisResponseFromJson(response.body);
        if (createAnalysisResponse.valide == "OK") {
          return createAnalysisResponse;
        } else {
          throw WebServiceCallException(createAnalysisResponse.erreur!);
        }
      }
    } catch (e) {
      throw WebServiceCallException(e.toString());
    }
  }
}
