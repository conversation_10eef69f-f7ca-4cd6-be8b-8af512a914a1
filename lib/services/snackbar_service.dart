import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:theradom/utils/theme/app_theme.dart';

class SnackBarService {
  /// Initialise le service dans le widget racine de l'application
  /// À placer dans le MaterialApp de votre application
  static Widget init({required Widget child}) {
    return Builder(
      builder: (context) => child,
    );
  }

  /// Affiche une snackbar d'erreur
  static void showErrorSnackBar(BuildContext context,
      {required String message}) {
    Flushbar(
      duration: Duration(seconds: 3),
      icon: Icon(Icons.error_outline, color: AppTheme.primaryColor),
      messageText: Text(
        message,
        style: TextStyle(
          color: AppTheme.primaryColor,
          fontFamily: "OpenSans",
          fontSize: 18.0,
        ),
      ),
      isDismissible: true,
      shouldIconPulse: false,
      blockBackgroundInteraction: true,
      flushbarPosition: FlushbarPosition.TOP,
      boxShadows: [
        BoxShadow(
          color: AppTheme.primaryBlurColor,
          spreadRadius: 0,
          blurRadius: 10.0,
        )
      ],
      padding: EdgeInsets.all(10.0),
      margin: EdgeInsets.all(8.0),
      backgroundColor: AppTheme.tertiaryColor,
      animationDuration: Duration(seconds: 1),
      borderRadius: BorderRadius.circular(16.0),
    ).show(context);
  }

  /// Affiche une snackbar de notification de message
  static void showNotifSnackBar(BuildContext context,
      {required String message, required String title}) {
    Flushbar(
      duration: Duration(seconds: 3),
      icon: Icon(FontAwesomeIcons.paperPlane, color: AppTheme.primaryColor),
      titleText: title.isNotEmpty
          ? Text(
              title,
              style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontFamily: "OpenSans",
                  fontSize: 12.0,
                  fontWeight: FontWeight.w600),
            )
          : null,
      messageText: Text(
        message,
        style: TextStyle(
          color: AppTheme.primaryColor,
          fontFamily: "OpenSans",
          fontSize: 18.0,
        ),
      ),
      isDismissible: true,
      shouldIconPulse: false,
      blockBackgroundInteraction: true,
      flushbarPosition: FlushbarPosition.TOP,
      boxShadows: [
        BoxShadow(
          color: AppTheme.primaryBlurColor,
          spreadRadius: 0,
          blurRadius: 10.0,
        )
      ],
      padding: EdgeInsets.all(10.0),
      margin: EdgeInsets.all(8.0),
      backgroundColor: AppTheme.secondaryColor,
      animationDuration: Duration(seconds: 1),
      borderRadius: BorderRadius.circular(4.0),
    ).show(context);
  }

  static void showSuccessSnackBar(BuildContext context,
      {required String message}) {
    Flushbar(
      duration: Duration(seconds: 3),
      icon: Icon(FontAwesomeIcons.circleCheck, color: AppTheme.primaryColor),
      messageText: Text(
        message,
        style: TextStyle(
          color: AppTheme.primaryColor,
          fontFamily: "OpenSans",
          fontSize: 18.0,
        ),
      ),
      isDismissible: true,
      shouldIconPulse: false,
      blockBackgroundInteraction: true,
      flushbarPosition: FlushbarPosition.TOP,
      boxShadows: [
        BoxShadow(
          color: AppTheme.primaryBlurColor,
          spreadRadius: 0,
          blurRadius: 10.0,
        )
      ],
      padding: EdgeInsets.all(10.0),
      margin: EdgeInsets.all(8.0),
      backgroundColor: AppTheme.secondaryColor,
      animationDuration: Duration(seconds: 1),
      borderRadius: BorderRadius.circular(16.0),
    ).show(context);
  }
}
