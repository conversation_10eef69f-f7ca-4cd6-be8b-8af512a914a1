import 'dart:async';
import 'dart:io';

import 'package:theradom/config.dart';

import 'package:theradom/services/web_service_call_exception.dart';
import 'package:theradom/utils/Helper.dart';
import 'package:http/http.dart' as http;

class ApiProvider {
  late http.Client _client;
  late Map<String, String> _baseHeaders;

  ApiProvider({http.Client? client, String targetId = "1"}) {
    _client = client ?? http.Client();
    _initializeHeaders(targetId);
  }

  void _initializeHeaders(String targetId) {
    _baseHeaders = {
      "username": Config.wsUser, // ws login
      "password": Config.wsPassword, // ws password
      "OLD_JSON": "", // flag to indicate the use of old json format,
      "serviceToken": "", // token en cas d'échange avec une base autre que HMD
      "targetID": targetId,
    };
    _updateJwtHeader();
  }

  void _updateJwtHeader() {
    // print(Config.basicUserInfos?.JWT);
    if (Config.basicUserInfos?.JWT != null) {
      _baseHeaders["JWT"] = Config.basicUserInfos!.JWT;
    }
  }

  //final String _authority = Config.nomDomaine.split("://").last;
  //final bool _isSecure = Config.nomDomaine.split("://").first == "https";
  final String _unencodedAPIpath = "/EMA_webServices/index.php";
  final _timeout = Duration(seconds: 60);
  Map<String, String> _baseQueryParameters = {
    "service": "EMA_webServices",
    "method": "appRelay"
  };

  String get _authority => Config.nomDomaine.split("://").length > 1
      ? Config.nomDomaine.split("://")[1] // "**********"
      : Config.nomDomaine; // ou fallback

  bool get _isSecure =>
      Config.nomDomaine.split("://").first.toLowerCase() == "https";

  Uri _createUri(Map<String, String> queryParameters) {
    /// create Uri using http or https constructor
    return _isSecure
        ? Uri.https(_authority, _unencodedAPIpath, queryParameters)
        : Uri.http(_authority, _unencodedAPIpath, queryParameters);
  }

  Future<http.Response> post(
    String webService, {
    List<int> fileBytes = const [],
    required String data,
    required int option,
  }) async {
    _updateJwtHeader();

    /// requête POST

    Map<String, String> headers = Map.from(_baseHeaders);
    headers.addAll({
      HttpHeaders.contentTypeHeader:
          fileBytes.isEmpty ? "application/json" : "application/octet-stream"
    });
    headers["JSON"] = fileBytes.isEmpty ? "" : data;

    Map<String, String> queryParameters = Map.from(_baseQueryParameters);
    queryParameters.addAll({"name": webService, "option": "$option"});

    final Uri uri = _createUri(queryParameters);

    // VDU DEBUG : afficher détails de la requête
    print("POST TO $uri");
    print("DATA = $data");
    print("HEADER = $headers");

    try {
      final http.Response response = await _client
          .post(uri,
              headers: headers, body: fileBytes.isEmpty ? data : fileBytes)
          .timeout(_timeout);

      print("RESPONSE: ${response.statusCode}");
      Helper.prettyPrintJson(response.body);

      return _handleResponse(response);
    } on TimeoutException catch (_) {
      // print("Timeout erreur: ${_.toString()}");
      throw TimeoutReachedException();
    } on SocketException catch (_) {
      // print("Socket erreur: ${_.toString()}");
      throw AppSocketException(_.message);
    } on HandshakeException catch (_) {
      // print("Handshake erreur: ${_.toString()}");
      throw SecureConnectionException(_.message);
    } catch (exception) {
      // print("Generic erreur: ${exception.toString()}");
      // print(exception.toString());
      throw defaultException(exception.toString());
    }
  }

  dynamic _handleResponse(http.Response response) {
    switch (response.statusCode) {
      case 200:
        // print("handle response 200");
        return response;
      case 400:
        // print("handle response 400");
        throw BadRequestException(response.body);
      case 401:
      // print("handle response 401");
      case 403:
        // print("handle response 403");
        throw UnauthorisedException(response.body);
      case 408:
        // print("handle response 408");
        throw TimeoutReachedException();
      case 500:
      // print("handle response 500");
      //ErrorDetails errorDetails = errorDetailsFromJson(response.body);
      //throw WebServiceCallException(errorDetails.message);
      default:
        throw WebServiceCallException(
            "Error occured while communication with server with StatusCode: ${response.statusCode}");
    }
  }
}
