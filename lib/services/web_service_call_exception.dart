import 'package:theradom/utils/Translations.dart';

class WebServiceCallException implements Exception {
  late String _prefix;
  late String _message;

  WebServiceCallException([String message = "", String prefix = ""]) {
    _prefix = prefix;
    _message = message;
    if (message.contains('|')) {
      String errorId = message.split("|").first;
      _message = allTranslations.text('_Error4D_$errorId');
    }
  }

  String toString() {
    return "$_prefix$_message";
  }
}

class BadRequestException extends WebServiceCallException {
  BadRequestException([message]) : super(message, "Mauvaise requête: ");
}

class UnauthorisedException extends WebServiceCallException {
  UnauthorisedException([message]) : super(message, "Non autorisé:");
}

class TimeoutReachedException extends WebServiceCallException {
  TimeoutReachedException() : super("", "Timeout");
}

class AppSocketException extends WebServiceCallException {
  AppSocketException([message]) : super(message, "Erreur de connexion: ");
}

class SecureConnectionException extends WebServiceCallException {
  SecureConnectionException(message)
      : super(message, "Impossible d'établir une connexion réseau sécurisée: ");
}

class defaultException extends WebServiceCallException {
  defaultException(message) : super(message, "Erreur: ");
}
