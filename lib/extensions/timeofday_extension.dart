import 'package:flutter/material.dart';

extension TimeOfDayExtension on TimeOfDay {
  String toLanguageString() {
    // retourne timeOfDay au format hh:mm
    String hh = this.hour.toString().padLeft(2, "0");
    String mm = this.minute.toString().padLeft(2, "0");
    return hh + "h" + mm;
  }

  String toLongLanguageString() {
    // retourne timeOfDay au format hh:mm:ss
    String hh = this.hour.toString().padLeft(2, "0");
    String mm = this.minute.toString().padLeft(2, "0");
    return "$hh:$mm:00";
  }

  bool isMidnight() {
    return this.minute == 0 && this.hour == 0;
  }
}
