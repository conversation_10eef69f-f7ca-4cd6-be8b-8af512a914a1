import 'package:date_format/date_format.dart';
import 'package:theradom/extensions/string_extension.dart';
import 'package:intl/intl.dart';
import 'package:theradom/utils/Translations.dart';

extension DateTimeExtension on DateTime {
  String toLanguageString() {
    String language = allTranslations.currentLanguage;
    switch (language) {
      case "fr":
        {
          return formatDate(this, [dd, "/", mm, "/", yyyy]);
        }
      case "it":
        {
          return formatDate(this, [dd, "/", mm, "/", yyyy]);
        }
      case "en":
        {
          return formatDate(this, [mm, "/", dd, "/", yyyy]);
        }
      default:
        {
          // default to fr
          return formatDate(this, [dd, "/", mm, "/", yyyy]);
        }
    }
  }

  String toLanguageStringShort() {
    String dateYYYYMMDD = this.toYYYYMMDD();
    return dateYYYYMMDD.formatYYYYMMDD2LanguageShort();
  }

  String toYYYYMMDD() {
    String formattedDate = DateFormat('yyyyMMdd').format(this);
    return formattedDate;
  }

  bool isSameDate(DateTime other) {
    return this.year == other.year &&
        this.month == other.month &&
        this.day == other.day;
  }
}
