import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:theradom/utils/Translations.dart';

extension StringExtension on String {
  String formatDecimalNumberInput() {
    // transforme le string par un string cohérent pour 4D
    String correctedValue = this.replaceAll(" ", "").replaceAll(",", ".");
    if (correctedValue[correctedValue.length - 1] == ".") {
      // si termine par une virgule, je la retire
      correctedValue = correctedValue.substring(0, correctedValue.length - 1);
    } else if (correctedValue[0] == ".") {
      // si commence par une virgule, je complète avec 0 devant
      correctedValue = "0" + correctedValue;
    }
    double decimal = double.parse(correctedValue);
    correctedValue = decimal.toString().replaceAll(".", ",");
    return correctedValue;
  }

  String formatIntegerNumberInput() {
    // trasnforme le string en un string cohérent pour 4D
    String correctedValue = this.replaceAll(" ", "");
    int nb = int.parse(correctedValue);
    return nb.toString();
  }

  String formatYYYYMMDD2Language() {
    // le parametre est au format YYYYMMDD
    int year = int.parse(this.substring(0, 4));
    int month = int.parse(this.substring(4, 6));
    int day = int.parse(this.substring(6, 8));
    DateTime date = DateTime(year, month, day);
    String language = allTranslations.currentLanguage;
    switch (language) {
      case "fr":
        {
          return formatDate(date, [dd, "/", mm, "/", yyyy]);
        }
      case "it":
        {
          return formatDate(date, [dd, "/", mm, "/", yyyy]);
        }
      case "en":
        {
          return formatDate(date, [mm, "/", dd, "/", yyyy]);
        }
      default:
        {
          // default to fr
          return formatDate(date, [dd, "/", mm, "/", yyyy]);
        }
    }
  }

  String formatYYYYMMDD2LanguageShort() {
    // le parametre est au format YYYYMMDD

    int year = int.parse(this.substring(0, 4));
    int month = int.parse(this.substring(4, 6));
    int day = int.parse(this.substring(6, 8));
    DateTime date = DateTime(year, month, day);

    String language = allTranslations.currentLanguage;
    switch (language) {
      case "fr":
        {
          return formatDate(date, [dd, "/", mm, "/", yy]);
        }
      case "it":
        {
          return formatDate(date, [dd, "/", mm, "/", yy]);
        }
      case "en":
        {
          return formatDate(date, [mm, "/", dd, "/", yy]);
        }
      default:
        {
          // default to fr
          return formatDate(date, [dd, "/", mm, "/", yy]);
        }
    }
  }

  DateTime yyyymmdd2DateTime() {
    int yyyy = int.parse(this.substring(0, 4));
    int mm = int.parse(this.substring(4, 6));
    int dd = int.parse(this.substring(6, 8));
    return DateTime(yyyy, mm, dd);
  }

  TimeOfDay hhmm2TimeOfDay() {
    List<String> tab = this.split(":");
    int hh = int.parse(tab[0]);
    int mm = int.parse(tab[1]);
    return TimeOfDay(hour: hh, minute: mm);
  }
}
