# Theradom - Application de Suivi Médical

![Theradom Logo](assets/images/logo/theradom.png)

## 📱 Description

**Theradom** est une application mobile Flutter dédiée au suivi médical et à la télémédecine. Elle permet aux patients de suivre leurs séances de soins, consulter leurs statistiques de santé, gérer leurs prescriptions et communiquer avec leurs professionnels de santé.

## ✨ Fonctionnalités Principales

### 🏥 Suivi Médical
- **Gestion des séances** : Création, suivi et historique des séances de soins
- **Monitoring en temps réel** : Suivi des paramètres vitaux et données de santé
- **Sessions programmées** : Planification automatique selon les jours de traitement
- **Séances non terminées** : Reprise des sessions interrompues

### 📊 Statistiques et Visualisation
- **Graphiques interactifs** : Visualisation des données avec Syncfusion Charts et FL Chart
- **Courbes multi-lignes** : Analyse comparative des différents paramètres
- **Historique détaillé** : Évolution des données sur différentes périodes
- **Export des données** : Partage des statistiques avec les professionnels

### 💊 Gestion des Prescriptions
- **Prescriptions numériques** : Consultation et suivi des ordonnances
- **Rappels de traitement** : Notifications pour la prise de médicaments
- **Historique médical** : Archive complète des prescriptions

### 📄 Documents Médicaux
- **Stockage sécurisé** : Gestion des documents PDF et images
- **Partage facilité** : Envoi sécurisé aux professionnels de santé
- **Organisation** : Classement par type et date

### 💬 Communication
- **Messagerie intégrée** : Communication directe avec l'équipe médicale
- **Notifications push** : Alertes importantes via Firebase
- **Téléconsultation** : Accès aux consultations vidéo

### 🔐 Sécurité et Authentification
- **Authentification biométrique** : Connexion par empreinte digitale ou Face ID
- **Chiffrement des données** : Protection des informations médicales sensibles
- **Déconnexion automatique** : Sécurité renforcée avec timer d'inactivité
- **Authentification multi-facteurs (MFA)** : Sécurité supplémentaire

## 🛠️ Technologies Utilisées

### Framework et Langage
- **Flutter** (SDK >=3.1.0) - Framework de développement multiplateforme
- **Dart** - Langage de programmation

### Architecture
- **BLoC Pattern** - Gestion d'état avec flutter_bloc
- **Repository Pattern** - Séparation des couches de données

### Visualisation de Données
- **Syncfusion Charts** - Graphiques professionnels interactifs
- **FL Chart** - Graphiques personnalisables
- **Animations** - Transitions fluides et feedback visuel

### Sécurité
- **Flutter Secure Storage** - Stockage sécurisé des données sensibles
- **Local Auth** - Authentification biométrique
- **Encrypt** - Chiffrement des données

### Communication
- **Firebase Messaging** - Notifications push
- **HTTP** - Communication avec les APIs REST

### Interface Utilisateur
- **Material Design** - Interface moderne et intuitive
- **Localisation** - Support multilingue (FR, EN, IT)
- **Thème personnalisé** - Design cohérent avec la charte graphique

## 📱 Plateformes Supportées

- ✅ **Android** (API 21+)
- ✅ **iOS** (iOS 12+)
- ✅ **Web** (Progressive Web App)
- ✅ **macOS**
- ✅ **Linux**
- ✅ **Windows**

## 🚀 Installation et Configuration

### Prérequis
- Flutter SDK >=3.1.0
- Dart SDK
- Android Studio / Xcode (pour le développement mobile)
- Compte Firebase (pour les notifications)

### Installation
```bash
# Cloner le repository
git clone https://github.com/votre-repo/theradom_app_new.git
cd theradom_app_new

# Installer les dépendances
flutter pub get

# Générer les fichiers de configuration
flutter packages pub run build_runner build

# Lancer l'application
flutter run
```

### Configuration Firebase
1. Créer un projet Firebase
2. Ajouter les fichiers de configuration :
   - `android/app/google-services.json` (Android)
   - `ios/Runner/GoogleService-Info.plist` (iOS)
3. Configurer les notifications push

## 📊 Architecture de l'Application

```
lib/
├── app.dart                 # Point d'entrée principal
├── config.dart             # Configuration globale
├── main.dart               # Fonction main
├── blocs/                  # Logique métier (BLoC)
│   ├── authentication/     # Authentification
│   ├── monitoring/         # Suivi médical
│   ├── statistics/         # Statistiques
│   └── documents/          # Gestion documents
├── models/                 # Modèles de données
├── services/              # Services externes
├── utils/                 # Utilitaires
├── views/                 # Interfaces utilisateur
│   ├── home/              # Écran d'accueil
│   ├── monitoring/        # Suivi médical
│   ├── statistics/        # Statistiques
│   └── login/             # Authentification
└── widgets/               # Composants réutilisables
    ├── charts/            # Graphiques
    └── monitoring/        # Composants de suivi
```

## 🎨 Fonctionnalités Avancées

### Graphiques Interactifs
- **Zoom et panoramique** : Navigation intuitive dans les données
- **Tooltips informatifs** : Détails au survol des points
- **Légendes dynamiques** : Identification claire des séries
- **Animations fluides** : Transitions visuelles attrayantes

### Monitoring Intelligent
- **Détection automatique** : Identification des anomalies
- **Alertes personnalisées** : Notifications basées sur les seuils
- **Synchronisation temps réel** : Mise à jour automatique des données

### Interface Adaptative
- **Design responsive** : Adaptation automatique aux différentes tailles d'écran
- **Mode sombre/clair** : Confort visuel selon les préférences
- **Accessibilité** : Support des technologies d'assistance

## 🔧 Développement

### Structure des BLoCs
```dart
// Exemple de BLoC pour les statistiques
class BlocStatistics extends Bloc<EventStatistics, StateStatistics> {
  // Gestion des événements et états
}
```

### Widgets de Graphiques
```dart
// Widget Syncfusion personnalisé
SyncfusionChartsWidget(
  lineDataList: chartData,
  // Configuration des graphiques
)
```

## 📈 Métriques et Performance

- **Temps de démarrage** : < 2 secondes
- **Fluidité** : 60 FPS constant
- **Taille de l'APK** : Optimisée avec ProGuard
- **Consommation batterie** : Optimisée pour un usage prolongé

## 🌐 Internationalisation

L'application supporte plusieurs langues :
- 🇫🇷 **Français** (par défaut)
- 🇬🇧 **Anglais**
- 🇮🇹 **Italien**

## 🔒 Conformité et Sécurité

- **RGPD** : Conformité avec la réglementation européenne
- **Chiffrement AES-256** : Protection des données sensibles
- **Audit de sécurité** : Tests réguliers de pénétration
- **Certification médicale** : Conformité aux normes de santé

## 📞 Support et Contact

Pour toute question ou support technique :
- 📧 Email : <EMAIL>
- 📱 Téléphone : +33 1 23 45 67 89
- 🌐 Site web : https://www.theradom.com

## 📄 Licence

Ce projet est sous licence propriétaire. Tous droits réservés.

---

**Theradom** - Votre santé, notre priorité 💙
