{"project_info": {"project_number": "670040429496", "firebase_url": "https://hemadom-d4576.firebaseio.com", "project_id": "hemadom-d4576", "storage_bucket": "hemadom-d4576.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:670040429496:android:ac952ee22c479116b6f54d", "android_client_info": {"package_name": "com.hemadialyse.hemadom"}}, "oauth_client": [{"client_id": "670040429496-dt6ek7ed0oojcgac1vdjh568p6s3r5t3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC9yi4C8ybYrdHC9ZlzGALAm6GwWVakGuk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "670040429496-dt6ek7ed0oojcgac1vdjh568p6s3r5t3.apps.googleusercontent.com", "client_type": 3}, {"client_id": "670040429496-8pmfbi448pad2fim8u26pkf59t7kid4d.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "EMA.testFirebase"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:670040429496:android:6b8e9a701a607c91b6f54d", "android_client_info": {"package_name": "com.scand.hemaccess"}}, "oauth_client": [{"client_id": "670040429496-dt6ek7ed0oojcgac1vdjh568p6s3r5t3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC9yi4C8ybYrdHC9ZlzGALAm6GwWVakGuk"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "670040429496-dt6ek7ed0oojcgac1vdjh568p6s3r5t3.apps.googleusercontent.com", "client_type": 3}, {"client_id": "670040429496-8pmfbi448pad2fim8u26pkf59t7kid4d.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "EMA.testFirebase"}}]}}}], "configuration_version": "1"}